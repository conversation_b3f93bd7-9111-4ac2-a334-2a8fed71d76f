declare namespace API {
    type receivedRobotDataListItem = {
        _id: string;
        deviceID: string;
        deviceInfo: any;
        placeInfo: any;
        robotPubilcPlace: string;
        taskNum?: string;
        taskStatus: string;
        createdAt: string;
        updatedAt: string;
        [k: string]: any;
    }

    interface getRobotDataListResult {
        total: number;
        list: receivedRobotDataListItem[]
    }

    declare type robotDataListParams = {
        page?: number;
        pageSize?: number;
        taskNum?: string;
        deviceName?:string;
        robotPubilcPlace?:string;
        result?:string;
        startTime?:string;
        endTime?:string;
    }

    declare type getLatestRobotDataParams = {
        deviceNames: string[];
    }

    interface getLatestRobotDataResult {
        list: receivedRobotDataListItem[]
    }
}