<template>
	<div class="xdj-store-dialog-container">
		<el-dialog :title="dialogParams.title" v-model="dialogParams.isShowDialog" width="769px">
			<el-form ref="StoreDialogFormRef" :rules="rules" :model="formData" label-width="auto">
				<el-row>
					<!-- 门店名称 -->
					<el-col class="mb20 ">
						<el-form-item :label="'门店名称'" prop="storeName">
							<el-input v-model="formData.storeName" :placeholder="'门店名称'" clearable
								class="pr10"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<!-- 门店类型 -->
					<el-col :span="24" class="mb20">
						<el-form-item :label="'门店类型'" prop="storeType">
							<el-select v-model="formData.storeType" :placeholder="'门店类型'" clearable
								:disabled="dialogParams.type == 'update'">
								<el-option label="门店" :value="1"></el-option>
								<el-option v-if="auths(['superAdmin', 'admin'])" label="总店" :value="0"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<!-- 所属总店（如果类型为门店且当前为admin或superadmin则显示，需要手动选择总店；总店账号新增或修改则自动填入不显示） -->
				<el-row v-if="formData.storeType && auths(['superAdmin', 'admin'])">
					<el-col :span="24" class="mb20">
						<el-form-item :label="'总店名称'" prop="parentId">
							<el-select v-model="formData.parentId" :placeholder="'总店名称'" clearable filterable
								class="w100 pr10">
								<el-option v-for="item in props.headList" :key="item._id" :label="item.placeName"
									:value="item._id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(StoreDialogFormRef)">{{ dialogParams.submitTxt }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemUserDialog">
import { reactive, ref, nextTick } from 'vue';
import { getPlaceDetail, addPlace, updatePlaceInfo } from "/@/api/publicPlace"
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
import { auth, auths } from '/@/utils/authFunction';
import { useUserInfo } from '/@/stores/userInfo';
const stores = useUserInfo();
// 总店列表
const props = defineProps<{ headList: receivedStore[] }>()
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 校验规则
const rules = reactive({
	storeName: [{ required: true, message: '请输入门店名称', trigger: 'blur' }],
	storeType: [{ required: true, message: '请选择门店类型', trigger: 'blur' }],
	parentId: [{ required: true, message: '请选择总店名称', trigger: 'blur' }],
})
// 组件ref
const StoreDialogFormRef = ref();
// 表单
const useFormData = () => ref<storeDialogFormType>({
	_id: '',
	storeName: '',
	storeType: 1,  // 1门店  0总店
	parentId: '',
})
const formData = useFormData()
// 对话框配置
const dialogParams = ref<dialogParamsType>({
	isShowDialog: false,
	type: '',
	title: '',
	submitTxt: '',
})

// 打开弹窗
const openDialog = async (type: string, row: receivedStore) => {
	// 初始化数据
	if (type === 'update') {
		const res = await getPlaceDetail({ _id: row._id })
		const { _id, placeName, parentId } = res.data
		const storeType = res.data.parentId.length > 0 ? 1 : 0
		const data = { _id, storeName: placeName, parentId, storeType }
		formData.value = data

		dialogParams.value.type = 'update'
		dialogParams.value.title = '编辑门店';
		dialogParams.value.submitTxt = t('message.common.modify');
	} else {
		Object.assign(formData.value, useFormData().value)

		dialogParams.value.type = 'add'
		dialogParams.value.title = '新增门店';
		dialogParams.value.submitTxt = t('message.common.add');
	}
	dialogParams.value.isShowDialog = true;

};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		StoreDialogFormRef.value.resetFields();
	});
	dialogParams.value.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
	formEl.validate(async (valid) => {
		if (!valid) {
			return
		}
		const params = {
			placeName: formData.value.storeName,
			parentId: auth('headStoreManager') ? stores.userInfos.enterpriseId : formData.value.parentId,
			_type: <placeType>'store'
		}
		if (dialogParams.value.type === 'add') {
			await addPlace(params)
			ElMessage.success(t('message.common.addSuccess'))
		}
		else {
			await updatePlaceInfo({ _id: formData.value._id, ...params })
			ElMessage.success(t('message.common.modifySuccess'))
		}
		closeDialog();
		emit('refresh');
	})
};

defineExpose({
	openDialog,
});
</script>
