<template>
    <div class="system-privateMessage-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-privateMessage-search mb15">
                <div style="display:flex;">
                    <el-input v-model="state.keyWord" :placeholder="$t('message.common.placeholderSearchKeyWord')"
                        style="width: 240px" class="mr10" clearable>
                    </el-input>
                    <el-input v-model="state.sendToPhone" placeholder="接收短信手机号" style="width: 240px" class="mr10" clearable>
                    </el-input>
                    <el-button type="primary" @click="getTableData()">
                        <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }}</el-button>
                </div>

                <div>
                    <el-button type="danger" @click="handleDelete"> <el-icon><ele-Delete /></el-icon>
                        删除</el-button>
                </div>
            </div>
            <el-table ref="tableRef" :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="40" />
                <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
                <el-table-column prop="content" :label="'短信内容'" min-width="300" show-overflow-tooltip></el-table-column>
                <el-table-column prop="sendToPhone" :label="'发送至'" width="140" show-overflow-tooltip></el-table-column>
                <el-table-column prop="createdAt" :label="'短信发送时间'" width="140">
                </el-table-column>
                <el-table-column prop="failureReason" :label="'发送状态'" width="80">
                    <template #default="scope">
                        <el-tag type="danger" v-if="scope.row.failureReason">{{ '失败' }}</el-tag>
                        <el-tag type="success" v-else>{{ '成功' }}</el-tag>
                    </template>
                </el-table-column>
                <!-- <el-table-column :label="$t('message.common.operation')" width="90">
                    <template #default="scope">
                        <el-button size="small" text type="primary" v-show="!scope.row.readTime"
                            @click="onMessageRead(scope.row)">{{ $t('message.views.privateMessageMarkRead') }}</el-button>
                    </template>
                </el-table-column> -->
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
    </div>
</template>

<script setup lang="ts" name="warningTextMessage">
import { reactive, ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage, ElTable } from 'element-plus';
import { getTextMessageList, deleteTextMessages } from "/@/api/message";
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

const tableRef = ref<InstanceType<typeof ElTable>>()

// 定义变量内容
const state = reactive<warningTextMessageState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 查询输入
    keyWord: '',
    sendToPhone: '',
    selectedRows: []
});

// 获取表格数据
const getTableData = async (keyWord = state.keyWord, sendToPhone = state.sendToPhone) => {
    state.tableData.loading = true;
    const res = await getTextMessageList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        keyWord,
        sendToPhone,
    })
    const data = res.data.list
    state.tableData.data = data;
    state.tableData.total = res.data.count;
    state.tableData.loading = false;
};

// 表格多选
const handleSelectionChange = (val: RowWarningTextMessageType[]) => {
    state.selectedRows = val
    // console.log(val);
}

// 删除
const handleDelete = () => {
    if (state.selectedRows.length === 0) {
        return ElMessage.warning('请先选择需要删除的短信')
    }

    ElMessageBox.confirm('确定删除所选短信吗？', t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
    }).then(async () => {
        const ids: string[] = []
        state.selectedRows.forEach(element => {
            // console.log(element._id);
            ids.push(element._id)
        })
        await deleteTextMessages({ ids })
    }).then(() => {
        ElMessage.success('删除成功！')
        getTableData()
    }).catch(() => {
    });
}

// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};

// 页面加载时
onMounted(() => {
    getTableData();
});
</script>

<style scoped lang="scss">
.system-privateMessage-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-privateMessage-search {
    display: flex;
    justify-content: space-between
}
</style>
