<template>
    <div class="system-privateMessage-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-privateMessage-search mb15">
                <div style="display: flex;">
                    <el-input v-model="state.keyWord" :placeholder="$t('message.common.placeholderSearchKeyWord')"
                        style="max-width: 240px" class="mr10" clearable>
                    </el-input>
                    <el-select v-model="state.selectedIsRead" :placeholder="$t('message.views.placeholderMessageStatus')"
                        class="mr10" clearable>
                        <el-option :label="$t('message.common.all')" :value="''"></el-option>
                        <el-option :label="$t('message.views.unread')" :value="'0'"></el-option>
                        <el-option :label="$t('message.views.read')" :value="'1'"></el-option>
                    </el-select>
                    <el-button type="primary" @click="getTableData()">
                        <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }}</el-button>
                </div>

                <div>
                    <el-button type="success" @click="router.push('/warningTextMessage')"> <el-icon><ele-Link /></el-icon>
                        短信记录</el-button>
                </div>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%">
                <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
                <el-table-column prop="title" :label="$t('message.views.privateMessageTitle')"
                    min-width="120"></el-table-column>
                <el-table-column prop="content" :label="$t('message.views.privateMessageContent')" min-width="300"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="createdAt" :label="$t('message.common.createdAt')" width="150">
                </el-table-column>
                <el-table-column prop="status" :label="$t('message.views.privateMessageStatus')" width="90">
                    <template #default="scope">
                        <el-tag type="success" v-if="scope.row.readTime">{{ $t('message.views.read') }}</el-tag>
                        <el-tag type="danger" v-else>{{ $t('message.views.unread') }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('message.common.operation')" width="90">
                    <template #default="scope">
                        <el-button size="small" text type="primary" v-show="!scope.row.readTime"
                            @click="onMessageRead(scope.row)">{{ $t('message.views.privateMessageMarkRead') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
    </div>
</template>

<script setup lang="ts" name="privateMessage">
import { reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getNoticeList, setNoticeRead } from "/@/api/notice";
import { useRouter } from 'vue-router';
const router = useRouter();
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 定义变量内容
const state = reactive<privateMessageState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 查询输入
    keyWord: '',
    selectedIsRead: undefined
});

// 获取表格数据
const getTableData = async (keyWord: string | undefined = state.keyWord) => {
    state.tableData.loading = true;
    const res = await getNoticeList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        keyWord,
        isRead: state.selectedIsRead
    })
    const data = res.data.list
    state.tableData.data = data;
    state.tableData.total = res.data.count;
    state.tableData.loading = false;
};

// 标记已读
const onMessageRead = (row: RowPrivateMessageType) => {
    ElMessageBox.confirm(t('message.views.markReadConfirm', { createdAt: row.createdAt }), t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
    }).then(async () => {
        await setNoticeRead({ _id: row._id })
    }).then(() => {
        ElMessage.success('已读标记成功');
        getTableData();
    }).catch(() => {
    });
}

// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};

// 页面加载时
onMounted(() => {
    getTableData();
});
</script>

<style scoped lang="scss">
.system-privateMessage-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-privateMessage-search {
    display: flex;
    justify-content: space-between;
}
</style>
