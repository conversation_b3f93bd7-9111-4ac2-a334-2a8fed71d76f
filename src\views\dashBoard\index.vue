<template>
    <div class="dashboard-container">
        <el-row id="big-screen-box" class="bg">
            <el-col :span="5.76" :sm="5.76" :md="5.76" :lg="5.76" :xl="5.76" class="index-left ">
                <Left />
            </el-col>
            <el-col :span="12.48" :sm="12.48" :md="12.48" :lg="12.48" :xl="12.48" class="index-center">
                <Center />
            </el-col>
            <el-col :span="5.76" :sm="5.76" :md="5.76" :lg="5.76" :xl="5.76" class="index-right">
                <Right />
            </el-col>
        </el-row>
    </div>
</template>
<script setup lang="ts" name="dashBoard">
import Left from "./components/Left.vue";
import Center from "./components/Center.vue";
import Right from "./components/Right.vue";

import { debounce } from 'lodash'
import mittBus from '/@/utils/mitt';
import { NextLoading } from '/@/utils/loading';

import { onMounted, onBeforeUnmount } from 'vue';

// 全屏
const launchFullscreen = (element: any) => {
    if (element.requestFullscreen) {
        element.requestFullscreen();
    } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
    } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
    } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullScreen();
    }
}
// 将big-screen-box全屏
const go = () => {
    launchFullscreen(document.getElementById('big-screen-box'));
}
const resizeFun = debounce(() => {
    mittBus.emit('resize')
}, 500)

onMounted(() => {

    NextLoading.done()
    go()
    resizeFun()
    window.addEventListener('resize', resizeFun);
    window.addEventListener("fullscreenchange", resizeFun);
})

onBeforeUnmount(() => {
    mittBus.off('resize')
})



</script>
<style scoped lang="scss">
.dashboard-container {
    // @media screen and (max-width: 3840px) {
    //     font-size: px;
    // }

    // @media screen and (max-width: 3440px) {
    //     font-size: px;
    // }

    // @media screen and (max-width: 2560px) {
    //     font-size: 12px;
    // }

    // @media screen and (max-width: 1960px) {
    //     font-size: 10px;
    // }

    // @media screen and (max-width: 1440px) {
    //     font-size: 8px;
    // }

    // @media screen and (max-width: 1024px) {
    //     font-size: 6px;
    // }

    width: 100vw;
    min-height: 100%;
    max-height: 100vh;
    overflow: hidden;

    .bg {
        background: url('../../../src/assets/dashBoard/bg.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        /* 当内容高度大于图片高度时，背景图像的位置相对于viewport固定 */
        background-attachment: fixed;
        display: flex;
        width: 100%;
        height: 100%;
    }


    .index-left {
        width: 24%;
    }

    .index-center {
        width: 52%;
    }

    .index-right {
        width: 24%;
    }
}
</style>