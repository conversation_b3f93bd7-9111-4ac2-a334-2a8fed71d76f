<template>
    <div class="w100 h100 table-container">
        <div class="system-disinfectant-search mb15">
            <el-input v-model="searchParams.keyWord" :placeholder="'产品名称'" style="max-width: 200px" clearable>
            </el-input>
            <!-- 查询按钮 -->
            <el-button type="primary" class="ml10" @click="getTableData()">
                <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }}</el-button>
            <!-- 新增 -->
            <el-button type="success" class="ml10" @click="onAdd()" v-if="auths(['superAdmin', 'storeManager'])">
                <el-icon><ele-FolderAdd /></el-icon>{{ $t('message.common.add') }}</el-button>
        </div>
        <!-- 消毒剂名称	门店名称	创建人	总店审核人	卫监审核人	创建时间	审核状态 -->
        <el-table :data="tableData" v-loading="tableLoading">
            <!-- 序号 -->
            <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="72" />
            <el-table-column prop="name" :label="'消毒剂名称'" min-width="120"></el-table-column>
            <el-table-column prop="enterpriseId" :label="'门店名称'" min-width="120">
                <template #default="scope">
                    {{ storeList.length && storeList.find(ele => ele._id == scope.row.enterpriseId)?.placeName ||
                scope.row.enterpriseId }}
                </template>
            </el-table-column>
            <el-table-column prop="userId" :label="'创建人'" min-width="120">

                <template #default="scope">
                    {{ userList.length && userList.find(ele => ele._id == scope.row.userId)?.name || scope.row.userId }}
                </template>
            </el-table-column>
            <!-- <el-table-column prop="storeName" :label="'总店审核人'"></el-table-column> -->
            <!-- <el-table-column prop="storeName" :label="'卫监审核人'"></el-table-column> -->
            <el-table-column prop="reviewUserId" :label="'审核人'" min-width="120">

                <template #default="scope">
                    {{ userList.length && userList.find(ele => ele._id == scope.row.reviewUserId)?.name ||
                scope.row.reviewUserId }}
                </template>
            </el-table-column>
            <el-table-column prop="createdAt" :label="$t('message.common.createdAt')" min-width="90"></el-table-column>
            <el-table-column prop="reviewStatus" :label="'审核状态'" min-width="90">

                <template #default="scope">
                    <span v-if="scope.row.reviewStatus == 0">草稿</span>
                    <span v-if="scope.row.reviewStatus == 1">提交审核</span>
                    <span v-if="scope.row.reviewStatus == 2">总店审核通过</span>
                    <span v-if="scope.row.reviewStatus == 3">监管审核通过</span>
                    <span v-if="scope.row.reviewStatus == 4">监管审核不通过</span>
                    <span v-if="scope.row.reviewStatus == 5">总店审核不通过</span>
                </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column :label="$t('message.common.operation')"
                :width="auths(['superAdmin', 'admin', 'headStoreManager']) ? 140 : 100">

                <template #default="scope">
                    <!-- 修改/查看 -->
                    <el-button text type="primary" @click="onEdit(scope.row)"
                        v-if="auths(['superAdmin', 'storeManager', 'headStoreManager']) && (scope.row.reviewStatus < 3)">
                        {{ $t('message.common.modify') }}
                    </el-button>
                    <el-button text type="primary" @click="onRead(scope.row)" v-else>查看</el-button>
                    <!-- 审核 -->
                    <el-button text type="primary" @click="onReview(scope.row)"
                        v-if="auths(['superAdmin', 'admin', 'headStoreManager'])"
                        :disabled="auth('headStoreManager') && (scope.row.reviewStatus == 3 || scope.row.reviewStatus == 4)">审核</el-button>
                    <!-- 删除 -->
                    <el-button text type="primary" @click="onDelete(scope.row)">
                        {{ $t('message.common.delete') }}
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
            :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="paginationParams.pageNum" background
            v-model:page-size="paginationParams.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="paginationParams.total">
        </el-pagination>
    </div>
    <reviewDialog ref="reviewDialogRef" @refresh="getTableData()" />
</template>

<script setup lang="ts" name="disinfectantTable">
import { defineAsyncComponent, onMounted, ref, onBeforeUnmount } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getDisinfectantList, deleteDisinfectant } from "/@/api/xdj/inspection";
import { getUserList } from '/@/api/user'
import { getPlaceList } from '/@/api/publicPlace'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
import mittBus from '/@/utils/mitt';
import { auth, auths } from '/@/utils/authFunction';
import { useUserInfo } from '/@/stores/userInfo';
const stores = useUserInfo();
// 审核组件
const reviewDialog = defineAsyncComponent(() => import('/@/views/xdj/inspection/dialog.vue'));
const reviewDialogRef = ref()

// 当前用户角色
const userRole = ref(stores.userInfos.roles[0])
// 查询参数
const searchParams = ref({
    keyWord: '',
    enterpriseId: ''
})
// table
const tableLoading = ref(false)
const tableData = ref([])
// pagination
const paginationParams = ref<paginationType>({
    total: 0,
    pageNum: 1,
    pageSize: 10,
})

// 获取表格数据
const getTableData = async () => {
    tableLoading.value = true;
    const res = await getDisinfectantList({
        page: paginationParams.value.pageNum,
        pageSize: paginationParams.value.pageSize,
        ...searchParams.value
    })
    tableData.value = res.data.list;
    paginationParams.value.total = res.data.count;
    tableLoading.value = false;
};

// 删除报告
const onDelete = (row: { name: string; _id: string; }) => {
    ElMessageBox.confirm(`此操作将永久删除（${row.name}），是否继续？`, t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deleteDisinfectant(row._id)
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};

// 新增报告
const onAdd = () => {
    mittBus.emit('xdjInspectShowForm', { type: 'add' })
};
// 修改报告
const onEdit = (row: xdjForm) => {
    mittBus.emit('xdjInspectShowForm', { type: 'update', id: row._id })
};
// 审核报告
const onReview = (row: xdjForm) => {
    reviewDialogRef.value.openDialog(row, userRole.value)
}
// 查看报告
const onRead = (row: xdjForm) => {
    mittBus.emit('xdjInspectShowForm', { type: 'readonly', id: row._id })
}

// 表格列行号
const indexMethod = (index: number) => {
    return (paginationParams.value.pageNum - 1) * paginationParams.value.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    paginationParams.value.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    paginationParams.value.pageNum = val;
    getTableData();
};

// (用户)门店管理员列表
const userList = ref<receivedUser[]>([])
// 门店列表
const storeList = ref<receivedStore[]>([])
// 获取用户列表
const getUserListData = async () => {
    if (!auth('api.user')) return;
    const res = await getUserList({ roles: ['storeManager', 'headStoreManager', 'admin'] })
    userList.value = res.data.list
}
// 获取门店列表
const getStoreListData = async () => {
    if (!auth('api.store')) return;
    const res = await getPlaceList({ _type: 'store' })
    storeList.value = res.data.list
}

// 页面加载时
onMounted(() => {
    getTableData();
    getUserListData()
    getStoreListData()
    mittBus.on('xdjTableRefresh', getTableData)
});


onBeforeUnmount(() => {
    mittBus.off('xdjTableRefresh')
})

</script>

<style scoped lang="scss">
.table-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;

    :deep(.el-table) {
        flex: 1;
    }

}

.system-disinfectant-search {
    display: flex;
    align-content: space-around;
}
</style>: { name: any; _id: string; }: { _id: any; }: any: { _id: any; }(: any)(: any)(: any)(: any)(: any)(: any)