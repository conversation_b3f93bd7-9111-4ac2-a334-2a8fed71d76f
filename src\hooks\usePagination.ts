import { ref } from 'vue'

export default function (getTableData: Function, param: { pageNum: number; pageSize: number; }) {


    // 表格列行号
    const indexMethod = (index: number) => {
        return (param.pageNum - 1) * param.pageSize + index + 1
    }
    // 分页改变
    const onHandleSizeChange = (val: number) => {
        param.pageSize = val;
        getTableData();
    };
    // 分页改变
    const onHandleCurrentChange = (val: number) => {
        param.pageNum = val;
        getTableData();
    };

    return {
        indexMethod,
        onHandleSizeChange,
        onHandleCurrentChange,
    }
}