<template>
    <div class="system-detection-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-tabs type="card" v-model="activeName" >
                <el-tab-pane label="CO2" name="CO2">
                    <div class="system-detection-search mb15">
                        <el-select v-model="state.deviceType" :placeholder="$t('message.views.placeholderDeviceType')" clearable
                            class="w100 select" style="max-width: 240px" @clear="selectedTypeClear">
                            <el-option v-for="item in deviceTypeList" :key="item.type" :label="item.type"
                                :value="item.type"></el-option>
                        </el-select>
                        <el-input v-model="state.keyWord" :placeholder="$t('message.views.placeholderDeviceNum')" class="mr10"
                            clearable style="max-width: 240px">
                        </el-input>
                        <el-date-picker v-model="timeSelect" type="datetimerange" :shortcuts="shortcuts" range-separator="-"
                            :start-placeholder="$t('message.views.startingTime')" :end-placeholder="$t('message.views.endingTime')"
                            :default-time="defaultTime" :disabledDate="disabledDate" style="max-width: 420px" class="mr10" />
                        <el-button type="primary" @click="getTableData()">
                            <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }} </el-button>
                        <el-button type="success" @click="output()"
                            :loading="state.exportBtnLoading"><el-icon><ele-Download /></el-icon>{{
                                $t('message.common.export') }}</el-button>
                    </div>
                    <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%;height: 57vh;">
                        <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="80" />
                        <el-table-column prop="deviceNum" :label="$t('message.views.deviceNum')" min-width="200"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="dataUnit" min-width="160">
                            <template #header>
                                <div class="table-column-select-header">
                                    <el-select v-model="state.dataUnit">
                                        <el-option :label="$t('message.views.historyData', 1)" value="ppm" />
                                        <el-option :label="$t('message.views.historyData', 2)" value="%" />
                                    </el-select>
                                </div>
                            </template>
                            <template #default="scope">
                                <div class="table-column-select-cell">
                                    <span v-if="state.dataUnit === 'ppm'">{{ scope.row.data }}</span>
                                    <span v-else>{{ scope.row.data / 10000 }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="time" :label="$t('message.views.receiveTime')" min-width="200"
                            show-overflow-tooltip></el-table-column>
                    </el-table>
                    <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                        :pager-count="5" :page-sizes="[10, 20, 50, 100]" v-model:current-page="state.tableData.param.pageNum"
                        background v-model:page-size="state.tableData.param.pageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
                    </el-pagination>
                </el-tab-pane>
                <el-tab-pane label="空气质量巡检" name="Robot">
                    <robothistory></robothistory>
                </el-tab-pane>
            </el-tabs>

        </el-card>
    </div>
</template>

<script setup lang="ts" name="decectedData">
import { ref, reactive, watch, computed, onBeforeMount } from 'vue';
import { ElMessage } from 'element-plus';
import { getDetectionDataList, getDetectionDataAll } from "/@/api/air";
import { getDeviceType } from "/@/api/device/index";
import { downFileByA, getFullUrl } from "/@/utils/common";
import robothistory from '../robothistory/index.vue'
// 国际化
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
const activeName = ref('Robot')
// 导出excel
// import * as XLSX from 'xlsx';

// 设备类型列表
const deviceTypeList = reactive<deviceType[]>([])
// table数据
const state = reactive<AirHistoryState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 搜索框输入
    keyWord: '',
    // 数据单位
    dataUnit: 'ppm',
    deviceType: 'CO2',
    exportBtnLoading: false
})
// 时间选择器
const timeSelect = ref('')
// shortcuts
const shortcuts = reactive([
    // {
    //     text: computed(() => t('message.views.lastMonth')),
    //     value: () => {
    //         const end = new Date()
    //         const start = new Date()
    //         start.setTime(start.getTime() - 1000 * 3600 * 24 * 30)
    //         return [start, end]
    //     },
    // },
    {
        text: computed(() => t('message.views.lastWeek')),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 1000 * 3600 * 24 * 7)
            return [start, end]
        },
    },
    {
        text: computed(() => t('message.views.lastDay')),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 1000 * 3600 * 24)
            return [start, end]
        },
    },
    {
        text: computed(() => t('message.views.lastHour')),
        value: () => {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 1000 * 3600)
            return [start, end]
        },
    },
])
// 默认起始和结束时间 '00:00:00', '23:59:59'
const defaultTime: [Date, Date] = [
    new Date(2000, 1, 1, 0, 0, 0),
    new Date(2000, 2, 1, 23, 59, 59),
]
// 不能选择今天之后的日期
const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
}

// 初始化设备类型列表
const getDeviceTypeList = async () => {
    const res = await getDeviceType()
    Object.assign(deviceTypeList, res.data)
}

// 点击导出
const output = async () => {
    if (!timeSelect.value) {
        return ElMessage.warning('请先选择起止时间！')
    }
    state.exportBtnLoading = true
    const keyWord = state.keyWord
    const startTime = timeSelect.value[0]
    const endTime = timeSelect.value[1]
    const res = await getDetectionDataAll({
        keyWord,
        startTime,
        endTime,
    })
    const filename = res.data.fileName
    const url = getFullUrl(res.data.url)
    downFileByA(filename, url)
    state.exportBtnLoading = false
}

// 获取表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    const keyWord = state.keyWord
    const startTime = timeSelect.value ? timeSelect.value[0] : undefined
    const endTime = timeSelect.value ? timeSelect.value[1] : undefined
    const res = await getDetectionDataList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        keyWord,
        startTime,
        endTime,
    })
    const data = res.data.list
    state.tableData.data = data;
    state.tableData.total = res.data.count;
    state.tableData.loading = false;
};

// 清除所选设备类型
const selectedTypeClear = () => {
    // 清空table数据
    state.tableData.data = []
    state.tableData.total = 0
    state.tableData.loading = false
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
    }
}

// 监听Type变化
watch(() => state.deviceType, (newValue) => {
    if (newValue) {
        getTableData()
    }
}, {
    immediate: true
})

onBeforeMount(() => {
    // getTableData()
    getDeviceTypeList()
})

// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    state.deviceType.length && getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    state.deviceType.length && getTableData();
};

</script>

<style scoped>
.select {
    margin-right: 10px;
}

.system-detection-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-detection-search {
    display: flex;
    align-content: space-around;
}

.table-column-select-header {
    width: 120px;
}

.table-column-select-cell {
    padding-left: 14px;
}
</style>