declare namespace API {
    type receivedPlaceListItem = {
        _id: string;
        status: boolean;
        placeName: string;
        placeType: string;
        region?: regionItemType[]
        address?: string;
        parentId?: string;
        // engineeringHead: personShortInfo;
        // head: personShortInfo;
        createdAt: string;
        updatedAt: string;
        [k: string]: any;
    }

    interface getPlaceListResult {
        count: number;
        list: receivedPlaceListItem[]
    }

    declare type PlaceListParams = {
        page?: number;
        pageSize?: number;
        keyWord?: string;
        _type: 'store' | 'public_place'
        
        startTime?: string;
        endTime?: string;
    }

    /**
     * placeName 场所名称
     * placeType 场所类别
     * region 所在区域（编码）
     * address 详细地址
     * _type 场所类型  store: 消毒剂审核/门店, public_place: 空气质量/公共场所, supervise: 监管单位 , community: 末梢水/社区卫生服务中心
     * status 场所经营状态
     */
    interface AddPlaceParams {
        placeName: string;
        placeType?: string;
        region?: string[];
        address?: string;
        _type: 'store' | 'public_place' | 'supervise' | 'community'
        status?: boolean
        // head: personShortInfo;
        // engineeringHead: personShortInfo;
        [property: string]: any;
    }

    interface updatePlaceParams extends AddPlaceParams {
        _id: string;
    }

    interface updateRobotPlaceParams extends AddRobotPlaceParams {
        _id: string;
    }

    interface AddRobotPlaceParams {
        placeName: string;
        placeAddress?: string;
        sensorData?: any;
        InchargeName?: string;
        InchargePhone:string;
        SupervisionName:string;
        SupervisionPhone:string;
        // head: personShortInfo;
        // engineeringHead: personShortInfo;
        [property: string]: any;
    }
}