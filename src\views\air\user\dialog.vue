<template>
	<div class="system-user-dialog-container">
		<el-dialog :title="dialogParams.title" v-model="dialogParams.isShowDialog" width="769px">
			<el-form ref="StoreDialogFormRef" :rules="rules" :model="formData" label-width="auto">
				<el-row :gutter="35">
					<!-- 登录账号 -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="'登录账号'" prop="userName">
							<el-input v-model="formData.userName" :placeholder="'登录账号'" clearable></el-input>
						</el-form-item>
					</el-col> -->
					<!-- 密码 -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="'密码'" prop="password">
							<el-input type="password" v-model="formData.password" :placeholder="'密码'" clearable
								show-password></el-input>
						</el-form-item>
					</el-col> -->
					<!-- 场所名称 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="'场所名称'" prop="enterpriseId">
							<el-select v-model="formData.enterpriseId" :placeholder="'请选择场所'" clearable filterable
								class="w100">
								<el-option v-for="item in props.storeList" :key="item._id" :label="item.placeName"
									:value="item._id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<!-- 用户姓名 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="'用户姓名'" prop="name">
							<el-input v-model="formData.name" :placeholder="'用户姓名'" clearable></el-input>
						</el-form-item>
					</el-col>
					<!-- 角色 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="角色" prop="role">
							<el-select v-model="formData.role" :placeholder="$t('message.views.placeholderUserRole')"
								clearable class="w100">
								<el-option label="主要负责人" value="placeHead"></el-option>
								<el-option label="工程部负责人" value="engineeringHead"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<!-- 手机号码 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.common.phoneNum')" prop="phoneNum">
							<el-input v-model="formData.phoneNum" :placeholder="$t('message.common.placeholderPhoneNum')"
								clearable></el-input>
						</el-form-item>
					</el-col>
					<!-- 是否启用 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="'是否启用'" prop="active">
							<el-switch v-model="formData.active" :active-value="1" :inactive-value="0" inline-prompt
								:active-text="$t('message.common.able')" :inactive-text="$t('message.common.unable')">
							</el-switch>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(StoreDialogFormRef)">{{ dialogParams.submitTxt }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="xdjUserDialog">
import { reactive, ref, nextTick, computed } from 'vue';
// import { getStoreManagerDetail, createStoreManager, updateStoreManager } from "/@/api/xdj/index";
import { createUsers, updateUsers, getUserInfo } from "/@/api/user/index";
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 校验规则
const rules = reactive({
	userName: [{ required: true, message: computed(() => t('message.views.placeholderUserName')), trigger: 'blur' }],
	name: [{ required: true, message: computed(() => t('message.common.placeholderName')), trigger: 'blur' }],
	role: [{ required: true, message: computed(() => t('message.common.placeholderName')), trigger: 'blur' }],
	enterpriseId: [{ required: true, message: '请选择门店', trigger: 'blur' }],
	phoneNum: [
		{ required: true, message: computed(() => t('message.common.placeholderPhoneNum')), trigger: 'blur' },
		{ pattern: /^1[3456789]\d{9}$/, message: computed(() => t('message.views.rulesUserPhoneNumReg')), trigger: 'blur' }
	]
})
// 组件ref
const StoreDialogFormRef = ref();
// 父组件props
const props = defineProps<{ storeList: StoreListRow[] }>()

// 表单
const useFormData = () => ref<storeManagerDialogFormType>({
	_id: '',
	userName: '',	// 用户名（账号）
	password: '',	// 密码
	name: '',		// 姓名
	phoneNum: '',	// 手机号
	enterpriseId: '',	// 所属门店id
	active: 1,		// 是否启用
	role: ''
})
const formData = useFormData()
// 对话框配置
const dialogParams = ref<dialogParamsType>({
	isShowDialog: false,
	type: '',
	title: '',
	submitTxt: '',
})

// 打开弹窗
const openDialog = async (type: string, row: any) => {
	// 初始化数据
	if (type === 'update') {
		const { _id } = row
		const res = await getUserInfo({ id: _id })
		const { userName, password, name, phoneNum, active, enterpriseId, role } = res.data
		const data = { userName, password, name, phoneNum, active, enterpriseId, role, _id }
		formData.value = data

		dialogParams.value.type = 'update'
		dialogParams.value.title = '编辑用户';
		dialogParams.value.submitTxt = t('message.common.modify');
	} else {
		Object.assign(formData.value, useFormData().value)

		dialogParams.value.type = 'add'
		dialogParams.value.title = '添加用户';
		dialogParams.value.submitTxt = t('message.common.add');
	}
	dialogParams.value.isShowDialog = true;

};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		StoreDialogFormRef.value.resetFields();
	});
	dialogParams.value.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
	formEl.validate(async (valid) => {
		if (!valid) {
			return
		}
		if (dialogParams.value.type === 'add') {
			const { userName, password, name, phoneNum, enterpriseId, active, role } = formData.value
			const data = { userName, password, name, phoneNum, enterpriseId, active, role }
			await createUsers(data)
			ElMessage.success(t('message.common.addSuccess'))
		}
		else {
			await updateUsers(formData.value)
			ElMessage.success(t('message.common.modifySuccess'))
		}
		closeDialog();
		emit('refresh');
	})
};

defineExpose({
	openDialog,
});
</script>
