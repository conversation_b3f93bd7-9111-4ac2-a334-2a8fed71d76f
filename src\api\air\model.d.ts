declare namespace API {

    type DetectionDataParams = {
        deviceNum: string;
        dataType: string;
        startTime?: string;
        endTime?: string;
    }

    type DetectionDataListParams = {
        page?: number;
        pageSize?: number;
        keyWord?: string;
        startTime?: string;
        endTime?: string;
    }

    type DetectionDataAllParams = {
        keyWord?: string;
        startTime: string;
        endTime: string;
    }
}