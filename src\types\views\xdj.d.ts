declare interface xdjFormData {
    _id?: string;
    /**
     * 一 产品名称
     */
    name: string;
    /**
     * 二 是否进口产品 '0': 否, '1': 是
    */
    isImport: '0' | '1';
    /**
     * 三 是否能提供国产产品生产企业卫生许可证或进口产品生产国（地区）允许生产销售的证明文件及报关单 '0': 否, '1': 是  否的话就直接传不能提供原因
    */
    hasProve: string;
    /**
     * 四 生产企业(制造商)名称
     */
    producer: string;
    /**
     * 五 是否为委托加工产品 '0': 否, '1': 是
    */
    isEntrust: string;
    entrustName?: string;
    /**
     * 六 如有生产企业卫生许可证，证上标准的生产类别是
     */
    productCategory?: string;
    /**
     * 七 净含量, 单位是%
    */
    netContent?: number;
    /**
     * 八 是否有标签和说明书  '0': 否, '1': 是
     */
    hasLable: string;
    /**
     * 九 是否有企业标准或质量标准 '0': 否, '1': 是
    */
    hasStandard: string;
    /**
     * 十 是否有检验报告 '0': 否, '1': 是
    */
    hasReport: string;
    /**
     * 十一 是否有配方 '0': 否, '1': 是
    */
    hasFormula: string;
    /**
     * 十二 消毒剂有效成分是， 传ActiveIngredient_id
     */
    activeIngredient: string;
    /**
     * 十三 有效成分含量
     */
    ingredients?: string;
    /**
     * 十四 产品标签和企业标准中有效成分及含量是否一致 '0': 否, '1': 是
     */
    labelsConsistent: string;
    /**
     * 十五 使用范围及其检测项目，只要传检测项目的_id即可
     */
    detectedItems: string[];
    /**
     * 十六 企业标准中显示产品有效期(保质期)为
     */
    effectiveDuration: string;
    /**
     * 十七 标签上是否注明是否标注有疗效   '0': 否, '1': 是
     */
    hasCurativeEffect: string;
    curativeEffect?: string
    /**
     * 十八 标签上是否注明是否标注减轻或缓解症状   '0': 否, '1': 是
    */
    lessenSymptoms: string;
    lessenSymptomsReason?: string;
    /**
     * 十九 标签上是否注明是否标注预防新冠肺炎   '0': 否, '1': 是
    */
    preventionCOVID19: string;
    preventionCOVID19Reason?: string;
    /**
     * 二十 不该用于部位, 可选项为：足部\眼睛\指甲\腋部\头皮\头发\鼻粘膜\肛肠\以上全无
     */
    unusableParts?: string[];
    [property: string]: any;
}

declare interface APIxdjFormParams {
    _id?: string;
    /**
     * 一 产品名称
     */
    name: string;
    /**
     * 二 是否进口产品 '0': 否, '1': 是
    */
    isImport: '0' | '1';
    /**
     * 三 是否能提供国产产品生产企业卫生许可证或进口产品生产国（地区）允许生产销售的证明文件及报关单 '0': 否, '1': 是  否的话就直接传不能提供原因
    */
    hasProve: string;
    /**
     * 四 生产企业(制造商)名称
     */
    producer: string;
    /**
     * 五 是否为委托加工产品 '0': 否, '1': 是
    */
    isEntrust: string;
    entrustName?: string;
    /**
     * 六 如有生产企业卫生许可证，证上标准的生产类别是
     */
    productCategory?: string;
    /**
     * 七 净含量, 单位是%
    */
    netContent?: number;
    /**
     * 八 是否有标签和说明书  '0': 否, '1': 是
     */
    hasLable: string;
    /**
     * 九 是否有企业标准或质量标准 '0': 否, '1': 是
    */
    hasStandard: string;
    /**
     * 十 是否有检验报告 '0': 否, '1': 是
    */
    hasReport: string;
    /**
     * 十一 是否有配方 '0': 否, '1': 是
    */
    hasFormula: string;
    /**
     * 十二 消毒剂有效成分是， 传ActiveIngredient_id
     */
    activeIngredient: string;
    /**
     * 十三 有效成分含量
     */
    ingredients?: string;
    /**
     * 十四 产品标签和企业标准中有效成分及含量是否一致 '0': 否, '1': 是
     */
    labelsConsistent: string;
    /**
     * 十五 使用范围及其检测项目，只要传检测项目的_id即可
     */
    detectedItems: string[];
    /**
     * 十六 企业标准中显示产品有效期(保质期)为
     */
    effectiveDuration: string;
    /**
     * 十七 标签上是否注明是否标注有疗效   '0': 否, '1': 是
     */
    hasCurativeEffect: string;
    curativeEffect?: string
    /**
     * 十八 标签上是否注明是否标注减轻或缓解症状   '0': 否, '1': 是
    */
    lessenSymptoms: string;
    lessenSymptomsReason?: string;
    /**
     * 十九 标签上是否注明是否标注预防新冠肺炎   '0': 否, '1': 是
    */
    preventionCOVID19: string;
    preventionCOVID19Reason?: string;
    /**
     * 二十 不该用于部位, 可选项为：足部\眼睛\指甲\腋部\头皮\头发\鼻粘膜\肛肠\以上全无
     */
    unusableParts?: string[];
    [property: string]: any;
}

declare interface receivedUser {
    _id: string;
    userName: string;
    name: string;
    phoneNum: string;
    role: userRole;
    active: 0 | 1;
    enterpriseId: string;
    enterpriseName: string;
    createdAt: string;
    updatedAt: string;
}

declare interface receivedStore extends Place {
    parentId: string;
}