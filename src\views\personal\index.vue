<template>
	<div class="personal layout-pd">
		<el-row>
			<!-- 个人信息 -->
			<el-col :xs="24" :sm="16" class="personal-info">
				<el-card shadow="hover">
					<template #header>
						<span>个人信息</span>
						<span v-if="!state.showUpdateForm" @click="state.showUpdateForm = true"
							class="personal-info-more">{{ $t('message.common.edit') }}</span>
						<span v-else @click="state.showUpdateForm = false" class="personal-info-more">{{
							$t('message.common.cancel') }}</span>
					</template>
					<div class="personal-user">
						<div class="personal-user-left">
							<img class="h100 w100 personal-user-left-upload"
								:src="state.personalForm.avatar || defaultAvatarUrl">
						</div>
						<div class="personal-user-right">
							<el-row>
								<el-col :span="24" class="personal-title mb18">{{
									(state.personalForm.name || state.personalForm.userName) + '，' +
									currentTime
								}}！
								</el-col>
								<el-col :span="24">
									<el-row>
										<el-col :xs="24" :sm="8" class="personal-item mb6">
											<div class="personal-item-label">{{ $t('message.common.username') }}：</div>
											<div class="personal-item-value">{{ state.personalForm.userName }}</div>
										</el-col>
										<el-col :xs="24" :sm="16" class="personal-item mb6">
											<div class="personal-item-label">身份：</div>
											<div class="personal-item-value" v-if="state.personalForm.role === 'admin'">
												{{ $t('message.common.admin') }}
											</div>
											<div class="personal-item-value" v-else>{{ $t('message.common.user') }}</div>
										</el-col>
									</el-row>
								</el-col>
								<el-col :span="24">
									<el-row>
										<el-col :xs="24" :sm="12" class="personal-item mb6">
											<div class="personal-item-label">{{ $t('message.common.phoneNum') }}：</div>
											<div class="personal-item-value">{{ state.personalForm.phoneNum }}</div>
										</el-col>

									</el-row>
								</el-col>
							</el-row>
						</div>
					</div>

				</el-card>
			</el-col>

			<!-- 消息通知 -->
			<el-col :xs="24" :sm="8" class="pl15 personal-info">
				<el-card shadow="hover">
					<template #header>
						<span>{{ $t('message.router.privateMessage') }}</span>
						<span class="personal-info-more" @click="router.push('/privateMessage')">
							{{ $t('message.common.more') }}</span>
					</template>
					<div class="personal-info-box">
						<ul class="personal-info-ul">
							<li v-for="(v, k) in noticeList" :key="k" class="personal-info-li">
								<a class="personal-info-li-title">{{ v.content }}</a>
							</li>
						</ul>
					</div>
				</el-card>
			</el-col>

			<!-- 更新信息 -->
			<el-col v-if="state.showUpdateForm" :span="24">
				<updateFrom :personalForm="state.personalForm" @refresh="getUserInfomation"></updateFrom>
			</el-col>

		</el-row>
	</div>
</template>

<script setup lang="ts" name="personal">
import { reactive, computed, onBeforeMount } from 'vue';

import { getNoticeList } from "/@/api/notice";
import { getUserInfo } from '/@/api/user/index'
import { formatAxis } from '/@/utils/formatTime';
import { getFullUrl } from "/@/utils/common";
import defaultAvatarUrl from "/@/assets/defaultAvatar.png";
import updateFrom from "./form.vue";

import { useRouter } from 'vue-router';
const router = useRouter();
import { Session } from '/@/utils/storage';
const userInfo = Session.get('userInfo')

// 定义变量内容
const state = reactive<PersonalIndexState>({
	personalForm: {
		name: '',
		userName: '',
		phoneNum: '',
		role: 'user',
		avatar: '',
		active: 1,
	},
	showUpdateForm: false,
});
// 站内消息列表
const noticeList = reactive<RowPrivateMessageType[]>([])

// 根据session中的userInfo._id获取当前登录账号的信息
const getUserInfomation = () => {
	getUserInfo({ id: userInfo._id }).then((res) => {
		// 填入个人信息表单
		state.personalForm.name = res.data.name
		state.personalForm.userName = res.data.userName
		state.personalForm.phoneNum = res.data.phoneNum
		state.personalForm.role = res.data.role
		state.personalForm.avatar = res.data.avatar ? getFullUrl(res.data.avatar) : ''
		state.personalForm.active = res.data.active
	})
}

// 站内消息
const getNoticeListData = async () => {
	const res = await getNoticeList({ page: 1, pageSize: 2 })
	Object.assign(noticeList, res.data.list)
}

// dom 挂载前
onBeforeMount(() => {
	getUserInfomation()
	getNoticeListData()
})

// 当前时间提示语
const currentTime = computed(() => {
	return formatAxis(new Date());
});

</script>

<style  lang="scss" scoped>
@import '../../theme/mixins/index.scss';

.personal {
	.personal-user {
		height: 130px;
		display: flex;
		align-items: center;

		.personal-user-left {
			width: 130px;
			height: 130px;
			border-radius: 3px;

			:deep(.el-upload) {
				height: 100%;
			}

			.personal-user-left-upload {
				img {
					width: 100%;
					height: 100%;
					border-radius: 3px;
				}

				&:hover {
					img {
						animation: logoAnimation 0.3s ease-in-out;
					}
				}
			}
		}

		.personal-user-right {
			flex: 1;
			padding: 0 16px;

			.personal-title {
				font-size: 18px;
				@include text-ellipsis(1);
			}

			.personal-item {
				display: flex;
				align-items: center;
				font-size: 14px;

				.personal-item-label {
					color: var(--el-text-color-secondary);
					@include text-ellipsis(1);
				}

				.personal-item-value {
					@include text-ellipsis(1);
				}
			}
		}
	}

	.personal-info {
		.personal-info-more {
			float: right;
			color: var(--el-text-color-secondary);
			font-size: 14px;

			&:hover {
				color: var(--el-color-primary);
				cursor: pointer;
			}
		}

		.personal-info-box {
			height: 130px;
			overflow: hidden;

			.personal-info-ul {
				list-style: none;

				.personal-info-li {
					font-size: 14px;
					padding-bottom: 12px;

					.personal-info-li-title {
						display: inline-block;
						// @include text-ellipsis(1);
						color: var(--el-text-color-secondary);
						text-decoration: none;
					}

					& a:hover {
						color: var(--el-color-primary);
						cursor: pointer;
					}
				}
			}
		}
	}

}
</style>