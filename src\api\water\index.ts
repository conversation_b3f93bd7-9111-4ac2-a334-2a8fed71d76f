import request from '/@/utils/request';

/**
 * 获取末梢水最新数据
 */
export function getWaterLatest() {
    return request({
        url: '/detected-data/water-new',
        method: 'get',
    });
}


/**
 * @ zbh 站点编号 示例：SHXHYYS10 
 * @ datatime 时间 格式：YYYY-MM-DD
 */
type WaterListParams = {
    zbh: string;
    datatime: string;
}

/**
 * 获取末梢水历史数据列表
 */
export function getWaterHistoryList(query: WaterListParams) {
    return request<waterListResData>({
        url: '/detected-data/water-list',
        method: 'get',
        params: query,
    });
}

/**
 * res.data的类型
 */
type waterListResData = {
    dataType: string[];
    datatime: string;
    zbh: string;
    list: waterListResDataList[];
}

type waterListResDataList = {
    xmid: string;
    data: { [time: string | number]: string; };

}