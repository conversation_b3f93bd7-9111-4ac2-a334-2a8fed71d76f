import request from '/@/utils/request';

type getNoticeListParams = {
    page?: number;
    pageSize?: number;
    keyWord?: string;
    isRead?: string;
}

/**
 * 站内消息 - 获取消息列表
 */
export function getNoticeList(query: getNoticeListParams) {
    return request({
        url: '/notice',
        method: 'get',
        params: query,
    });
}

/**
 * 站内消息 - 获取单个消息的详情
 */
export function getNoticeDetail(query: { _id: string }) {
    return request({
        url: '/notice/' + query._id.toString(),
        method: 'get',
        params: query,
    });
}

/**
 * 站内消息 - 标记已读
 */
export function setNoticeRead(data: { _id: string }) {
    return request({
        url: '/notice',
        method: 'put',
        data,
    });
}