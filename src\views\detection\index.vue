<template>
    <el-card style="max-width: 320px" class="ml10 mr10">
        <div class="card-body">
            <div v-if="state.flag" class="text-body">
                <el-text size="small">设备编号：{{ state.msg.deviceNum }}</el-text>
                <el-text size="small" class="ml24">数据：{{ state.msg.data }}</el-text>
            </div>
            <div v-else>{{ state.desc }}</div>
        </div>
    </el-card>
</template>

<script setup lang="ts" name="socketConn">
import { reactive, onBeforeUnmount } from 'vue';
import { io } from "socket.io-client";

const state = reactive<detectionState>({
    flag: false,
    desc: '设备连接中...',
    // 接收后台 message 事件传来的数据
    msg: {
        deviceNum: '',
        data: undefined,
        time: undefined
    }
})
// 创建连接
// import.meta.env.VITE_API_URL
// 'http://*************:3000'
// 'https://iot-oapi.zyws.cn/'
const socket = io('/', {
    transports: ['websocket'], // 指定传输方式，如WebSocket
    autoConnect: true, // 是否自动连接
    reconnection: true, // 是否自动重新连接
    reconnectionAttempts: 4, // 重新连接尝试次数
    reconnectionDelay: 2000, // 重新连接延迟时间（毫秒）
    // query: { token: 'your-token' }, // 自定义查询参数
});

socket.on("connect", () => {
    state.flag = false
    state.desc = '设备连接中...'
});

// 接收后台返回的数据
socket.on('message', (value: socketMessageResult) => {
    state.flag = true
    state.msg.deviceNum = value.deviceNum
    state.msg.data = value.data
    state.msg.time = value.time
});

socket.on("connect_error", () => {
    state.flag = false
    state.desc = '设备连接发生错误'
});

socket.on("disconnect", () => {
    state.flag = false
    state.desc = '设备连接已断开'
});

// 组件销毁前关闭连接
onBeforeUnmount(() => {
    socket.disconnect()
})
// 返回socket连接接收到的 msg 对象
const getResult = (): detectionState => state
defineExpose({
    getResult,
});
</script>

<style scoped  >
.card-body {
    display: flex;
    font-size: 12px;
    flex: 1;
}

.text-body {
    display: flex;
    justify-content: space-between;

}
</style>