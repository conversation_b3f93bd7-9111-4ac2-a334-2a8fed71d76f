import { defineStore } from 'pinia';

import { getWaterStatistics, getCO2ByYear,getWarningCount } from '/@/api/dashBoard/index'
import { getWarningList } from "/@/api/warning";
import { getRobotAllData } from '/@/api/robotHistory/index';
import { getRobotDeviceList} from "/@/api/robotDevice/index";
import { getRobotPlaceList } from "/@/api/publicPlace/index";
import { getSensorData } from '/@/api/publicPlace/index'
import { getPlaceList } from "/@/api/publicPlace/index";
/**
 * 数据大屏
 * 
 */
export const useDashBoardStore = defineStore('dashBoardStore', {
    state: () => ({
        // 当前元素宽高
        offsetWidth: 0,
        offsetHeight: 0,
        // 大屏标准宽高
        standardWidth: 2080,
        standardHeight: 640,

        // 图表中主要颜色，常态、高亮
        chartColor: ['#18CACA', '#FFD33C'],
        // 字体颜色
        fontColor: ['#FFFFFF', '#18CACA'],

        // 数据年份
        year: new Date(`${new Date().getFullYear()}`),

        /**center */
        // 总体统计数据 左上
        totalCount: {
            place: 0,
            point: 0,
            warning: 0,
            rate: 0,
        },

        // 消毒间报警次数排行 左中
        xdjWarning: {
            yAxisData: [] as any[],
            seriesData: [] as any[]
        },
        // 消毒间监控类型统计 左下
        xdjMonitor: {
            yAxisData: []  as any[],
            seriesData: [] as any[]
        },
        // 预警列表
        warningList: []  as any[],

        /**center-centerMap */
        // 公共场所空气巡航
        airStatistics: []  as any[],
        // 末梢水统计数据
        waterStatistics: []  as any[],
        // 二氧化碳年度数据
        CO2ByYear: []  as any[],

        airParams:{
            Ytype:'time',
            sensorType: 'temperature',
            timeType: 'hour_data',
            deviceName:'',
            robotPubilcPlace:'',
            startTime: '',
            endTime: '',
        },
        // 空气巡航数据右上
        robotData:[] as any[],
        // 场所列表
        RobotPlaceList:[] as any[],
        // 指标列表
        sensorData:[] as any[],
        // 设备列表
        RobotDeviceList:[] as any[],
        // 空气巡航数据地图
        robotDataMap:[] as any[],
        // CO2场所列表
        CO2PlaceList:[] as any[],
    }),
    getters: {
        /**center */
        // 空气巡航报警次数排行 右上
        airWarning: (state) => {
            const yAxisData: string[] = []
            const seriesData: number[] = []
            // 降序排序
            state.airStatistics.toSorted((a:any, b:any) => (b.count - a.count)).forEach((item:any) => {
                yAxisData.push(item.area)
                seriesData.push(item.count)
            });
            console.log(state.airStatistics,'state.airStatistics');
            
            return { yAxisData, seriesData }
        },

        // 末稍水报警次数排行 右中
        waterWarning: (state) => {
            const yAxisData: string[] = []
            const seriesData: number[] = []
            // 降序排序
            state.waterStatistics.toSorted((a:any, b:any) => (b.warningCount - a.warningCount)).forEach((item:any) => {
                yAxisData.push(item.name)
                seriesData.push(item.warningCount)
            });
            return { yAxisData, seriesData }
        },

        // CO₂监测点报警次数排行 右下
        CO2Warning: (state) => {
            const yAxisData: string[] = []
            const seriesData: number[] = []
            state.CO2ByYear.toSorted((a:any, b:any) => (b.count - a.count)).forEach((item:any) => {
                yAxisData.push(item.area)
                seriesData.push(item.count)
            })
            return { yAxisData, seriesData }
        },

        
    },

    actions: {
        async getPlaceLists(){
            const res = await getPlaceList({
                page: 1,
                pageSize: 99999,
                _type: 'public_place',
            })
            this.CO2PlaceList = res.data.list
        },
        // 消毒剂
        getXdjData(year:string) {
            this.xdjWarning.yAxisData = [] as any[]
            this.xdjWarning.seriesData = []
            this.xdjMonitor.yAxisData = []
            this.xdjMonitor.seriesData = []
            if (year === '2024') {
                setTimeout(() => {
                    this.xdjWarning.yAxisData = ['凌云', '湖南', '漕河泾', '长桥', '天平', '华泾', '斜土', '田林']
                    this.xdjWarning.seriesData = [99, 91, 73, 65, 63, 51, 48, 22]
                }, 500);
                setTimeout(() => {
                    this.xdjMonitor.yAxisData = [
                        '未清洗表面残渣、污垢',
                        '未使用洗涤剂洗涤用品用具表面',
                        '未冲洗残留洗涤剂',
                        '杯具未放置入消毒设施',
                        '消毒设施未开启',
                        '杯具物理消毒时长未达到',
                        '物理消毒设施温度未达到',
                        '杯具消毒使用药物不合格',
                        '水池使用不规范',
                        '杯具未完全浸没在消毒剂',
                        '杯具浸没消毒剂时长未达到',
                        '杯具化学消毒后未清洗',
                        '杯具消毒后未采用自然滤干或烘干',
                        '消毒后杯具未放入保洁柜',
                        '清洗消毒保洁设施被拆除',
                        '清洗消毒保洁设施被改造',
                        '消毒间存在无关物品',
                        '清洗消毒池内清洗杯具外物品',
                        '停止使用清洗消毒保洁设施',
                        '杯具转运时存在二次污染风险',
                    ]
                    this.xdjMonitor.seriesData = [95, 88, 85, 82, 80, 73, 72, 67, 62, 49, 40, 38, 37, 28, 22, 21, 18, 16, 7, 3]
                }, 750);
            }
            if (year === '2023') {
                setTimeout(() => {
                    this.xdjWarning.yAxisData = ['凌云', '徐家汇', '康健', '虹梅', '龙华', '枫林']
                    this.xdjWarning.seriesData = [78, 65, 44, 35, 28, 19]
                }, 500);
                setTimeout(() => {
                    this.xdjMonitor.yAxisData = [
                        '未使用洗涤剂洗涤用品用具表面',
                        '未清洗表面残渣、污垢',
                        '杯具消毒使用药物不合格',
                        '杯具未放置入消毒设施',
                        '杯具化学消毒后未清洗',
                        '杯具未完全浸没在消毒剂',
                        '未冲洗残留洗涤剂',
                        '物理消毒设施温度未达到',
                        '杯具转运时存在二次污染风险',
                        '消毒后杯具未放入保洁柜',
                        '杯具浸没消毒剂时长未达到',
                        '杯具消毒后未采用自然滤干或烘干',
                        '杯具物理消毒时长未达到',
                        '水池使用不规范',
                        '清洗消毒保洁设施被改造',
                        '消毒设施未开启',
                        '清洗消毒保洁设施被拆除',
                        '停止使用清洗消毒保洁设施',
                        '消毒间存在无关物品',
                        '清洗消毒池内清洗杯具外物品',
                    ]
                    this.xdjMonitor.seriesData = [96, 90, 86, 81, 77, 72, 69, 64, 55, 49, 42, 39, 36, 27, 21, 14, 9, 7, 2, 0]
                }, 750);
            }

        },

        // 空气巡航和CO2    
        async getAirStatisticsData(year:string) {

            const res = await getWarningCount({year})
            this.totalCount.place=res.data.publicPlaces.length+res.data.robotPlaces.length
            this.CO2ByYear=res.data.publicPlaces.map((item:any)=>{
                this.totalCount.point=1
                this.totalCount.warning+=item.warningCount
                return {
                    area:item.placeName,
                    count:item.warningCount
                }
            })
            this.airStatistics=res.data.robotPlaces.map((item:any)=>{
                if(item.point){
                    this.totalCount.point+=item.point.length
                }
                this.totalCount.warning+=item.warningCount
                return {
                    area:item.placeName,
                    count:item.warningCount
                }
            })
            this.totalCount.rate = Math.round(Math.random() * 100);
            console.log(this.totalCount,'11111222');
            
        },


        // 获取末梢水大屏地图统计数据 （末梢水数据不随年份选择改变，只获取一次，在center组件中调用）
        async getWaterStatisticsData() {
            const res = await getWaterStatistics()

            const waterSiteList: any = []
            for (const key in res.data) {
                if (Object.prototype.hasOwnProperty.call(res.data, key)) {
                    const element = res.data[key];
                    waterSiteList.push({ zbh: key, ...element })
                }
            }

            this.waterStatistics = waterSiteList
        },

        // 获取公共场CO2年度各场所检测数据统计


        // 获取预警列表
        async getWarningListData(year: string) {
            const res = await getWarningList({ year })
            this.warningList = res.data.list
        },
        // 获取预警列表
        async getRobotDataLists() {
            const res = await getRobotAllData({
                robotPubilcPlace:this.airParams.robotPubilcPlace,
                year:this.year
            })

            this.warningList = res.data.list
        },

        // 根据年份场所获取数据
        async getRobotAllDatas(year: string) {
            if(this.airParams.robotPubilcPlace==''){
                return
            }
            const res=await getRobotAllData({
                robotPubilcPlace:this.airParams.robotPubilcPlace,
                year,
            })
            this.robotData=res.data
            const { sensorType, timeType } = this.airParams;
            const processedData = [] as any[];
            const filteredData = this.robotData.map((item: any) => ({
              ...item,
              sensorValue: item.sensorData[sensorType] ?? 0 // 使用空值合并运算符
            })).filter((item) => item.sensorValue !== undefined);
  
          // 2. 分组计算
          if (filteredData.length > 0) {
            const timeGroups = {} as Record<string, any[]>;
            filteredData.forEach((item) => {
              const timeKey = this.getTimeKey(item.time, timeType);
              timeGroups[timeKey] = [...(timeGroups[timeKey] || []), item];
            });
  
            // 计算平均值
            Object.entries(timeGroups).forEach(([key, group]) => {
              const avgValue = Number((group.reduce((sum, item) => sum + item.sensorValue, 0) / group.length).toFixed(2));
              processedData.push({ time: key, value: avgValue });
            });
          }
          this.robotData=processedData
        },
        // 获取所有场所
        async getRobotPlaceLists(){
            const res = await getRobotPlaceList({
                page: 1,
                pageSize: 999999
            })
            const dataList = res.data.list
            const data = dataList
            this.RobotPlaceList = data;
            if(data && this.RobotPlaceList[0]){
                this.airParams.robotPubilcPlace=this.RobotPlaceList[0]._id;
                this.getRobotAllDatas(this.year.getFullYear() + '')
            }
        },
        // 获取指标列表
        async getSensorDatas ()  { 
            const res= await getSensorData()
            // console.log(res,'res');
            if(res.code===0){
                this.sensorData=res.data
                this.airParams.sensorType=this.sensorData[0].EnglishName
            }
        },
        // 获取设备列表
        async getRobotDeviceLists(){ 
            const res = await getRobotDeviceList({
                page: 1,
                pageSize: 99999,
            })
            console.log(res,'res');
            this.RobotDeviceList=res.data.list
            this.airParams.deviceName=this.RobotDeviceList[1].deviceName  
        },
        selectedYearChange() {
            this.totalCount.warning=0 
            this.getPlaceLists()
            this.getAirStatisticsData(this.year.getFullYear() + '')
            this.getXdjData(this.year.getFullYear() + '')
            this.getWarningListData(this.year.getFullYear() + '')
            this.getRobotAllDatas(this.year.getFullYear() + '')
        },
        getTimeKey(timestamp: string, timeType: string): string {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            // const minutes = date.getMinutes().toString().padStart(2, '0');
        
            switch (timeType) {
                case 'minute5_data':
                    return `${year}-${month}-${day} ${hours}:${Math.floor(date.getMinutes() / 5) * 5}`.padStart(2, '0');
                case 'hour_data':
                    return `${year}-${month}-${day} ${hours}:00`;
                case 'day_data':
                    return `${year}-${month}-${day}`;
                case 'month_data':
                    return `${year}-${month}`;
                default:
                    return timestamp;
            }
        }
    },
    
});