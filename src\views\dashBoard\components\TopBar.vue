<template>
  <div class="top-bar">
    <!-- 标题 -->
    <div class="title ">
      <div style="display: flex; align-items: center;">
        <img class="logo" src="../../../../src/assets/dashBoard/logo.svg" />
        <div><span>徐汇区巡航监测“谛听”系统</span></div>
      </div>
    </div>
    <div ref="lottieTopBar" style="width: 100%; height: auto; pointer-events: none;position: relative;"></div>
    <!-- 按钮 -->
    <div class="big-screen-top-bar-btn">
      <!-- 地区 -->
      <div class="worktop" @click="goHome">进入工作台</div>

      <!-- 退出按钮 -->
      <el-button type="primary" size="default" class="exit-btn" @click="goBack"></el-button>
    </div>
  </div>
</template>
<script setup lang="ts" name="TopBar">
import { ref, onMounted } from "vue";
import lottie from 'lottie-web';
import animationData from '/@/assets/lottie/topBar.json'; // 路径需要根据实际情况修改
import { useRouter } from 'vue-router';
const router = useRouter();

const lottieTopBar = ref()
const initLottie = () => {
  lottie.loadAnimation({
    container: lottieTopBar.value, // 在模板中添加一个 ref="lottieContainer" 的容器
    animationData: animationData,
    renderer: 'svg', // 选择渲染器，可以是 'svg'、'canvas'、'html'
    loop: true, // 是否循环播放
    autoplay: true, // 是否自动播放
  });
}

const goHome = () => {
  router.push('/home')
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  initLottie();
})

</script>

<style scoped lang="scss">
.top-bar {
  display: relative;
  height: 10vh;

  @media screen and (max-width: 3840px) {
    font-size: 32px;
  }

  @media screen and (max-width: 3440px) {
    font-size: 24px;
  }

  @media screen and (max-width: 2560px) {
    font-size: 18px;
  }

  @media screen and (max-width: 1960px) {
    font-size: 14px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 9px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 7px;
  }

  .title {
    position: absolute;
    width: 52%;
    background-image: linear-gradient(180deg, #56f4fe 33%, #a3ffcd 85%);
    cursor: pointer;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1em;
    font-family: Source Han Sans CN, Source Han Sans CN-Medium;
    font-weight: Medium;
    text-align: center;
    color: #56f4fe;
    line-height: 1em;
    letter-spacing: 0.08px;
    pointer-events: none;
    display: flex;
    justify-content: space-around;

    .logo {
      display: inline-block;
      height: 2.75vw;
      width: 2.2vw;
      vertical-align: center;
      margin-right: 0.4vw;

      position: relative;
      top: -5%;
    }
  }

  .big-screen-top-bar-btn {
    position: relative;

    .worktop {
      -webkit-appearance: none;
      background-color: transparent;
      background-image: url('../../../../src/assets/images/topBar/top-bar-date-picker-bg.png');
      background-size: auto 100%;
      background-repeat: no-repeat;

      border: none;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN-Medium;
      text-align: left;
      vertical-align: center;
      color: #18caca;
      display: inline-block;
      outline: 0;
      padding: 0 0 0 1em;
      -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

      cursor: pointer;

      font-size: 0.72vw;
      height: 1.56vw;
      line-height: 2vw;
      width: 5vw;
      // height: 40px;
      // width: 100px ;
      // 33  104 

      position: absolute;
      top: -3.2vw;
      // left: 44vw;
      right: 2.4vw;

      scale: 60%;
    }

    .worktop:focus,
    .worktop:hover {
      transform: scale(1.1);
      transition: all 0.5s;
    }

    // 退出按钮
    .el-button--primary {
      background-color: transparent;
      background-image: url('../../../../src/assets/images/topBar/top-bar-exit-btn-bg.svg');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border: none;
      width: 2.45vw;
      height: 1.56vw;
      position: absolute;
      top: -3.2vw;

      scale: 60%;
    }

    .el-button--primary:focus,
    .el-button--primary:hover {
      // background-color: transparent;
      // background-image: url('../../../../src/assets/images/topBar/top-bar-exit-btn-bg.svg');
      // background-repeat: no-repeat;
      // width: 2.45vw;
      // height: 1.56vw;
      // // width: 51px;
      // // height: 33px;
      // border: none;
      //放大动效
      transform: scale(1.1);
      transition: all 0.5s;
    }

  }

  .exit-btn {
    left: auto;
    right: 0.5vw;
    // top: -68px;
    position: absolute;
  }

}


@font-face {
  font-family: Source Han Sans CN-Medium;
  src: url('../../../../src/assets/font/Source Han Sans CN Medium.otf');
}
</style>
