<template>
	<div class="system-device-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="deviceDialogFormRef" :rules="rules" :model="state.dataFome" label-width="auto">
				<el-row :gutter="35">
					<!-- 设备编号 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.deviceNum')" prop="deviceNum">
							<el-input v-model="state.dataFome.deviceNum"
								:placeholder="$t('message.views.placeholderDeviceNum')" clearable>
							</el-input>
						</el-form-item>
					</el-col>
					<!-- 设备编号 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="deviceName" prop="deviceName">
							<el-select v-model="state.dataFome.deviceName" 
								:placeholder="$t('message.views.placeholderdeviceNickName')" clearable class="w100" @change="changeDeviceNum">
								<el-option v-for="item in props.DeviceNumList" :key="item.deviceNum" :label="item.deviceName"
									:value="item.deviceName">
								</el-option>
							</el-select>
							
						</el-form-item>
					</el-col>
					<!-- 设备名称-->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.deviceName')" prop="deviceNickName">
							<el-input v-model="state.dataFome.deviceNickName"
								:placeholder="$t('message.views.placeholderDeviceName')" clearable>
							</el-input>
						</el-form-item>
					</el-col>
					
					<!-- 设备型号-->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.deviceModel')" prop="deviceModel">
							<el-input v-model="state.dataFome.deviceModel"
								:placeholder="$t('message.views.placeholderDeviceModel')" clearable disabled>
							</el-input>
						</el-form-item>
					</el-col>
					<!-- 设备管理员名字-->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.deviceAdminName')" prop="deviceAdminName">
							<el-input v-model="state.dataFome.deviceAdminName"
								:placeholder="$t('message.views.placeholderDeviceAdminName')" clearable>
							</el-input>
						</el-form-item>
					</el-col>
					
					<!-- 设备管理员电话 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.deviceAdminPhone')" prop="deviceAdminPhone">
							<el-input v-model="state.dataFome.deviceAdminPhone"
								:placeholder="$t('message.views.placeholderDeviceAdminPhone')" clearable>
							</el-input>
						</el-form-item>
					</el-col>
					
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(deviceDialogFormRef)">
						{{ state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="deviceDialog">
import { reactive, ref, nextTick, onBeforeMount, computed } from 'vue';
import { createRobotDevice, updateRobotDevice } from "/@/api/robotDevice/index";
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 组件ref
const deviceDialogFormRef = ref();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

const props = defineProps({
  DeviceNumList: {
    type: Array as () => Array<robotDeviceNum>,
    required: true
  }
});
// 定义变量内容
const useState = () => reactive<robordeviceDialogState>({
	dataFome: {
		_id: '',
		deviceName: '',
		deviceNickName:'',
		deviceNum: '',
		deviceModel: '',
		deviceAdminName: '',
		deviceAdminPhone: '',
		status: 0,
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});
const state = useState()


// 校验规则
const rules = reactive({
	deviceNickName: [{ required: true, message: computed(() => t('message.views.placeholderDeviceName')), trigger: 'blur' }],
	deviceNum: [{ required: true, message: computed(() => t('message.views.placeholderDeviceNum')), trigger: 'blur' }],
	deviceName: [{ required: true, message: computed(() => t('message.views.placeholderdeviceNickName')), trigger: 'change' }],
	deviceAdminName: [{ required: true, message: computed(() => t('message.views.placeholderDeviceAdminName')), trigger: 'blur' }],
	deviceAdminPhone: [{ required: true, message: computed(() => t('message.views.placeholderDeviceAdminPhone')), trigger: 'blur' }, { pattern: /^1[3-9]\d{9}$/, message: computed(() => t('message.views.rulesPlacePhoneNum')), trigger: 'blur' }],
})

const changeDeviceNum=()=>{
	// console.log(state.dataFome.deviceNum);
	props.DeviceNumList.forEach((item:any)=>{
		if(item.deviceName===state.dataFome.deviceName){
			state.dataFome.deviceModel=item.deviceModel
		}
	})
}
// 打开弹窗
const openDialog = (type: string, row: rowRobotDeviceType) => {
	// 弹窗数据初始化
	if (type === 'update') {
		// console.log(row);
		state.dataFome = { ...row }

		state.dialog.type = 'update'
		state.dialog.title = t('message.views.deviceDialogUpdateTitle');
		state.dialog.submitTxt = t('message.common.modify');
	} else {
		Object.assign(state.dataFome, useState().dataFome)
		// state.dataFome._id = ''
		// state.dataFome.deviceNum = ''
		// state.dataFome.name = ''
		// state.dataFome.publicPlaceId = ''
		// state.dataFome.position = ''
		// state.dataFome.status = 0
		// state.dataFome.type = ''
		// state.dataFome.unit = ''
		// state.dataFome.threshold = undefined

		state.dialog.type = 'add'
		state.dialog.title = t('message.views.placeholderRobotDevice');
		state.dialog.submitTxt = t('message.common.add');
	}
	state.dialog.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		deviceDialogFormRef.value.resetFields();
	});
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
	formEl.validate(async (valid) => {
		if (!valid) {
			return
		}
		// console.log(state.dataFome,'state.dataFome');
		
		try {
			if (state.dialog.type === 'add') {
				await createRobotDevice(state.dataFome)
				ElMessage.success(t('message.common.addSuccess'))
			}
			else {
				// 如果是修改，参数需要包含_id
				await updateRobotDevice(Object.assign(state.dataFome, { _id: state.dataFome._id }))
				ElMessage.success(t('message.common.modifySuccess'))
			}
		} catch (error) {
			// console.log('@@@@@', error);
			return
			// closeDialog();
		}
		closeDialog();
		emit('refresh');
	})
};



onBeforeMount(() => {
})

// 暴露变量
defineExpose({
	openDialog,
});
</script>
