<template>
	<div class="system-place-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="placeDialogFormRef" :rules="rules" :model="state.dataFome" v-loading="state.loading"
				destroy-on-close label-width="auto">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="deviceName" :label="$t('message.views.deviceNum')">
							<el-select v-model="state.dataFome.deviceName"
								:placeholder="$t('message.views.placeholderchangeDeviceNum')" class="w100" clearable filterable @change="changeDeviceNum">
								<el-option v-for="item in props.DeviceList" :key="item" :label="item.deviceNum " :value="item.deviceName" :disabled="item.hasRunningTask"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="deviceNickName" :label="$t('message.views.deviceName')">
							<el-input v-model="state.dataFome.deviceNickName"
								:placeholder="$t('message.views.placeholderDeviceName')" disabled></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="deviceModel" :label="$t('message.views.deviceModel')">
							<el-input v-model="state.dataFome.deviceModel"
								:placeholder="$t('message.views.placeholderchangeDeviceModel')" disabled></el-input>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="robotPubilcPlace" :label="$t('message.views.placeName')">
							<el-select v-model="state.dataFome.robotPubilcPlace"
								:placeholder="$t('message.views.placeholderchangePlaceName')" class="w100" clearable filterable @change="changePlaceName">
								<el-option v-for="item in props.PlaceList" :key="item" :label="item.placeName"
									:value="item._id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item prop="placeAddress" :label="$t('message.views.placeAddress')">
							<el-input v-model="state.dataFome.placeAddress"
								:placeholder="$t('message.views.placeholderPlaceAddress')" disabled></el-input>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="startTime" :label="$t('message.views.startTime')">
							<el-time-picker value-format="HH:mm" v-model="state.dataFome.startTime" :placeholder="$t('message.views.startTime')"/>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="intervalTime" :label="$t('message.views.intervalTime')" >
							<div style="display: flex;justify-content: space-between;">
								<el-select v-model="state.dataFome.intervalTime.hour" :placeholder="$t('message.views.hour')" >
									<el-option
									v-for="item in 23"
									:key="item"
									:label="item+'h'"
									:value="item"
									/>
								</el-select>
							</div>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item prop="samplePointList">
							<el-button type="primary" @click="addSamplePoint" style="margin-bottom: 15px;">
								{{ $t('message.views.addSamplePoint') }}
							</el-button>
							<el-table :data="state.dataFome.samplePointList" border style="width: 100%" max-height="250">
								<el-table-column prop="index" :label="$t('message.views.serialNumber')" width="80" align="center" />
								<el-table-column :label="$t('message.views.samplePointName')" align="center">
									<template #default="{ row }">
										<el-select v-model="row.pointName" :placeholder="$t('message.views.selectSamplePoint')"  style="width: 100%;">
											<el-option
												v-for="point in samplePointOptions"
												:key="point.value"
												:label="point.label"
												:value="point.value"
											/>
										</el-select>
									</template>
								</el-table-column>
								<el-table-column :label="$t('message.common.operation')" width="120" align="center">
									<template #default="{ $index }">
										<el-button type="danger" @click="removeSamplePoint($index)" size="small">
											{{ $t('message.common.delete') }}
										</el-button>
									</template>
								</el-table-column>
							</el-table>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(placeDialogFormRef)">
						{{ state.dialog.submitTxt }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="placeDialog">
import { reactive, ref, nextTick, onMounted, computed,defineProps } from 'vue';
import { addTask,checkDeviceStatus,getPoi } from '/@/api/task/index'

import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 接收父组件传递的 props
const props = defineProps({
  DeviceList: {
    type: Array as () => Array<rowRobotDeviceType>, // 使用之前定义的 rowRobotDeviceType 类型
    required: true
  },
  PlaceList: {
    type: Array as () => Array<RobotplaceDataFome>, // 根据实际类型调整
    required: true
  }
});
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 组件ref
const placeDialogFormRef = ref();
// 校验规则
const rules = reactive({
	deviceName: [{ required: true, message: computed(() => t('message.views.placeholderchangeDeviceNum')), trigger: 'change' }],
	robotPubilcPlace: [{ required: true, message: computed(() => t('message.views.placeholderchangePlaceName')), trigger: 'change' }],
	startTime: [{ required: true, message: computed(() => t('message.views.placeholderStartTime')), trigger: 'change' }],
	intervalTime: [{
        validator: (rule: any, value: any, callback: any) => {
            if (value.hour === 0 && value.minute === 0) {
                callback(new Error(t('message.views.placeholderiIntervalTime')));
            } else {
                callback();
            }
        },
		required: true,
        trigger: 'change'
    }],
    samplePointList: [{
        validator: (rule: any, value: any, callback: any) => {
            if (value.length === 0) {
                callback(new Error(t('message.views.placeholderiSamplePointList')));
                return;
            }
            const hasEmptyPointName = value.some((point: any) => !point.pointName);
            if (hasEmptyPointName) {
                callback(new Error(t('message.views.placeholderSamplePointName')));
            } else {
                callback();
            }
        },
        required: true,
        trigger: 'change'
    }],
})
const samplePointOptions = ref([])
// 定义变量内容
const useState = () => reactive<taskDialogState>({
	dataFome: {
		_id: '',
		deviceName: '',
		robotPubilcPlace: '',
		taskStatus: '',
		taskNum:'',
		deviceNickName:'',
		deviceModel:'',
		placeAddress:'',
		startTime:'',
		intervalTime:{
			hour:'',
		},
		samplePointList:[] as any[]
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
	loading: false,
	// 负责人关联
	headRelated: true,
});
const state = useState()
// 添加采样点
const addSamplePoint =async () => {
	if(state.dataFome.deviceName==''){
		return ElMessage.error('请先选择设备编号')
	}
	const res=await getPoi(state.dataFome.deviceName)
	// console.log(res,'res');
	if(res.code==0){
		samplePointOptions.value=res.data.map((item:any)=>{
			return {
				label:item,
				value:item
			}
		})
	}
	if(res.data.length==0){
		return ElMessage.error('该设备暂无采样点无法创建任务')
	}
	state.dataFome.samplePointList.push({
		index: state.dataFome.samplePointList.length + 1,
		pointName: '',
	})
}

// 删除采样点
const removeSamplePoint = (index: number) => {
	state.dataFome.samplePointList.splice(index, 1)
	// 重新计算序号
	state.dataFome.samplePointList.forEach((item, idx) => {
		item.index = idx + 1
	})
}
const changeDeviceNum =async () => {
	// console.log(state.dataFome.deviceName,'state.dataFome.deviceName');
	
	const res= await checkDeviceStatus(state.dataFome.deviceName)
	// console.log(res,'res');
	if(res.data.hasRunningTask){
		// 清空表单
		nextTick(() => {
			placeDialogFormRef.value.resetFields();
		});
		return ElMessage.error('设备正在运行任务中，请勿重复添加！')
	}
	// 根据 deviceID 在 DeviceList 中查找对应设备
	const device = props.DeviceList.find(item => item.deviceName === state.dataFome.deviceName);
	if (device) {
		// 赋值 deviceNickName 和 deviceModel
		state.dataFome.deviceNickName = device.deviceNickName;
		state.dataFome.deviceModel = device.deviceModel;
	}
};

const changePlaceName = () => {
  // 根据 placeName 在 PlaceList 中查找对应场所
  const place = props.PlaceList.find(item => item._id === state.dataFome.robotPubilcPlace);
  if (place) {
    // 赋值 placeAddress
    state.dataFome.placeAddress = place.placeAddress;
  }
};
// 页面DOM挂载完后
onMounted(async () => {
})

// 打开弹窗
const openDialog = async () => {
	state.dialog.isShowDialog = true;
	state.loading = true
	state.dialog.type = 'add';
	state.dialog.title = t('message.views.taskAdd');
	state.dialog.submitTxt = t('message.common.saveTask');
	Object.assign(state.dataFome, useState().dataFome)
	state.loading = false
};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		placeDialogFormRef.value.resetFields();
	});
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
    formEl.validate(async (valid) => {
        if (!valid) return;
        const intervalTimeStr = `${state.dataFome.intervalTime.hour}`;
		const samplePointNames = state.dataFome.samplePointList
            .map(point => point.pointName)
            .filter(Boolean) // Remove empty strings
            .join('>');
		const submitData = {
            ...state.dataFome,
            intervalTime: intervalTimeStr,
            samplePointList: samplePointNames,
        };
		// console.log(submitData,'submitData');

        try {
            if (state.dialog.type === 'add') {
                await addTask(submitData); 
                ElMessage.success(t('message.common.addSuccess'));
            }
        } catch (error) {
            return;
        }
        closeDialog();
        emit('refresh');
    });
};


// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style lang="scss">
.inputIcon {
	color: #aaa;

	&:hover {
		color: var(--el-color-primary);
		cursor: pointer;
	}
}
</style>
