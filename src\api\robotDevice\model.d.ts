declare namespace API {

    type RobotDeviceListParams = {
        page?: number;
        pageSize?: number;
        status?: number;
        keyWord?: string;
        deviceModel?: string;
        deviceName?: string ;
    }

    type CreateRobotDeviceParams = {
        deviceNum?: string;
        deviceName?: string;
        status?: number;
        deviceNickName?: string;
        deviceModel?: string;
        deviceAdminName?: string;
        deviceAdminPhone?: string;
    }

    type UpdateRobotDeviceParams = {
        _id: string | undefined;
        deviceNum?: string;
        deviceName?: string;
        status?: number;
        deviceNickName?: string;
        deviceModel?: string;
        deviceAdminName?: string;
        deviceAdminPhone?: string;
    }
}