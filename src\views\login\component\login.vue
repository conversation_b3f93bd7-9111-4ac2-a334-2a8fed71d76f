<template>
    <div class="container loginPage">
        <div class="title"><b>用户登录</b></div>
        <!-- 账号 -->
        <el-form v-if="showAccountLogin" size="large" class="login-content-form">
            <el-form-item>
                <el-input text :placeholder="$t('message.account.accountPlaceholder1')"
                    v-model="accountState.ruleForm.userName" clearable autocomplete="off">
                    <template #prefix>
                        <el-icon class="el-input__icon"><ele-User /></el-icon>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input :type="accountState.isShowPassword ? 'text' : 'password'"
                    :placeholder="$t('message.account.accountPlaceholder2')" v-model="accountState.ruleForm.password"
                    autocomplete="off">

                    <template #prefix>
                        <el-icon class="el-input__icon"><ele-Unlock /></el-icon>
                    </template>

                    <template #suffix>
                        <i class="iconfont el-input__icon login-content-password"
                            :class="accountState.isShowPassword ? 'icon-yincangmima' : 'icon-xianshimima'"
                            @click="accountState.isShowPassword = !accountState.isShowPassword">
                        </i>
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item>
                <el-button @click="onLogin" type="primary" class="login-content-submit" v-waves
                    :loading="accountState.loading.signIn">
                    <span>{{ $t('message.account.accountBtnText') }}</span>
                </el-button>
            </el-form-item>

            <el-form-item>
                <div style="width: 100%;display: flex; justify-content: center;">
                    <a style="color: #00B8FF;text-decoration:none;" class="changeLoginModel"
                        @click="showAccountLogin = false" href="javascript:;">
                        手 机 验 证 码 登 陆
                    </a>
                </div>
            </el-form-item>
        </el-form>
        <!-- 手机号 -->
        <el-form v-else ref="formRef" size="large" class="login-content-form" :model="accountState.ruleForm"
            :rules="rules">
            <el-form-item prop="phoneNum">
                <el-input text :placeholder="$t('message.mobile.placeholder1')" v-model="phoneState.ruleForm.phoneNum"
                    clearable autocomplete="off">

                    <template #prefix>
                        <i class="iconfont icon-dianhua el-input__icon"></i>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item prop="code">
                <el-col :span="15">
                    <el-input text maxlength="6" :placeholder="$t('message.mobile.placeholder2')"
                        v-model="phoneState.ruleForm.code" clearable autocomplete="off">

                        <template #prefix>
                            <el-icon class="el-input__icon"><ele-Position /></el-icon>
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="1"></el-col>
                <el-col :span="8">
                    <el-button @click="getCode(formRef)" type="primary" v-waves class="login-content-code"
                        :loading="phoneState.loading.codeBtn" :disabled="phoneState.getCodeBtnDisable">
                        {{ phoneState.codeBtnText }}
                    </el-button>
                </el-col>
            </el-form-item>
            <el-form-item>
                <el-button @click="phoneLogin" type="primary" v-waves class="login-content-submit"
                    :loading="phoneState.loading.login">
                    <span>{{ $t('message.mobile.btnText') }}</span>
                </el-button>
            </el-form-item>

            <el-form-item>
                <div style="width: 100%;display: flex; justify-content: center;">
                    <a class="changeLoginModel" @click="showAccountLogin = true" href="javascript:;">
                        账 号 密 码 登 陆
                    </a>
                </div>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts" name="login">
import { ref, reactive, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import Cookies from 'js-cookie';

import { initFrontEndControlRoutes } from '/@/router/frontEnd';

import { Session } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';

import { login } from "/@/api/login/index";

const showAccountLogin = ref(true)

const { t } = useI18n();

const route = useRoute();
const router = useRouter();

// 定义变量内容
const accountState = reactive({
    isShowPassword: false,
    ruleForm: {
        userName: '',
        password: '',
        code: undefined,
    },
    loading: {
        signIn: false,
    },
});

// 时间获取
const currentTime = computed(() => {
    return formatAxis(new Date());
});

// 账号登录
const onLogin = async () => {
    // lodaing
    accountState.loading.signIn = true;
    try {
        const res = await login({
            userName: accountState.ruleForm.userName,
            password: accountState.ruleForm.password
        })
        accountState.loading.signIn = false;
        Session.set('token', res.data.token);
        Cookies.set('userName', accountState.ruleForm.userName);
        // router.push('/');
        const isNoPower = await initFrontEndControlRoutes();
        signInSuccess2(isNoPower);
        // 添加 loading，防止第一次进入界面时出现短暂空白
        NextLoading.start();
    } catch (error) {
        accountState.loading.signIn = false;
    }
}

// 登录成功后的跳转
const signInSuccess = (isNoPower: boolean | undefined) => {
    if (isNoPower) {
        ElMessage.warning('抱歉，您没有登录权限');
        Session.clear();
    } else {
        // 初始化登录成功时间问候语
        let currentTimeInfo = currentTime.value;
        // 登录成功，跳到转首页
        // 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
        if (route.query?.redirect) {
            router.push({
                path: <string>route.query?.redirect,
                query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
            });
        } else {
            router.push('/');
        }
        // 登录成功提示
        const signInText = t('message.signInText');
        ElMessage.success(`${currentTimeInfo}，${signInText}`);
        // 添加 loading，防止第一次进入界面时出现短暂空白
        NextLoading.start();
    }
    accountState.loading.signIn = false;
};

import type { FormInstance } from 'element-plus'
import { getCodeByPhone, loginByCode } from "/@/api/login";

const usePhoneState = () => reactive({
    ruleForm: {
        phoneNum: '',
        code: '',
    },
    loading: {
        codeBtn: false,
        login: false,
    },
    codeBtnText: t('message.mobile.codeText'),
    waitTime: 120,
    getCodeBtnDisable: false,
})

const phoneState = usePhoneState()
// 检验规则
const rules = reactive({
    phoneNum: [
        // { required: true, message: computed(() => t('message.common.placeholderPhoneNum')), trigger: 'blur' },
        { pattern: /^1[3456789]\d{9}$/, message: computed(() => t('message.views.rulesUserPhoneNumReg')), trigger: 'blur' }
    ]
})
// 组件ref
const formRef = ref()
// 获取验证码
const getCode = async (formEl: FormInstance) => {
    // 校验手机号
    formEl.validate(async (valid) => {
        if (!valid) {
            return
        }
        phoneState.loading.codeBtn = true
        await getCodeByPhone({ phoneNum: phoneState.ruleForm.phoneNum })
            // new Promise((resolve) => {
            // 	return resolve('success')
            // })
            .then(() => {
                // loading end
                phoneState.loading.codeBtn = false
                ElMessage.success('验证码发送成功！验证码有效期为10分钟')
                phoneState.getCodeBtnDisable = true
                phoneState.codeBtnText = `${phoneState.waitTime}秒后重新发送`
                // 按钮disable倒计时
                let timer = setInterval(() => {
                    if (phoneState.waitTime > 1) {
                        phoneState.waitTime--
                        phoneState.codeBtnText = `${phoneState.waitTime}秒后重新发送`
                    } else {
                        clearInterval(timer)
                        phoneState.codeBtnText = t('message.mobile.codeText')
                        phoneState.getCodeBtnDisable = false
                        phoneState.waitTime = 120
                    }
                }, 1000)
            })
            .catch(() => {
                phoneState.loading.codeBtn = false
                ElMessage.error('验证码发送失败！')
            })
    })
}

const phoneLogin = async () => {
    phoneState.loading.login = true;
    try {
        const res = await loginByCode({ phoneNum: phoneState.ruleForm.phoneNum, code: phoneState.ruleForm.code })
        Session.set('token', res.data.token);
        const isNoPower = await initFrontEndControlRoutes();
        phoneState.loading.login = false;
        signInSuccess(isNoPower);
        // 添加 loading，防止第一次进入界面时出现短暂空白
        NextLoading.start();
    } catch (error) {
        phoneState.loading.login = false;
    }
}

// 登录成功后的跳转
const signInSuccess2 = (isNoPower: boolean | undefined) => {
    if (isNoPower) {
        ElMessage.warning('抱歉，您没有登录权限');
        Session.clear();
    } else {
        // 初始化登录成功时间问候语
        let currentTimeInfo = currentTime.value;
        // 登录成功，跳到转首页
        // 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
        if (route.query?.redirect) {
            router.push({
                path: <string>route.query?.redirect,
                query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
            });
        } else {
            router.push('/');
        }
        // 登录成功提示
        const signInText = t('message.signInText');
        ElMessage.success(`${currentTimeInfo}，${signInText}`);
    }
};
</script>

<style scoped lang="scss">
.container {
    width: 100%;
    height: 100%;

    background-repeat: no-repeat;
    background-size: 100% 100%;

    position: relative;

    display: flex;
    flex-direction: column;
    justify-content: space-between;

    padding: 2vh 4vw 3vh 4vw;
    z-index: 9;

    font-size: 1em;

    .title {
        font-size: 3vh;
        color: #FFFFFF;
        text-shadow: 5px;
        width: 100%;
        height: auto;
        display: flex;
        justify-content: center;
        letter-spacing: 2px;
        text-shadow: 0 0 10px #00D2FF
    }


    .login-content-form {
        margin-top: 20px;
        width: 100%;
        height: calc(100% - 26px);
        padding-top: 2vh;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;

        .login-content-password {
            display: inline-block;
            width: 20px;
            cursor: pointer;

            &:hover {
                color: #909399;
            }
        }

        .login-content-code {
            width: 100%;
            padding: 0;
            // font-weight: bold;
            letter-spacing: 5px;
        }

        .login-content-submit {
            width: 100%;
            letter-spacing: 2px;
            font-weight: 300;
            // margin-top: 15px;
        }

        .changeLoginModel {
            color: #00B8FF;
            text-decoration: none;

            @media screen and (max-width: 3840px) {
                font-size: 32px;
            }

            @media screen and (max-width: 3440px) {
                font-size: 27px;
            }

            @media screen and (max-width: 2560px) {
                font-size: 21px;
            }

            @media screen and (max-width: 1960px) {
                font-size: 16px;
            }

            @media screen and (max-width: 1440px) {
                font-size: 10px;
            }

            @media screen and (max-width: 1024px) {
                font-size: 7px;
            }
        }
    }
}


.loginPage {
    --el-text-color-regular: #FFFFFF;
    --el-text-color-placeholder: #FFFFFF;

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-input) {
        @media screen and (max-width: 3840px) {
            font-size: 32px;
        }

        @media screen and (max-width: 3440px) {
            font-size: 27px;
        }

        @media screen and (max-width: 2560px) {
            font-size: 21px;
        }

        @media screen and (max-width: 1960px) {
            font-size: 16px;
        }

        @media screen and (max-width: 1440px) {
            font-size: 10px;
        }

        @media screen and (max-width: 1024px) {
            font-size: 7px;
        }

    }

    :deep(.is-focus) {
        box-shadow: 0 0 5px #00D2FF, 0 0 0 1px var(--el-input-focus-border-color) inset;
    }

    :deep(.el-input__wrapper) {
        background-color: transparent;
        border-radius: 5px;
    }

    :deep(.el-input__prefix) {
        color: '#FFFFFF';
    }

    :deep(.el-input__inner) {
        color: '#FFFFFF';
        height: 2.7em;
        line-height: 2.7em;

        // 去除自动填入时的浏览器样式
        &:autofill {
            background: transparent; // 支持火狐
        }

        // 支持chrome
        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus,
        &:-webkit-autofill:active {
            transition: background-color 999999s;
            -webkit-text-fill-color: #fff !important;
        }
    }

    :deep(.iconfont) {
        font-size: 1em;
    }

    :deep(.el-input__suffix),
    :deep(.el-input__prefix) {
        height: 2.7em;
    }

    :deep(.el-input__clear) {
        font-size: 1em;
        height: 2.7em;
        line-height: 2.7em;
    }


    :deep(.el-button) {
        @media screen and (max-width: 3840px) {
            font-size: 32px;
        }

        @media screen and (max-width: 3440px) {
            font-size: 27px;
        }

        @media screen and (max-width: 2560px) {
            font-size: 21px;
        }

        @media screen and (max-width: 1960px) {
            font-size: 16px;
        }

        @media screen and (max-width: 1440px) {
            font-size: 10px;
        }

        @media screen and (max-width: 1024px) {
            font-size: 7px;
        }

        height: 2.7em;
    }
}
</style>