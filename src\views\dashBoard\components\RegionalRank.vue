<template>
    <div class="rank-container">
        <miniBorder :title="props.title">
            <div ref="barChartRef" class="barChart"></div>
        </miniBorder>
    </div>
</template>
<script setup lang="ts" name="RegionalRank">
import miniBorder from './miniBorder.vue';
import * as echarts from 'echarts';
import { ref, onMounted, watch, markRaw, nextTick } from "vue";
import mittBus from '/@/utils/mitt';
import { debounce } from 'lodash';

const props = withDefaults(defineProps<{ title: string; yAxisData?: any; seriesData?: any }>(),
    {
        title: '区域排行',
    })

const option = {
    color: '#01C4D3',
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    grid: {
        top: '4%',
        right: '6%',
        bottom: '0',
        left: '0%',
        containLabel: true
        // containLabel: false,
    },
    xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
        // 分隔线
        splitLine: {
            lineStyle: {
                type: 'dashed',
                color: '#D9D9D9',
            },
        },
        // 标签文字颜色
        axisLabel: {
            textStyle: {
                color: '#D9D9D9',
            },
        },
    },

    yAxis: {
        type: 'category',
        data: [],
        axisLabel: {
            textStyle: {
                color: '#D9D9D9',
                fontSize: 6,
                // align: 'left'
            },
            formatter: function (value) {
                var str = "";
                var num = 6; //每行显示字数 
                var valLength = value.length; //该项x轴字数  
                var rowNum = Math.ceil(valLength / num); // 行数  

                if (rowNum > 1) {
                    for (var i = 0; i < rowNum; i++) {
                        var temp = "";
                        var start = i * num;
                        var end = start + num;

                        temp = value.substring(start, end) + "\n";
                        str += temp;
                    }
                    return str;
                } else {
                    return value;
                }
            }
        },
        // 类目轴为索引，从0开始
        // 当展示数据不足4条时，也按照4条数据时的样式展示现有数据（避免自动调整大小柱条宽度过大）
        max: function (value) {
            if (value.max < 3) {
                value.max = 3;
            }
            return value.max;
        },
        // 反向（默认false 从下往上）
        inverse: true,
        // 刻度线
        axisTick: {
            length: 0
        }
    },
    series: [
        {
            type: 'bar',
            data: [],
            barWidth: '40%',
            barMaxWidth: '40%',
        },
    ]
}

const yAxisData = ref()
const seriesData = ref()

const barChartRef = ref()
const chartInstance = ref();

// 定时器
let timer: any = null
// 滚动索引 设置为-1为了让第一次滚动展示0~3而非1~4
const startIndex = ref(-1)

// 绘制(更新)
const draw = () => {
    if (chartInstance.value) {
        chartInstance.value.setOption(option);
        if (timer) { clearInterval(timer) }
        if (seriesData.value.length > 5) {
            // 自动滚动
            timer = setInterval(() => {
                if (startIndex.value + 3 >= seriesData.value.length - 1) {
                    startIndex.value = 0
                    option.yAxis.data = yAxisData.value.slice(startIndex.value, startIndex.value + 4)
                    option.series[0].data = seriesData.value.slice(startIndex.value, startIndex.value + 4)
                } else {
                    startIndex.value++
                    option.yAxis.data = yAxisData.value.slice(startIndex.value, startIndex.value + 4)
                    option.series[0].data = seriesData.value.slice(startIndex.value, startIndex.value + 4)
                }
                draw()
            }, 5000)
        }
    }
};

// 监听props，处理父组件传递的数据（为第一项加上样式）
watch(
    props,
    () => {
        
        const temp = [...props.seriesData]
        temp[0] = {
            value: temp[0],
            itemStyle: {
                color: '#ffd33c'
            }
        }

        seriesData.value = temp
        yAxisData.value = props.yAxisData

        option.yAxis.data = yAxisData.value
        option.series[0].data = seriesData.value

        draw()
    },
    { immediate: true, deep: true }
)

// 初始化
const initBar = () => {

    if (!barChartRef.value) return;
    // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
    chartInstance.value = echarts.getInstanceByDom(barChartRef.value);

    if (!chartInstance.value) {
        chartInstance.value = markRaw(echarts.init(barChartRef.value, undefined, { renderer: "svg" }));
        draw();
    }

}

// resize
const resizeChartfun = debounce(() => {
    const offsetWidth = document.getElementsByClassName('dashboard-container')[0].offsetWidth
    option.yAxis.axisLabel.textStyle.fontSize = offsetWidth / 1000 * 3 > 6 ? offsetWidth / 1000 * 3 : 6
    nextTick(() => {
        chartInstance.value.resize();
        draw()
    });
}, 500)

onMounted(() => {

    const offsetWidth = document.getElementsByClassName('dashboard-container')[0].offsetWidth
    option.yAxis.axisLabel.textStyle.fontSize = offsetWidth / 1000 * 3 > 6 ? offsetWidth / 1000 * 3 : 6
    initBar()
    mittBus.on('resize', resizeChartfun)
})


</script>

<style scoped lang="scss">
@font-face {
    font-family: Source Han Sans CN-Medium;
    src: url('../../../../src/assets/font/Source Han Sans CN Medium.otf');
}

.rank-container {
    width: 11vw;
    height: 30vh;

    display: flex;
    flex-direction: column;
    justify-content: center;

    font-size: 12px;

    @media screen and (max-width: 1960px) {
        font-size: 10px;
    }

    @media screen and (max-width: 1440px) {
        font-size: 8px;
    }

    @media screen and (max-width: 1024px) {
        font-size: 6px;
    }

    .barChart {
        width: 100%;
        height: 100%;
        padding-bottom: 1.5em;
    }
}
</style>
