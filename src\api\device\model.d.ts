declare namespace API {

    type DeviceListParams = {
        page?: number;
        pageSize?: number;
        active?: number;
        keyWord?: string;
        publicPlaceId?: string;
    }

    type CreateDeviceParams = {
        deviceNum: string; //设备唯一编码
        name: string;
        status?: number;
        type?: string;
        unit?: string
    }

    type UpdateDeviceParams = {
        _id: string;
        deviceNum?: string;
        name?: string;
        status?: number;
        type?: string;
        unit?: string
    }
}