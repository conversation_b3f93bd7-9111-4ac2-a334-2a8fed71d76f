import request from '/@/utils/request';
import { auth } from '/@/utils/authFunction';
import { ElMessage } from 'element-plus';
const authRequest = (option: any, val: string) => {
    if (auth(val)) {
        return request(option)
    }
    else ElMessage.error('账号无权限')
}

// 获取用户列表
export function getUserList(query: API.UserListParams) {
    return request({
        url: '/user',
        method: 'get',
        params: query,
    });
}

// 根据id获取某个用户信息
export function getUserInfo(query: { id: string }) {
    return request({
        url: '/user/' + query.id.toString(),
        method: 'get'
    });
}

// 添加用户 
export function createUsers(data: API.CreateUserParams) {
    return request({
        url: '/user',
        method: 'post',
        data,
    });
}

// 更新
export function updateUsers(data: API.UpdateUserParams) {
    return request({
        url: '/user',
        method: 'put',
        data,
        headers: {
            'Content-Type': 'multipart/form-data;'
        }
    });
}

// 删除用户
export function deleteUsers(query: { id: string }) {
    return request({
        url: '/user/' + query.id.toString(),
        method: 'delete',
    });
}