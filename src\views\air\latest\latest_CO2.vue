<template>
	<div class="home-container layout-pd">
		<!-- socket实时数据 -->
		<el-row :gutter="15" class="home-card-one mb15">
			<el-col class="home-media-sm">
				<div class="home-card-item flex">
					<div class="flex-margin flex w100">
						<div class="flex-auto" v-if="state.detectedData.flag">
							<span class="font30">设备：{{ state.detectedData.msg.deviceNum }}</span>
							<span class="ml24 font24" :style="{ color: state.detectedStyle.color1 }">实时数据：{{
			state.detectedData.msg.data }}</span>
							<div class="mt10">时间：{{ socketParsedTime }}</div>
						</div>
						<div class="flex-auto" v-else>
							<span class="font30">{{ state.detectedData.desc }}</span>
						</div>
						<div class="home-card-item-icon flex"
							:style="{ background: `var(${state.detectedStyle.color2})` }">
							<i class="flex-margin font32" :class="state.detectedStyle.icon"
								:style="{ color: `var(${state.detectedStyle.color3})` }"></i>
						</div>
					</div>
				</div>
			</el-col>
		</el-row>
		<!-- 折线图 -->
		<el-row :gutter="15" class="home-card-two mb15">
			<el-col>
				<div class="home-card-item">
					<div class="flex-warp">
						<div class="flex-warp-item  mr10">
							<div class="flex-warp-item-box">
								{{ $t('message.views.timeSpan') }}：
								<el-select v-model="timeType" @change="changeTimeType(timeType)" placeholder="请选择"
									style="width: 80px;">
									<el-option :label="$t('message.views.month')" value="month_data" />
									<el-option :label="$t('message.views.day')" value="day_data" />
									<el-option :label="$t('message.views.hour')" value="hour_data" />
									<el-option :label="$t('message.views.minute', { n: 5 })" value="minute5_data" />
									<el-option :label="$t('message.views.minute')" value="minute_data" />
								</el-select>
							</div>
						</div>
						<div class="flex-warp-item">
							<div class="flex-warp-item-box">
								{{ $t('message.views.startingAndEndingTime') }}：
								<el-date-picker v-model="timeSelect" type="datetimerange" :shortcuts="state.shortcuts"
									range-separator="-" :start-placeholder="$t('message.views.startingTime')"
									:end-placeholder="$t('message.views.endingTime')" :default-time="defaultTime"
									:disabledDate="disabledDate" />
							</div>
						</div>
					</div>
					<div style="height: 100%" ref="homeLineRef"></div>
				</div>
			</el-col>
		</el-row>

		<el-row v-show="false">
			<el-col>
				<socketConn ref="socketConnRef"></socketConn>
			</el-col>
		</el-row>

	</div>
</template>

<script setup lang="ts" name="disinfectant">
import socketConn from "/@/views/detection/index.vue";
import { reactive, onMounted, ref, watch, nextTick, onActivated, markRaw, computed } from 'vue';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus'
import { useThemeConfig } from '/@/stores/themeConfig';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';

import { getDetectionData } from "/@/api/air/index"
import { getDeviceList } from "/@/api/device/index"

import { debounce } from 'lodash';
import { timestampToTime } from '/@/utils/toolsValidate';

import { useI18n } from 'vue-i18n';
const { t } = useI18n()

// 组件ref
const socketConnRef = ref()
const homeLineRef = ref();
// store
const storesTagsViewRoutes = useTagsViewRoutes();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);

// 时间选择器
const timeSelect = ref('')
// 默认起始和结束时间 '00:00:00', '23:59:59'
const defaultTime: [Date, Date] = [
	new Date(2000, 1, 1, 0, 0, 0),
	new Date(2000, 2, 1, 23, 59, 59),
]
// 不能选择今天之后的日期
const disabledDate = (time: Date) => {
	return time.getTime() > Date.now()
}

// 时间跨度
const timeType = ref('hour_data')
// 解析后用来发送请求的时间参数
const parsedTime = reactive({
	startTime: '',
	endTime: ''
})
// 配置数据
const state = reactive({
	// socket实时数据（最新数据）
	detectedData: {} as detectionState,
	global: {
		homeChartOne: null,
		dispose: [null, '', undefined],
	} as any,

	detectedStyle: {
		title: '实时二氧化碳检测',
		icon: 'fa fa-meetup',
		color1: '#6690F9',
		color2: '--next-color-primary-lighter',
		color3: '--el-color-primary',
	},
	myCharts: [] as EmptyArrayType,
	charts: {
		theme: '',
		bgColor: '',
		color: '#303133',
	},
	shortcuts: [
		{
			text: computed(() => t('message.views.lastMonth')),
			value: () => {
				const end = new Date()
				const start = new Date()
				start.setTime(start.getTime() - 1000 * 3600 * 24 * 30)
				return [start, end]
			},
		},
		{
			text: computed(() => t('message.views.lastWeek')),
			value: () => {
				const end = new Date()
				const start = new Date()
				start.setTime(start.getTime() - 1000 * 3600 * 24 * 7)
				return [start, end]
			},
		},

		{
			text: computed(() => t('message.views.lastDay')),
			value: () => {
				const end = new Date()
				const start = new Date()
				start.setTime(start.getTime() - 1000 * 3600 * 24)
				return [start, end]
			},
		},
		{
			text: computed(() => t('message.views.lastHour')),
			value: () => {
				const end = new Date()
				const start = new Date()
				start.setTime(start.getTime() - 1000 * 3600)
				return [start, end]
			},
		},
	]
});
// 折线图配置项
const lineChartOption = reactive({
	backgroundColor: state.charts.bgColor,
	title: {
		text: '二氧化碳浓度检测',
		x: 'left',
		textStyle: { fontSize: '15', color: state.charts.color },
	},
	grid: { top: 120, right: 42, bottom: 40, left: 42 },
	tooltip: { trigger: 'axis' },
	toolbox: {
		show: true,
		left: 0,
		top: 20,
		// feature: {
		// 	saveAsImage: {}  // 导出图片
		// },
		feature: {
			saveAsImage: {},  // 导出图片
			dataZoom: {
				yAxisIndex: 'none'
			},
			dataView: {
				readOnly: false,
				optionToContent: function (opt: { xAxis: { data: any; }[]; series: { data: any; }[]; }) {
					let axisData = opt.xAxis[0].data;
					let seriesData = opt.series[0].data;
					let table =
						`<table style="margin:10px 20px;border-collapse:collapse;font-size:13px;text-align:center;width:calc(100% - 40px);line-height: 32px;border:1px solid #E5EFFB"><tbody><tr style="border-bottom:1px solid #E5EFFB; font-weight:bolder; font-size:14px;">
						<td>时间</td>
						<td>CO2(ppm)</td>
						</tr>`;
					for (let i = 0, l = axisData.length; i < l; i++) {
						table +=
							`<tr style="border-bottom:1px solid #E5EFFB">
							<td> ${axisData[i]} </td>
							<td> ${seriesData[i]} </td>
							</tr>`;
					}
					table += '</tbody></table>';
					return table;
				},
				textColor: '#0063E0',
				buttonColor: '#0063E0'
			},
			// magicType: { type: ['line', 'bar'] },
			// restore: {},
		},
	},
	xAxis: { data: [] },
	yAxis: [
		{
			type: 'value',
			name: '单位：ppm',
			splitLine: { show: true, lineStyle: { type: 'dashed', color: '#f5f5f5' } },
		},
	],
	visualMap: {
		top: -5,
		right: 0,
		pieces: [
			{
				gt: 0,
				lte: 500,
				color: '#93CE07'
			},
			{
				gt: 500,
				lte: 1000,
				color: '#FBDB0F'
			},
			{
				gt: 1000,
				lte: 1500,
				color: '#FC7D02'
			},
			{
				gt: 1500,
				color: '#FD0100'
			}
		],
		outOfRange: {
			color: '#999'
		}
	},
	series: [
		{
			type: 'line',
			symbolSize: 6,
			symbol: 'circle',
			smooth: true,
			data: [],
			markLine: {
				silent: true,
				lineStyle: {
					color: '#AAA'
				},
				symbolSize: 6,
				data: [
					{ yAxis: 500 },
					{ yAxis: 1000 },
					{ yAxis: 1500 },
				]
			}
		},
	],
	dataZoom: [
		{
			type: 'inside',
			start: 0,
			end: 100,
			minValueSpan: 9
		},
		{
			show: false,
			type: 'slider',
			bottom: 30,
			start: 0,
			end: 100,
			minValueSpan: 9
		}
	]
})

// 阈值
let threshold = ref<number>()
// 获取设备阈值信息
const getDeviceThreshold = async () => {
	const res = await getDeviceList({
		page: 1,
		pageSize: 99,
		keyWord: 'SHXH_CO2_23111301',
	})
	if (!res.data || res.data.list.length < 1) {
		return ElMessage.warning('获取设备阈值失败')
	}
	const device = res.data.list[res.data.list.length - 1]
	// 阈值
	threshold.value = device.threshold
	// markLine 更新
	const markLineLength = lineChartOption.series[0].markLine.data.length
	for (let i = 0; i < markLineLength; i++) {
		lineChartOption.series[0].markLine.data[i].yAxis = Math.round((<number>threshold.value / markLineLength) * (i + 1))
	}
	// visualMap 更新
	let lastgt = 0
	const visualMapLength = lineChartOption.visualMap.pieces.length
	for (let i = 0; i < visualMapLength; i++) {
		if (i != lineChartOption.visualMap.pieces.length - 1) {
			lineChartOption.visualMap.pieces[i].gt = lastgt
			lineChartOption.visualMap.pieces[i].lte = Math.round((<number>threshold.value / (visualMapLength - 1)) * (i + 1))
			lastgt = lineChartOption.visualMap.pieces[i].lte as number
		}
		else lineChartOption.visualMap.pieces[i].gt = <number>threshold.value
	}
}

// 获取折线图数据
const getLineChartData = async (dataType: string = timeType.value, startTime: string = parsedTime.startTime, endTime: string = parsedTime.endTime) => {
	const res = await getDetectionData({
		deviceNum: 'SHXH_CO2_23111301',
		dataType,
		startTime,
		endTime
	})
	// 进行转换避免报错
	const data = JSON.parse(JSON.stringify(res)).data
	if (data.length === 0) {
		ElMessage.warning('暂无数据！')
	}
	const xAxisDate = data.map((i: { data: number; time: string; }) => i.time)
	const seriesDate = data.map((i: { data: number; time: string; }) => i.data)
	lineChartOption.xAxis.data = xAxisDate
	lineChartOption.series[0].data = seriesDate
	// 复制一个新的配置对象
	const option = Object.assign({}, lineChartOption)
	// 如果数据大于 60 项，开启slider缩放
	if (seriesDate.length > 60) {
		Object.assign(option, {
			grid: { top: 100, right: 42, bottom: 90, left: 42 },
			dataZoom: [
				{
					type: 'inside',
					start: 95,
					end: 100,
					minValueSpan: 9
				},
				{
					show: true,
					type: 'slider',
					bottom: 20,
					start: 95,
					end: 100,
					minValueSpan: 9
				}
			]
		})
	}
	// 更新图表
	state.global.homeChartOne.setOption(option);
}

// 选择时间跨度
const changeTimeType = debounce((selectedTimeType: string) => {
	timeType.value = selectedTimeType
	getLineChartData()
}, 500)

// 折线图初始化
const initLineChart = () => {
	if (!state.global.dispose.some((b: any) => b === state.global.homeChartOne)) state.global.homeChartOne.dispose();
	state.global.homeChartOne = markRaw(echarts.init(homeLineRef.value, state.charts.theme));

	// state.global.homeChartOne.setOption(lineChartOption);
	state.myCharts.push(state.global.homeChartOne);
};

// 批量设置 echarts resize
const initEchartsResizeFun = () => {
	nextTick(() => {
		for (let i = 0; i < state.myCharts.length; i++) {
			setTimeout(() => {
				state.myCharts[i].resize();
			}, i * 1000);
		}
	});
};
// 批量设置 echarts resize
const initEchartsResize = () => {
	window.addEventListener('resize', initEchartsResizeFun);
};
// 页面加载时
onMounted(() => {
	initEchartsResize();
	// 实时数据
	state.detectedData = socketConnRef.value.getResult()
	// 获取设备信息
	getDeviceThreshold().then(() => {
		// 进入首页默认时间维度为 时 
		changeTimeType('hour_data')
	})
});

// 实时数据的时间戳转换为date格式字符串
const socketParsedTime = computed(() => {
	const time = state.detectedData.msg.time
	if (time) {
		// console.log(timestampToTime(time));
		return timestampToTime(time)
	}
	else {
		return ''
	}
})

// 由于页面缓存原因，keep-alive
onActivated(() => {
	initEchartsResizeFun();
});
// 监听 pinia 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
watch(
	() => isTagsViewCurrenFull.value,
	() => {
		initEchartsResizeFun();
	}
);
// 监听 pinia 中是否开启深色主题
watch(
	() => themeConfig.value.isIsDark,
	(isIsDark) => {
		nextTick(() => {
			state.charts.theme = isIsDark ? 'dark' : '';
			state.charts.bgColor = isIsDark ? 'transparent' : '';
			state.charts.color = isIsDark ? '#dadada' : '#303133';
			setTimeout(() => {
				initLineChart();
			}, 500);
		});
	},
	{
		deep: true,
		immediate: true,
	}
);
// 监听时间选择器是否更改
watch(timeSelect, (newValue) => {
	if (newValue) {
		// 字符串形式
		parsedTime.startTime = newValue[0].toString()
		parsedTime.endTime = newValue[1].toString()
		// 转为时间戳
		// parsedTime.startTime = Date.parse(newValue[0].toString())
		// parsedTime.endTime = Date.parse(newValue[1].toString())
	}
	else {
		parsedTime.startTime = ''
		parsedTime.endTime = ''
	}
	getLineChartData()
})
</script>

<style scoped lang="scss">
$homeNavLengh: 8;

.home-container {
	overflow: hidden;

	.home-card-one,
	.home-card-two {
		.home-card-item {
			width: 100%;
			height: 150px;
			border-radius: 4px;
			transition: all ease 0.3s;
			padding: 20px;
			padding-bottom: 32px;
			overflow: hidden;
			background: var(--el-color-white);
			color: var(--el-text-color-primary);
			border: 1px solid var(--next-border-color-light);

			&:hover {
				box-shadow: 0 2px 12px var(--next-color-dark-hover);
				transition: all ease 0.3s;
			}

			&-icon {
				width: 70px;
				height: 70px;
				border-radius: 100%;
				flex-shrink: 1;

				i {
					color: var(--el-text-color-placeholder);
				}
			}

			&-title {
				font-size: 15px;
				font-weight: bold;
				height: 30px;
			}
		}
	}

	.home-card-two {
		.home-card-item {
			height: 580px;
			width: 100%;
			overflow: hidden;
			padding-bottom: 60px;

			.home-monitor {
				height: 100%;

				.flex-warp-item {
					width: 25%;
					height: 111px;
					display: flex;

					.flex-warp-item-box {
						margin: auto;
						text-align: center;
						color: var(--el-text-color-primary);
						display: flex;
						border-radius: 5px;
						background: var(--next-bg-color);
						cursor: pointer;
						transition: all 0.3s ease;

						&:hover {
							background: var(--el-color-primary-light-9);
							transition: all 0.3s ease;
						}
					}

					@for $i from 0 through $homeNavLengh {
						.home-animation#{$i} {
							opacity: 0;
							animation-name: error-num;
							animation-duration: 0.5s;
							animation-fill-mode: forwards;
							animation-delay: calc($i/10) + s;
						}
					}
				}
			}
		}
	}
}


.flex-warp {
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;
	margin: 0 -5px 6px 0;

	.flex-warp-item {
		padding: 5px;

		.flex-warp-item-box {
			width: 100%;
			height: 100%;
		}
	}
}
</style>