<template>
    <div class="system-device-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-device-search mb15">
                <el-input v-model="searchParams.keyWord" :placeholder="'门店名称'" style="max-width: 200px" clearable>
                </el-input>
                <!-- <el-select v-model="searchParams.storeType" :placeholder="'门店类型'" style="max-width: 200px" clearable
                    class="ml10">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="门店" value="branch"></el-option>
                    <el-option label="总店" value="head"></el-option>
                </el-select> -->
                <el-select v-if="auths(['superAdmin', 'admin'])" v-model="searchParams.parentId"
                    @clear="searchParams.parentId = undefined" :placeholder="'所属总店'" style="max-width:200px" clearable
                    filterable class="ml10">
                    <el-option v-for="item in headList" :key="item._id" :label="item.placeName"
                        :value="item._id"></el-option>
                </el-select>
                <!-- 查询按钮 -->
                <el-button type="primary" class="ml10" @click="getTableData()">
                    <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }}</el-button>
                <!-- 新增用户 -->
                <el-button type="success" class="ml10" @click="onOpenAddDevice('add')">
                    <el-icon><ele-FolderAdd /></el-icon>{{ $t('message.common.add') }}</el-button>
            </div>
            <el-table :data="tableData" v-loading="tableLoading" table-layout="auto" style="width: 100%">
                <!-- 序号 -->
                <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="80" />
                <!-- 门店名称 -->
                <el-table-column prop="placeName" :label="'门店名称'"></el-table-column>
                <!-- 门店类型 -->
                <el-table-column prop="type" :label="'门店类型'">
                    <template #default="scope">
                        <span v-if="scope.row.parentId">门店</span>
                        <span v-else>总店</span>
                    </template>
                </el-table-column>
                <!-- 创建时间 -->
                <el-table-column prop="createdAt" :label="$t('message.common.createdAt')"></el-table-column>
                <!-- 操作 -->
                <el-table-column :label="$t('message.common.operation')" :width="120">
                    <template #default="scope">
                        <el-button size="small" text type="primary" @click="onOpenEditDevice('update', scope.row)">{{
                    $t('message.common.modify') }}</el-button>
                        <el-button size="small" text type="primary" @click="onRowDel(scope.row)">{{
                    $t('message.common.delete') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="paginationParams.pageNum" background
                v-model:page-size="paginationParams.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="paginationParams.total">
            </el-pagination>
        </el-card>
        <StoreDialog ref="StoreDialogRef" :headList="headList" @refresh="refresh()" />
    </div>
</template>

<script setup lang="ts" name="storeMana">
import { defineAsyncComponent, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getPlaceList, deletePlace } from "/@/api/publicPlace"

import { useI18n } from 'vue-i18n';
import { auths } from '/@/utils/authFunction';
const { t } = useI18n()

// 引入组件
const StoreDialog = defineAsyncComponent(() => import('/@/views/xdj/store/dialog.vue'));

// 查询参数
const searchParams = ref({
    keyWord: '',
    parentId: undefined,
})
// table
const tableLoading = ref(false)
const tableData = ref<receivedStore[]>([])
// pagination
const paginationParams = ref<paginationType>({
    total: 0,
    pageNum: 1,
    pageSize: 10,
})

// 总店列表
const headList = ref<receivedStore[]>([])

// 获取表格数据
const getTableData = async () => {
    tableLoading.value = true;
    const res = await getPlaceList({
        page: paginationParams.value.pageNum,
        pageSize: paginationParams.value.pageSize,
        ...searchParams.value,
        _type: 'store'
    })
    tableData.value = res.data.list;
    paginationParams.value.total = res.data.count;
    tableLoading.value = false;
};

// 获取所有总店
const getHeadList = async () => {
    const res = await getPlaceList({
        page: 1,
        pageSize: 999,
        _type: 'store'
    })
    headList.value = res.data.list.filter(ele => ele.parentId == '')
}

// 删除门店
const onRowDel = (row: receivedStore) => {
    ElMessageBox.confirm(`此操作将永久删除门店（${row.placeName}），是否继续？`, t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deletePlace(row._id)
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};

// 表格列行号
const indexMethod = (index: number) => {
    return (paginationParams.value.pageNum - 1) * paginationParams.value.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    paginationParams.value.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    paginationParams.value.pageNum = val;
    getTableData();
};

// 弹窗组件ref
const StoreDialogRef = ref();
// 打开新增设备弹窗
const onOpenAddDevice = (type: string) => {
    StoreDialogRef.value.openDialog(type);
};
// 打开修改设备弹窗
const onOpenEditDevice = (type: string, row: receivedStore) => {
    StoreDialogRef.value.openDialog(type, row);
};

// 刷新
const refresh = () => {
    getTableData();
    getHeadList()
}

// 页面加载时
onMounted(() => {
    refresh()
});
</script>

<style scoped lang="scss">
.system-device-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-device-search {
    display: flex;
    align-content: space-around;
}
</style>
