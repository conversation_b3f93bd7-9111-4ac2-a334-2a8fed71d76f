<?xml version="1.0" encoding="UTF-8"?>
<svg width="840px" height="87px" viewBox="0 0 840 87" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>头部  </title>
    <defs>
        <rect id="path-1" x="0" y="0" width="840" height="112"></rect>
        <linearGradient x1="50%" y1="3.06161713e-15%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#4FFEFF" stop-opacity="0.159999996" offset="0%"></stop>
            <stop stop-color="#4FFEFF" stop-opacity="0.460000008" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="3.06161713e-15%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#56FEFE" stop-opacity="0.920000017" offset="0%"></stop>
            <stop stop-color="#56FEFE" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="3.06161713e-15%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#25FFE5" stop-opacity="0.119999997" offset="0%"></stop>
            <stop stop-color="#25FFE5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-6" points="509.090909 0 439.385682 53.5 384.939311 53.5 454.644538 0"></polygon>
        <mask id="mask-7" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="124.151598" height="53.5" fill="white">
            <use xlink:href="#path-6"></use>
        </mask>
        <linearGradient x1="50%" y1="3.06161713e-15%" x2="50%" y2="100%" id="linearGradient-8">
            <stop stop-color="#56FEFE" stop-opacity="0.920000017" offset="0%"></stop>
            <stop stop-color="#56FEFE" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="3.06161713e-15%" x2="50%" y2="100%" id="linearGradient-9">
            <stop stop-color="#25FFE5" stop-opacity="0.119999997" offset="0%"></stop>
            <stop stop-color="#25FFE5" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-10" points="421.699282 0 362.39782 44 307.951449 44 367.252911 0"></polygon>
        <mask id="mask-11" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="113.747833" height="44" fill="white">
            <use xlink:href="#path-10"></use>
        </mask>
        <polygon id="path-12" points="344.711419 0 285.409958 44 230.963587 44 290.265048 0"></polygon>
        <mask id="mask-13" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="113.747833" height="44" fill="white">
            <use xlink:href="#path-12"></use>
        </mask>
        <polygon id="path-14" points="124.151598 0 54.4463711 53.5 0 53.5 69.7052267 0"></polygon>
        <mask id="mask-15" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="124.151598" height="53.5" fill="white">
            <use xlink:href="#path-14"></use>
        </mask>
        <polygon id="path-16" points="201.13946 0 141.837999 44 87.3916274 44 146.693089 0"></polygon>
        <mask id="mask-17" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="113.747833" height="44" fill="white">
            <use xlink:href="#path-16"></use>
        </mask>
        <polygon id="path-18" points="278.127322 0 218.825861 44 164.37949 44 223.680951 0"></polygon>
        <mask id="mask-19" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="113.747833" height="44" fill="white">
            <use xlink:href="#path-18"></use>
        </mask>
        <linearGradient x1="0.352112667%" y1="53.3330259%" x2="100.352113%" y2="53.3329444%" id="linearGradient-20">
            <stop stop-color="#18CACA" offset="0%"></stop>
            <stop stop-color="#56FEFE" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="徐汇区巡航监测“谛听”系统（公共场所）备份-6" transform="translate(-1, 0)">
            <g id="头部--" transform="translate(1, 0)">
                <g id="背景_大标题">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <g id="标题背景-(Background/Mask)"></g>
                    <path d="M256.115309,70.5 L269.881638,84.5 L560.722656,84.5 L574.488986,70.5 L840,70.5 L840,-2 L0,-2 L0,70.5 L59.2349727,70.5 L256.115309,70.5 Z" id="Vector-21" fill-opacity="0.319999993" fill="url(#linearGradient-3)" mask="url(#mask-2)"></path>
                    <g id="装饰" mask="url(#mask-2)" fill-opacity="0.0500000007" stroke-dasharray="0,0">
                        <g transform="translate(-296.1411, 14.5)">
                            <g id="Group-8">
                                <use id="Rectangle-19" stroke="url(#linearGradient-5)" mask="url(#mask-7)" stroke-width="2" fill="url(#linearGradient-4)" fill-rule="nonzero" transform="translate(447.0151, 26.75) scale(1, -1) rotate(-180) translate(-447.0151, -26.75)" xlink:href="#path-6"></use>
                                <use id="Rectangle-20" stroke="url(#linearGradient-9)" mask="url(#mask-11)" stroke-width="2" fill="url(#linearGradient-8)" fill-rule="nonzero" transform="translate(364.8254, 22) scale(1, -1) rotate(-180) translate(-364.8254, -22)" xlink:href="#path-10"></use>
                                <use id="Rectangle-21" stroke="url(#linearGradient-9)" mask="url(#mask-13)" stroke-width="2" fill="url(#linearGradient-8)" fill-rule="nonzero" transform="translate(287.8375, 22) scale(1, -1) rotate(-180) translate(-287.8375, -22)" xlink:href="#path-12"></use>
                            </g>
                            <g id="Group-9" transform="translate(903.3381, 2)">
                                <use id="Rectangle-19" stroke="url(#linearGradient-5)" mask="url(#mask-15)" stroke-width="2" fill="url(#linearGradient-4)" fill-rule="nonzero" xlink:href="#path-14"></use>
                                <use id="Rectangle-20" stroke="url(#linearGradient-9)" mask="url(#mask-17)" stroke-width="2" fill="url(#linearGradient-8)" fill-rule="nonzero" xlink:href="#path-16"></use>
                                <use id="Rectangle-21" stroke="url(#linearGradient-9)" mask="url(#mask-19)" stroke-width="2" fill="url(#linearGradient-8)" fill-rule="nonzero" xlink:href="#path-18"></use>
                            </g>
                        </g>
                    </g>
                    <g id="Group-6" mask="url(#mask-2)" fill-rule="nonzero">
                        <g transform="translate(640.8878, 79.5)">
                            <path d="M12.0809659,0 L6.26420455,7.5 L0,7.5 L5.81676136,0 L12.0809659,0 Z" id="Rectangle-19" fill="#18CACA"></path>
                            <path d="M22.3721591,0 L16.5553977,7.5 L10.2911932,7.5 L16.1079545,0 L22.3721591,0 Z" id="Rectangle-20" fill="#18CACA"></path>
                            <path d="M32.6633523,0 L26.8465909,7.5 L20.5823864,7.5 L26.3991477,0 L32.6633523,0 Z" id="Rectangle-21" fill="#18CACA"></path>
                            <path d="M42.9545455,0 L37.1377841,7.5 L30.8735795,7.5 L36.6903409,0 L42.9545455,0 Z" id="Rectangle-22" fill="#18CACA"></path>
                            <path d="M53.2457386,0 L47.4289773,7.5 L41.1647727,7.5 L46.9815341,0 L53.2457386,0 Z" id="Rectangle-23" fill="#18CACA"></path>
                            <path d="M63.5369318,0 L57.7201705,7.5 L51.4559659,7.5 L57.2727273,0 L63.5369318,0 Z" id="Rectangle-24" fill="#18CACA"></path>
                            <path d="M73.828125,0 L68.0113636,7.5 L61.7471591,7.5 L67.5639205,0 L73.828125,0 Z" id="Rectangle-25" fill="#18CACA"></path>
                            <path d="M199.112216,0 L193.295455,7.5 L72.0383523,7.5 L77.8551136,0 L199.112216,0 Z" id="Rectangle-26" fill="url(#linearGradient-20)"></path>
                        </g>
                    </g>
                    <g id="Group-7" mask="url(#mask-2)" fill-rule="nonzero">
                        <g transform="translate(0, 79.5)">
                            <path d="M199.112216,0 L193.295455,7.5 L187.03125,7.5 L192.848011,0 L199.112216,0 Z" id="Rectangle-19" fill="#18CACA" transform="translate(193.0717, 3.75) scale(1, -1) rotate(-180) translate(-193.0717, -3.75)"></path>
                            <path d="M188.821023,0 L183.004261,7.5 L176.740057,7.5 L182.556818,0 L188.821023,0 Z" id="Rectangle-20" fill="#18CACA" transform="translate(182.7805, 3.75) scale(1, -1) rotate(-180) translate(-182.7805, -3.75)"></path>
                            <path d="M178.52983,0 L172.713068,7.5 L166.448864,7.5 L172.265625,0 L178.52983,0 Z" id="Rectangle-21" fill="#18CACA" transform="translate(172.4893, 3.75) scale(1, -1) rotate(-180) translate(-172.4893, -3.75)"></path>
                            <path d="M168.238636,0 L162.421875,7.5 L156.15767,7.5 L161.974432,0 L168.238636,0 Z" id="Rectangle-22" fill="#18CACA" transform="translate(162.1982, 3.75) scale(1, -1) rotate(-180) translate(-162.1982, -3.75)"></path>
                            <path d="M157.947443,0 L152.130682,7.5 L145.866477,7.5 L151.683239,0 L157.947443,0 Z" id="Rectangle-23" fill="#18CACA" transform="translate(151.907, 3.75) scale(1, -1) rotate(-180) translate(-151.907, -3.75)"></path>
                            <path d="M147.65625,0 L141.839489,7.5 L135.575284,7.5 L141.392045,0 L147.65625,0 Z" id="Rectangle-24" fill="#18CACA" transform="translate(141.6158, 3.75) scale(1, -1) rotate(-180) translate(-141.6158, -3.75)"></path>
                            <path d="M137.365057,0 L131.548295,7.5 L125.284091,7.5 L131.100852,0 L137.365057,0 Z" id="Rectangle-25" fill="#18CACA" transform="translate(131.3246, 3.75) scale(1, -1) rotate(-180) translate(-131.3246, -3.75)"></path>
                            <path d="M127.073864,0 L121.257102,7.5 L0,7.5 L5.81676136,0 L127.073864,0 Z" id="Rectangle-26" fill="url(#linearGradient-20)" transform="translate(63.5369, 3.75) scale(1, -1) rotate(-180) translate(-63.5369, -3.75)"></path>
                        </g>
                    </g>
                </g>
                <g id="标题" transform="translate(177, 24)"></g>
            </g>
        </g>
    </g>
</svg>