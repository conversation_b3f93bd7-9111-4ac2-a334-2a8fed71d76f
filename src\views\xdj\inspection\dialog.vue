<template>
	<div class="xdj-inspection-dialog-container">
		<el-dialog :title="dialogParams.title" v-model="dialogParams.isShowDialog" width="769px">
			<el-form ref="ReviewDialogFormRef" :rules="rules" :model="formData" label-width="auto">
				<el-row>
					<!-- 审核结果 -->
					<el-col :span="24" class="mb20">
						<el-form-item :label="'审核结果'" prop="reviewStatus">
							<el-select v-model="formData.reviewStatus" :placeholder="'请选择审核结果'" clearable
								:disabled="dialogParams.type == 'update'">
								<el-option label="通过" :value="1"></el-option>
								<el-option label="不通过" :value="0"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<!-- 审核意见 -->
					<el-col class="mb20 ">
						<el-form-item :label="'审核意见'" prop="returnReason">
							<el-input v-model="formData.returnReason" :placeholder="'请输入审核意见'" clearable
								class="pr10"></el-input>
						</el-form-item>
					</el-col>
				</el-row>

			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(ReviewDialogFormRef)">提交</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemUserDialog">
import { reactive, ref, nextTick } from 'vue';
import { reviewDisinfectant } from "/@/api/xdj/inspection";

import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 校验规则
const rules = reactive({
	reviewStatus: [{ required: true, message: '请选择审核结果', trigger: 'blur' }],
})
// 组件ref
const ReviewDialogFormRef = ref();
// 表单
const useFormData = () => ref({
	_id: '',
	reviewStatus: undefined,
	returnReason: '',
	userRole: ''	// 用户角色
})
const formData = useFormData()


// 对话框配置
const dialogParams = ref<dialogParamsType>({
	isShowDialog: false,
	type: '',
	title: '',
	submitTxt: '',
})

// 打开弹窗
const openDialog = async (row: xdjForm, userRole: userRole) => {
	Object.assign(formData.value, useFormData().value)
	formData.value._id = row._id
	formData.value.userRole = userRole
	dialogParams.value.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		ReviewDialogFormRef.value.resetFields();
	});
	dialogParams.value.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
	formEl.validate(async (valid) => {
		if (!valid) return;

		await reviewDisinfectant({ _id: formData.value._id, reviewStatus: <0 | 1><unknown>formData.value.reviewStatus, returnReason: formData.value.returnReason })
		ElMessage.success(t('操作成功'))

		closeDialog();
		emit('refresh');
	})
};

defineExpose({
	openDialog,
});
</script>
