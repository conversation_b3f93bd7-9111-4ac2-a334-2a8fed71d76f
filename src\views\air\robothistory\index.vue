<template>
	<div>
		<div class="system-detection-search mb15">
			<el-input v-model="state.taskNum" :placeholder="$t('message.views.placeholderTaskNumSearch')" style="max-width: 240px; width: 150px" clearable>
			</el-input>
			<el-select
				v-model="state.deviceName"
				:placeholder="$t('message.views.placeholderchangeDeviceModel')"
				clearable
				filterable
				style="max-width: 240px; width: 150px; margin: 0 10px"
			>
				<el-option v-for="item in DeviceList" :key="item" :label="item.deviceNum" :value="item.deviceName"></el-option>
			</el-select>
			<el-select
				v-model="state.robotPubilcPlace"
				:placeholder="$t('message.views.placeholderchangePlaceName')"
				clearable
				filterable
				style="max-width: 240px; width: 150px"
			>
				<el-option v-for="item in PlaceList" :key="item" :label="item.placeName" :value="item._id"></el-option>
			</el-select>
			<el-select
				v-model="state.result"
				:placeholder="$t('message.views.placeholderResult')"
				clearable
				filterable
				style="max-width: 240px; width: 150px; margin: 0 10px"
			>
				<el-option v-for="item in resultList" :key="item" :label="item.label" :value="item.value"></el-option>
			</el-select>

			<el-date-picker
				v-model="timeSelect"
				type="datetimerange"
				:shortcuts="shortcuts"
				range-separator="-"
				:start-placeholder="$t('message.views.startingTime')"
				:end-placeholder="$t('message.views.endingTime')"
				:default-time="defaultTime"
				:disabledDate="disabledDate"
				style="max-width: 420px; width: 200px"
				class="mr10"
			/>
			<el-button type="primary" @click="getTableData()">
				<el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }}
			</el-button>
		</div>
		<el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%; height: 57vh">
			<el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="60" />
			<el-table-column prop="deviceInfo.deviceNum" :label="$t('message.views.deviceNum')" show-overflow-tooltip width="120"></el-table-column>
			<el-table-column prop="deviceInfo.deviceNickName" :label="$t('message.views.deviceName')" show-overflow-tooltip width="130"></el-table-column>
			<el-table-column prop="deviceInfo.deviceModel" :label="$t('message.views.deviceModel')" show-overflow-tooltip width="130"></el-table-column>
			<el-table-column prop="" :label="$t('message.views.taskNum')" show-overflow-tooltip width="120">
				<template #default="{ row }">
					<div v-if="row.taskNum">
						{{ row.taskNum }}
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="" :label="$t('message.views.placeName')" show-overflow-tooltip width="120">
				<template #default="{ row }">
					<div v-if="row.taskInfo && row.taskInfo.placeInfo">
						{{ row.taskInfo.placeInfo.placeName }}
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="" :label="$t('message.views.Metrics')" show-overflow-tooltip width="60">
				<template #default="{ row }">
					<el-link type="primary" :underline="false" v-if="row.taskInfo && row.taskInfo.placeInfo" @click="onOpenMetrics(row)">{{
                         row.taskInfo.placeInfo.sensorData.filter((sensor: any) => sensor.choose)
                            .length
					}}</el-link>
					<el-link type="primary" :underline="false" v-else @click="onOpenMetricsNoRange(row)">{{ Object.keys(row.sensorData).length }}</el-link>
				</template>
			</el-table-column>
			<el-table-column prop="sensorData.sample_point" :label="$t('message.views.sample_point')" show-overflow-tooltip width="120"></el-table-column>
			<el-table-column prop="result" :label="$t('message.views.Result')" show-overflow-tooltip width="120">
				<template #default="{ row }">
					<el-tag type="success" v-if="row.result === '合格'">{{ row.result }}</el-tag>
					<el-tag type="danger"  v-if="row.result === '不合格'">{{ row.result }}</el-tag>
				</template>
			</el-table-column>
			<el-table-column :label="$t('message.views.receiveTime')" min-width="200" show-overflow-tooltip>
				<template #default="{ row }">
					{{ formatTime(row.time) }}
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
			class="mt15"
			:pager-count="5"
			:page-sizes="[10, 20, 50, 100]"
			v-model:current-page="state.tableData.param.pageNum"
			background
			v-model:page-size="state.tableData.param.pageSize"
			layout="total, sizes, prev, pager, next, jumper"
			:total="state.tableData.total"
		>
		</el-pagination>

		<Dialog :showrow="showrow" :showNoRangerow="showNoRangerow" ref="dialogRef"></Dialog>
	</div>
</template>

<script setup lang="ts" name="robotHistory">
import { formatTime } from '/@/utils/formatTime';
import { ref, reactive, computed, onBeforeMount, onMounted, defineAsyncComponent, nextTick } from 'vue';
// import { ElMessage } from 'element-plus';
import { getRobotDataList } from '/@/api/robotHistory/index';
// import { downFileByA, getFullUrl } from '/@/utils/common';
import { getRobotDeviceList } from '/@/api/robotDevice/index';
import { getRobotPlaceList } from '/@/api/publicPlace/index';
// 国际化
import { useI18n } from 'vue-i18n';
// import { Row } from 'element-plus/es/components/table-v2/src/components';

const Dialog = defineAsyncComponent(() => import('./dialog.vue'));
const { t } = useI18n();
// 导出excel
// import * as XLSX from 'xlsx';
const dialogRef = ref();

// 设备列表
const DeviceList = reactive<rowRobotDeviceType[]>([]);

// 场所列表
const PlaceList = reactive<RobotplaceDataFome[]>([]);

// 采样结果
const resultList = reactive([
	{
		label: '合格',
		value: '合格',
	},
	{
		label: '不合格',
		value: '不合格',
	},
]);

// table数据
const state = reactive<RobotHistoryState>({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		param: {
			pageNum: 1,
			pageSize: 10,
		},
	},
	taskNum: '',
	deviceName: '',
	robotPubilcPlace: '',
	result: '',
	exportBtnLoading: false,
});

const showrow = ref();

const showNoRangerow = ref();

// 时间选择器
const timeSelect = ref('');
// shortcuts
const shortcuts = reactive([
	// {
	//     text: computed(() => t('message.views.lastMonth')),
	//     value: () => {
	//         const end = new Date()
	//         const start = new Date()
	//         start.setTime(start.getTime() - 1000 * 3600 * 24 * 30)
	//         return [start, end]
	//     },
	// },
	{
		text: computed(() => t('message.views.lastWeek')),
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 1000 * 3600 * 24 * 7);
			return [start, end];
		},
	},
	{
		text: computed(() => t('message.views.lastDay')),
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 1000 * 3600 * 24);
			return [start, end];
		},
	},
	{
		text: computed(() => t('message.views.lastHour')),
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 1000 * 3600);
			return [start, end];
		},
	},
]);
// 默认起始和结束时间 '00:00:00', '23:59:59'
const defaultTime: [Date, Date] = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)];
// 不能选择今天之后的日期
const disabledDate = (time: Date) => {
	return time.getTime() > Date.now();
};

// 获取表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	const taskNum = state.taskNum;
	const deviceName = state.deviceName;
	const robotPubilcPlace = state.robotPubilcPlace;
	const result = state.result;
	const startTime = timeSelect.value ? timeSelect.value[0] : undefined;
	const endTime = timeSelect.value ? timeSelect.value[1] : undefined;
	const res = await getRobotDataList({
		page: state.tableData.param.pageNum,
		pageSize: state.tableData.param.pageSize,
		taskNum,
		deviceName,
		robotPubilcPlace,
		result,
		startTime,
		endTime,
	});
	state.tableData.data = res.data.list;
	state.tableData.total = res.data.total;
	state.tableData.loading = false;
};

const RobotDeviceList = async () => {
	const res = await getRobotDeviceList({
		page: 1,
		pageSize: 99999,
	});
	Object.assign(DeviceList, res.data.list);
};
const RobotPlaceList = async () => {
	const res = await getRobotPlaceList({
		page: 1,
		pageSize: 99999,
	});
	Object.assign(PlaceList, res.data.list);
};

const onOpenMetrics = (row: any) => {
	showrow.value = {
		testsensorData: row.sensorData,
		sensorData: row.taskInfo.placeInfo.sensorData,
		time: formatTime(row.time),
	};
	// dialogTableVisible.value=true
    nextTick(() => {
		dialogRef.value.opendialogTableVisible();
	});
};

const onOpenMetricsNoRange = (row: any) => {
	showNoRangerow.value = {
		testsensorData:row.sensorData,
		time: formatTime(row.time)
	};
	// console.log(showNoRangerow.value, 'showNoRangerow.value');
	nextTick(() => {
		dialogRef.value.opendialogTableVisible2();
	});
};

onBeforeMount(() => {
	getTableData();
});
onMounted(() => {
	RobotDeviceList();
	RobotPlaceList();
});

// 表格列行号
const indexMethod = (index: number) => {
	return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1;
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};
</script>

<style scoped>
.select {
	margin-right: 10px;
}

.system-detection-container {
	:deep(.el-card__body) {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;

		.el-table {
			flex: 1;
		}
	}
}

.system-detection-search {
	display: flex;
	align-content: space-around;
}

.table-column-select-header {
	width: 120px;
}

.table-column-select-cell {
	padding-left: 14px;
}
</style>