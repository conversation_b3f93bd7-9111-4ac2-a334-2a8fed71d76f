#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
error_log  /var/logs/error.log  notice;
#error_log  /var/logs/error.log  info;

#pid        logs/nginx.pid;

events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;


    server {
        listen       80 default_server;

        # 传递真实客户端IP，处理多层代理场景
        # 设置真实IP（优先使用X-Real-IP，如果没有则取X-Forwarded-For的第一个IP）
        set $real_ip $remote_addr;
        if ($http_x_real_ip) {
            set $real_ip $http_x_real_ip;
        }
        if ($http_x_forwarded_for) {
            set $real_ip $http_x_forwarded_for;
        }

        # 处理X-Forwarded-For中的多个IP，取第一个（最原始的客户端IP）
        if ($real_ip ~* "^([^,]+)") {
            set $real_ip $1;
        }

        proxy_set_header X-Real-IP $real_ip;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header Host $http_host;

        location / {
            root   /usr/vuejs/nginx/;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # WebSocket代理配置
        location /socket.io/ {
            proxy_pass http://iot-oapi;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $http_host;
            proxy_cache_bypass $http_upgrade;

            # WebSocket连接超时设置
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }

        # API代理配置
        location /api/ {
            proxy_pass http://iot-oapi/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $http_host;
            proxy_set_header X-Original-URI $request_uri;
        }
    }
}

