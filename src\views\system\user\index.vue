<template>
	<div class="system-user-container layout-padding">
		<el-card shadow="hover" class="layout-padding-auto">
			<!-- 搜索 -->
			<div class="system-user-search mb15">
				<el-input v-model="state.keyWord" :placeholder="$t('message.views.placeholderUserKeyword')"
					style="max-width: 240px" class="mr10" clearable> </el-input>
				<el-button type="primary" @click="getTableData()">
					<el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }} </el-button>
				<el-button type="success" @click="onOpenAddUser('add')">
					<el-icon><ele-FolderAdd /></el-icon>{{ $t('message.views.userAdd') }} </el-button>
			</div>
			<!-- 表格 -->
			<el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%">
				<el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
				<el-table-column prop="userName" :label="$t('message.views.userName')"
					show-overflow-tooltip></el-table-column>
				<el-table-column prop="name" :label="$t('message.common.name')" show-overflow-tooltip></el-table-column>
				<el-table-column prop="role" :label="$t('message.views.userRole')" show-overflow-tooltip>
					<template #default="scope">
						<span v-if="scope.row.role == 'admin'">{{ $t('message.common.admin') }}</span>
						<!-- <span v-else>{{ $t('message.common.user') }}</span> -->
					</template>
				</el-table-column>
				<el-table-column prop="phoneNum" :label="$t('message.common.phoneNum')"
					show-overflow-tooltip></el-table-column>
				<!-- 用户状态 -->
				<el-table-column prop="active" :label="$t('message.views.userStatus')" min-width="86"
					show-overflow-tooltip>
					<template #default="scope">
						<el-tag type="success" v-if="scope.row.active == 1">{{ $t('message.common.enable') }}</el-tag>
						<el-tag type="info" v-else>{{ $t('message.common.disable') }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="createdAt" :label="$t('message.common.createdAt')"
					show-overflow-tooltip></el-table-column>
				<el-table-column :label="$t('message.common.operation')"
					:width="120">

					<template #default="scope">
						<el-button size="small" text type="primary" @click="onOpenEditUser('update', scope.row)">{{
					$t('message.common.modify') }}</el-button>
						<el-button size="small" text type="primary" @click="onRowDel(scope.row)">{{
					$t('message.common.delete') }}</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
				:pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum"
				background v-model:page-size="state.tableData.param.pageSize"
				layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
			</el-pagination>
		</el-card>
		<UserDialog ref="userDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemUser">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getUserList, deleteUsers } from "/@/api/user/index";
// 
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

// 引入组件
const UserDialog = defineAsyncComponent(() => import('/@/views/system/user/dialog.vue'));
// 组件ref
const userDialogRef = ref();
// 定义变量内容
const state = reactive<SysUserState>({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		param: {
			pageNum: 1,
			pageSize: 10,
		},
	},
	// 搜索框输入
	keyWord: ''
});

// 初始化表格数据
const getTableData = async (keyWord: string = state.keyWord) => {
	state.tableData.loading = true;
	const res = await getUserList({
		page: state.tableData.param.pageNum,
		pageSize: state.tableData.param.pageSize,
		keyWord,
		// 查询角色为 卫健委监督员（管理员），超级管理员
		roles: ['admin', 'superAdmin'],
	})
	const data = [...res.data.list]
	state.tableData.data = data;
	state.tableData.total = res.data.count;
	state.tableData.loading = false;
};
// 打开新增用户弹窗
const onOpenAddUser = (type: string) => {
	userDialogRef.value.openDialog(type);
};
// 打开修改用户弹窗
const onOpenEditUser = (type: string, row: RowUserType) => {
	userDialogRef.value.openDialog(type, row);
};
// 删除用户
const onRowDel = (row: RowUserType) => {
	ElMessageBox.confirm(t('message.views.deleteUserConfirm', { userName: row.name }), t('message.common.prompt'), {
		confirmButtonText: t('message.common.confirm'),
		cancelButtonText: t('message.common.cancel'),
		type: 'warning',
	})
		.then(() => {
			deleteUsers({ id: row._id })
		})
		.then(() => {
			ElMessage.success('删除成功！');
			getTableData();
		})
		.catch(() => {
		});
};
// 表格列行号
const indexMethod = (index: number) => {
	return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};

// 页面加载
onMounted(() => {
	getTableData();
});
</script>

<style scoped lang="scss">
.system-user-container {
	:deep(.el-card__body) {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;

		.el-table {
			flex: 1;
		}
	}
}

.system-user-search {
	display: flex;
	align-content: space-around;
}
</style>
