<template>
	<div class="system-user-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="userDialogFormRef" :rules="rules" :model="state.dataFome" label-width="auto">
				<el-row :gutter="35">
					<!-- 姓名 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.common.name')" prop="name">
							<el-input v-model="state.dataFome.name" :placeholder="$t('message.common.placeholderName')"
								clearable></el-input>
						</el-form-item>
					</el-col>
					<!-- 手机号 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.common.phoneNum')" prop="phoneNum">
							<el-input v-model="state.dataFome.phoneNum"
								:placeholder="$t('message.common.placeholderPhoneNum')" clearable></el-input>
						</el-form-item>
					</el-col>
					<!-- 用户名 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.userName')" prop="userName">
							<el-input v-model="state.dataFome.userName"
								:placeholder="$t('message.views.placeholderUserName')" clearable></el-input>
						</el-form-item>
					</el-col>
					<!-- 密码 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="state.dialog.type == 'add'">
						<el-form-item :label="$t('message.common.password')" prop="password">
							<el-input v-model="state.dataFome.password"
								:placeholder="$t('message.common.placeholderPassword')" type="password" clearable
								show-password></el-input>
						</el-form-item>
					</el-col>
					<!-- 角色 -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.userRole')" prop="role">
							<el-select v-model="state.dataFome.role" :placeholder="$t('message.views.placeholderUserRole')"
								clearable class="w100">
								<el-option label="监督员" value="admin"></el-option>
							</el-select>
						</el-form-item>
					</el-col> -->
					<!-- 用户状态 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.userStatus')" prop="active">
							<el-switch v-model="state.dataFome.active" :active-value="1" :inactive-value="0"
								inline-prompt :active-text="$t('message.common.enable', 2)"
								:inactive-text="$t('message.common.disable', 2)">
							</el-switch>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(userDialogFormRef)">{{ state.dialog.submitTxt }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemUserDialog">
import { reactive, ref, nextTick, computed } from 'vue';
import { createUsers, updateUsers } from "/@/api/user/index";
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 组件ref
const userDialogFormRef = ref();
// 定义变量内容
const useState = () => reactive<userDialogState>({
	dataFome: {
		userName: '',
		// password: '',
		name: '',
		phoneNum: '',
		// 在此处创建的角色统一为 卫健委监督员（管理员）
		role: 'admin',
		active: 1,
		_id: '',
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});
const state = useState()

// 校验规则
const rules = reactive({
	// userName: [{ required: true, message: computed(() => t('message.views.placeholderUserName')), trigger: 'blur' }],
	password: [
		// { required: true, message: computed(() => t('message.common.placeholderPassword')), trigger: 'blur' },
		{ min: 6, max: 16, message: computed(() => t('message.views.rulesUserPasswordLength')), trigger: 'change' }
	],
	name: [{ required: true, message: computed(() => t('message.common.placeholderName')), trigger: 'blur' }],
	phoneNum: [
		{ required: true, message: computed(() => t('message.common.placeholderPhoneNum')), trigger: 'blur' },
		{ pattern: /^1[3456789]\d{9}$/, message: computed(() => t('message.views.rulesUserPhoneNumReg')), trigger: 'blur' }
	]
})

// 打开弹窗
const openDialog = (type: string, row: RowUserType) => {
	// 初始化数据
	if (type === 'update') {
		// state.dataFome = {...row}

		const { userName, name, phoneNum, role, active, _id } = row
		const data = { userName, name, phoneNum, role, active, _id }

		state.dataFome = data

		state.dialog.type = 'update'
		state.dialog.title = t('message.views.userDialogUpdateTitle');
		state.dialog.submitTxt = t('message.common.modify');
	} else {
		Object.assign(state.dataFome, useState().dataFome)

		state.dialog.type = 'add'
		state.dialog.title = t('message.views.userDialogAddTitle');
		state.dialog.submitTxt = t('message.common.add');
	}
	state.dialog.isShowDialog = true;
	// getMenuData();
};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		userDialogFormRef.value.resetFields();
	});
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
	formEl.validate(async (valid) => {
		if (!valid) {
			return
		}
		if (state.dialog.type === 'add') {
			await createUsers(state.dataFome)
			ElMessage.success(t('message.common.addSuccess'))
		}
		else {
			await updateUsers(state.dataFome)
			ElMessage.success(t('message.common.modifySuccess'))
		}
		closeDialog();
		emit('refresh');
	})
};

defineExpose({
	openDialog,
});
</script>
