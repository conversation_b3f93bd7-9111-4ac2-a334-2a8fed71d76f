<template>
    <div class="system-disinfectant-container layout-padding">
        <el-card shadow="hover" class="card layout-padding-auto" :body-style="{ height: '100%' }">
            <template v-if="!flag" #header>
                <div class="card-header">
                    <el-button size="small" text :icon="ArrowLeft" @click="onHeaderClick"></el-button>
                    <b style="font-size: 1.2em;">{{ cardTitle }}</b>
                </div>
            </template>
            <div class="w100 h100" v-show="flag">
                <disinfectantTable />
            </div>
            <el-scrollbar v-show="!flag">
                <disinfectantForm ref="formRef" />
            </el-scrollbar>
        </el-card>
    </div>
</template>

<script setup lang="ts" name="xdjInspection">
import { defineAsyncComponent, onMounted, onUnmounted, ref } from 'vue';
import mittBus from '/@/utils/mitt';
import { ArrowLeft } from '@element-plus/icons-vue'
const disinfectantTable = defineAsyncComponent(() => import('/@/views/xdj/inspection/table.vue'));
const disinfectantForm = defineAsyncComponent(() => import('/@/views/xdj/inspection/form.vue'));

const flag = ref(true)
const cardTitle = ref('')
const formRef = ref()

const onHeaderClick = () => {
    mittBus.emit('xdjInspectShowTable')
}

onMounted(() => {
    mittBus.on('xdjInspectShowForm', (params: { type: 'add' | 'update' | 'readonly', id?: string }) => {
        flag.value = false
        switch (params.type) {
            case 'add':
                cardTitle.value = '新增报告'
                break;
            case 'update':
                cardTitle.value = '修改报告'
                break;
            case 'readonly':
                cardTitle.value = '查看报告'
                break;
            default:
                break;
        }
        formRef.value.openForm(params)
    });

    mittBus.on('xdjInspectShowTable', () => {
        flag.value = true
    })
})

onUnmounted(() => {
    mittBus.off('xdjInspectShowForm');
    mittBus.off('xdjInspectShowTable');
});

</script>

<style lang="scss">
.card-header {
    display: flex;
}
</style>