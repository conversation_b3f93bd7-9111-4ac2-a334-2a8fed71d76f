<template>
    <div >
        <div class="system-place-search mb15">
                <el-input v-model="state.placeName" :placeholder="$t('message.views.placeholderPlaceNameSearch')"
                    style="max-width: 240px" clearable>
                </el-input>
                <el-button size="default" type="primary" class="ml10" @click="getTableData()">
                    <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }} </el-button>
                <el-button size="default" type="success" class="ml10" @click="onOpenAddDevice('add')">
                    <el-icon><ele-FolderAdd /></el-icon>{{ $t('message.views.placeAdd') }} </el-button>
        </div>
        <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%;height: 57vh;">
            <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
            <el-table-column prop="placeName" :label="$t('message.views.placeName')"
                show-overflow-tooltip ></el-table-column>
            <el-table-column prop="placeAddress" :label="$t('message.views.placeAddress')"></el-table-column>
            <el-table-column prop="InchargeName" :label="$t('message.views.InchargeName')"
                show-overflow-tooltip width="130"></el-table-column>
            <el-table-column prop="InchargePhone" :label="$t('message.views.InchargePhone')"
                show-overflow-tooltip width="130"></el-table-column>
            <el-table-column prop="SupervisionName" :label="$t('message.views.SupervisionName')"
                show-overflow-tooltip width="130"></el-table-column>
            <el-table-column prop="SupervisionPhone" :label="$t('message.views.SupervisionPhone')"
                show-overflow-tooltip width="130"></el-table-column>
            <el-table-column prop="sensorData" :label="$t('message.views.sensorData')"
                show-overflow-tooltip>
                <template #default="scope">
                    {{ scope.row.sensorData
                        .filter((sensor: any) => sensor.choose)
                        .map((sensor: any) => sensor.sensorName)
                        .join(', ') }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('message.common.operation')" width="120" fixed="right">
                <template #default="scope">
                    <el-button size="small" text type="primary" @click="onOpenEditDevice('update', scope.row)">{{
                        $t('message.common.modify') }}</el-button>
                    <el-button size="small" text type="primary" @click="onRowDel(scope.row)">{{
                        $t('message.common.delete') }}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
            :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
            v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="state.tableData.total">
        </el-pagination>
        <PlaceDialog ref="PlaceDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="airPlaceManage">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getRobotPlaceList, deleteRobotPlace } from "/@/api/publicPlace/index";
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 引入组件
const PlaceDialog = defineAsyncComponent(() => import('./dialog.vue'));
// 组件ref
const PlaceDialogRef = ref();
// 定义变量内容
const state = reactive<robotplaceState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 查询输入
    placeName: '',
});

// 获取表格数据
const getTableData = async (placeName: string = state.placeName) => {
    state.tableData.loading = true;
    const res = await getRobotPlaceList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        placeName
    })
    const dataList = res.data.list
    const data = dataList
    state.tableData.data = data;
    state.tableData.total = res.data.total;
    state.tableData.loading = false;
};
// 打开新增场所弹窗
const onOpenAddDevice = (type: string) => {
    PlaceDialogRef.value.openDialog(type);
};
// 打开修改场所弹窗
const onOpenEditDevice = (type: string, row: rowPlaceType) => {
    PlaceDialogRef.value.openDialog(type, row);
};
// 删除场所
const onRowDel = (row: rowPlaceType) => {
    ElMessageBox.confirm(t('message.views.deletePlcaeConfirm', { placeName: row.placeName }), t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deleteRobotPlace(row._id)
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};
// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};

// DOM挂载完
onMounted(() => {
    getTableData();
});
</script>

<style scoped lang="scss">
.system-place-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-place-search {
    display: flex;
    align-content: space-around;
}
</style>
