<template>
	<el-dialog v-model="dialogTableVisible" :title="$t('message.views.MetricsInfo')">
    <div style="font-weight: bold; font-size: 16px; margin-bottom: 20px;">接收时间：{{ props.showrow.time }}</div>
		<el-table :data="props.showrow.sensorData.filter((item: any) => item.choose)" style="width: 100%">
			<el-table-column prop="sensorName" :label="$t('message.views.Metrics')"></el-table-column>
			<el-table-column :label="$t('message.views.MeasurementData')">
				<template #default="{ row }">
					{{ props.showrow.testsensorData[row.EnglishName] !== undefined && props.showrow.testsensorData[row.EnglishName] !== null ? props.showrow.testsensorData[row.EnglishName] : '暂无采样数据' }}
				</template>
				
			</el-table-column>
			<el-table-column :label="$t('message.views.MetricsRange')">
				<template #default="{ row }">
					
					{{ formatRange(row.sensorPlaceAndRange[0].Range,row.unit) }}
				</template>
			</el-table-column>
			<el-table-column :label="$t('message.views.Result')">
				<template #default="{ row }">
					<el-tag type="success" v-if="row.result === '达标' ">{{ row.result }}</el-tag>
					<el-tag type="danger" v-if="row.result === '未达标' || row.result === '超标'">{{ row.result }}</el-tag>
				</template>
			</el-table-column>
		</el-table>
	</el-dialog>

	<el-dialog v-model="dialogTableVisible2" :title="$t('message.views.MetricsInfo')">
    <div style="font-weight: bold; font-size: 16px; margin-bottom: 20px;">接收时间：{{ props.showNoRangerow.time }}</div>
		<el-descriptions :column="1" border>
			<el-descriptions-item v-for="item in sensorDataArray" :key="item.key" :label="item.key">{{ item.value }}</el-descriptions-item>
		</el-descriptions>
	</el-dialog>
</template>

<script setup lang="ts">
// import { useI18n } from 'vue-i18n';
import { ref,  onMounted,watch } from 'vue';
// const { t } = useI18n();
const props = defineProps({
	showrow: {
		type: Object,
		required: true,
        default: () => {},
	},
	showNoRangerow: {
		type: Object,
		required: true,
        default: () => {},
	},
});
watch(props,()=>{
  // console.log(props,'props');
	if(props.showrow){
		calculateResultAndSave(props.showrow.testsensorData,props.showrow)
	}
})
onMounted(() => {
	// console.log(props.showrow,'123');
});

const dialogTableVisible = ref(false);

const dialogTableVisible2 = ref(false);

const opendialogTableVisible = () => {
	dialogTableVisible.value = true;
};
const sensorData = ref([
	{ sensorName: '采样点', unit: '', EnglishName: 'sample_point' },
	{ sensorName: '温度', unit: '℃', EnglishName: 'temperature' },
	{ sensorName: '相对湿度', unit: '%RH', EnglishName: 'humidity' },
	{ sensorName: '风速', unit: 'm/s', EnglishName: 'windspeed' },
	{ sensorName: '噪声', unit: 'dB(A)', EnglishName: 'noise' },
	{ sensorName: '二氧化碳', unit: '%', EnglishName: 'co2' },
	{ sensorName: '甲醛', unit: 'mg/m3', EnglishName: 'ch2o' },
	{ sensorName: 'PM10', unit: 'mg/m3', EnglishName: 'PM10' },
	{ sensorName: 'PM2.5', unit: 'μg/m3', EnglishName: 'PM2_5' },
	{ sensorName: '一氧化碳', unit: 'mg/m3', EnglishName: 'co' },
	{ sensorName: 'TVOC', unit: 'mg/m3', EnglishName: 'TVOC' },
	{ sensorName: '大气压', unit: 'hPa', EnglishName: 'pressure' },
	{ sensorName: '采样结果', unit: '', EnglishName: 'result' },
]);
const sensorDataArray = ref();
const opendialogTableVisible2 = () => {
    sensorDataArray.value = Object.entries(props.showNoRangerow.testsensorData || props.showNoRangerow)
        .filter(([key]) => key !== 'sample_point' && key !== 'result') // 过滤掉不需要的字段
        .map(([key, value]) => {
            const sensor = sensorData.value.find((s: any) => s.EnglishName === key);
            return {
                key: sensor ? sensor.sensorName : key, // 转换为中文
                value: `${value} ${sensor ? sensor.unit : ''}`, // 添加单位
            };
        });

    dialogTableVisible2.value = true;
};
const formatRange = (range: {
  minValue: string | number;
  minValueType: string | number;
  maxValue: string | number;
  maxValueType: string | number;
},unit='') => {
  const { minValue, minValueType, maxValue, maxValueType } = range;

  const isMinEmpty = minValue === '' || minValue === '无限值';
  const isMaxEmpty = maxValue === '' || maxValue === '无限值';

  // 最小值文本
  const minText = isMinEmpty
    ? ''
    : maxValueType === 2
      ? `≥${minValue}${unit}`
      : `>${minValue}${unit}`;

  // 最大值文本
  const maxText = isMaxEmpty
    ? ''
    : minValueType === 2
      ? `≤${maxValue}${unit}`
      : `<${maxValue}${unit}`;

  // 组合逻辑
  if (isMinEmpty && isMaxEmpty) {
    return '无限值';
  } else if (isMinEmpty) {
    return maxText;
  } else if (isMaxEmpty) {
    return minText;
  } else {
    // 若两端都是“闭区间”才用 ~ 连接
    if (minValueType === 2 && maxValueType === 2) {
      return `${minValue}${unit}~${maxValue}${unit}`;
    } else {
      return `${minText}${unit} ~ ${maxText}${unit}`;
    }
  }
};
const calculateResultAndSave = (sensorData: Record<string, any>, placeInfo: any): string => {
	let allValid = true;
  let hasData = false;

  if (placeInfo?.sensorData) {
    for (const sensor of placeInfo.sensorData) {
      if (!sensor?.choose) continue;

      const sensorValue = sensorData[sensor.EnglishName];
      
      // 1. 无检测数据
      if (sensorValue === undefined || sensorValue === null) {
        sensor.result = ''; // 显式标记无结果
        continue;
      }

      hasData = true;
      const range = sensor.sensorPlaceAndRange?.[0]?.Range;
      if (!range) {
        sensor.result = '达标'; // 默认值
        continue;
      }

      // 2. 处理无限值
      if (range.minValue === '无限值' && range.maxValue === '无限值') {
        sensor.result = '达标';
        continue;
      }

      // 3. 计算逻辑（保持不变）
      const minValue = range.minValue !== '无限值' ? Number(range.minValue) : -Infinity;
      const maxValue = range.maxValue !== '无限值' ? Number(range.maxValue) : Infinity;
      const minValueType = Number(range.minValueType) || 1;
      const maxValueType = Number(range.maxValueType) || 1;

      let result = '达标';
      if (minValue !== -Infinity && maxValue !== Infinity) {
        const minValid = minValueType === 2 ? sensorValue >= minValue : sensorValue > minValue;
        const maxValid = maxValueType === 2 ? sensorValue <= maxValue : sensorValue < maxValue;
        result = minValid && maxValid ? '达标' : (minValid ? '超标' : '未达标');
      } 
      // ...其他判断逻辑...

      // 4. 保存结果到传感器数据
      sensor.result = result;
      if (result !== '达标') allValid = false;
    }
  }

  return hasData ? (allValid ? '合格' : '不合格') : '不合格';
};
defineExpose({
	opendialogTableVisible,
	opendialogTableVisible2,
});
</script>

<style scoped lang="scss">
</style>