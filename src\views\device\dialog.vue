<template>
	<div class="system-device-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="deviceDialogFormRef" :rules="rules" :model="state.dataFome" label-width="auto">
				<el-row :gutter="35">
					<!-- 设备名 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.deviceName')" prop="name">
							<el-input v-model="state.dataFome.name" :placeholder="$t('message.views.placeholderDeviceName')"
								clearable>
							</el-input>
						</el-form-item>
					</el-col>
					<!-- 设备编码 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.deviceNum')" prop="deviceNum">
							<el-input v-model="state.dataFome.deviceNum"
								:placeholder="$t('message.views.placeholderDeviceNum')" clearable>
							</el-input>
						</el-form-item>
					</el-col>
					<!-- 设备类型 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.deviceType')" prop="type">
							<el-select v-model="state.dataFome.type" value-key="type"
								:placeholder="$t('message.views.placeholderDeviceType')" clearable class="w100">
								<el-option v-for="item in deviceTypeList" :key="item.type" :label="item.type"
									:value="item.type">
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<!-- 数据单位 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.common.dataUnit')" prop="unit">
							<el-input :value="state.dataFome.unit" disabled></el-input>
						</el-form-item>
					</el-col>
					<!-- 阈值 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.common.threshold')" prop="threshold">
							<el-input v-model="state.dataFome.threshold"
								:placeholder="$t('message.views.placeholderDeviceThreshold')" clearable>
							</el-input>
						</el-form-item>
					</el-col>
					<!-- 安装场所 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.devicePlace')" prop="place">
							<el-select v-model="state.dataFome.publicPlaceId" value-key="type"
								:placeholder="$t('message.views.placeholderDevicePlcae')" clearable filterable class="w100">
								<el-option v-for="item in props.placeList" :key="item._id" :label="item.placeName"
									:value="item._id"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<!-- 是否启用 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.common.isAble')" prop="status">
							<el-switch v-model="state.dataFome.status" :active-value="1" :inactive-value="0" inline-prompt
								:active-text="$t('message.common.able')"
								:inactive-text="$t('message.common.unable')"></el-switch>
						</el-form-item>
					</el-col>
					<!-- 安装点位 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item :label="$t('message.views.devicePosition')" prop="position">
							<el-input v-model="state.dataFome.position"
								:placeholder="$t('message.views.placeholderDevicePosition')" clearable>
							</el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(deviceDialogFormRef)">
						{{ state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="deviceDialog">
import { reactive, ref, nextTick, onBeforeMount, watch, computed } from 'vue';
import { createDevice, updateDevice, getDeviceType } from "/@/api/device/index";
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 组件ref
const deviceDialogFormRef = ref();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// props 场所列表
const props = defineProps<{ placeList: { _id: string; placeName: string }[] }>()
// 定义变量内容
const useState = () => reactive<deviceDialogState>({
	dataFome: {
		_id: '',
		name: '',
		deviceNum: '',
		type: '',
		unit: '',
		threshold: undefined,
		publicPlaceId: '',
		position: '',
		status: 0,
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});
const state = useState()
// 设备类型列表
const deviceTypeList = reactive<deviceType[]>([])
// 校验规则
const rules = reactive({
	name: [{ required: true, message: computed(() => t('message.views.placeholderDeviceName')), trigger: 'blur' }],
	deviceNum: [{ required: true, message: computed(() => t('message.views.placeholderDeviceNum')), trigger: 'blur' }],
	type: [{ required: true, message: computed(() => t('message.views.placeholderDeviceType')), trigger: 'change' }],
	threshold: [{ required: true, message: computed(() => t('message.views.placeholderDeviceThreshold')), trigger: 'change' }],
})

// 监听设别类型，自动填入阈值
watch(
	() => state.dataFome.type,
	(newVal) => {
		// 选择某个类型
		if (newVal) {
			const foundType = deviceTypeList.find((item) => item.type === newVal);
			if (foundType == undefined) { return }
			// 如果row中没有数据，则填入deviceTypeList中相应类型对应的默认值
			if (state.dataFome.unit.length == 0) {
				state.dataFome.unit = foundType.unit as string
			}
			if (!state.dataFome.threshold) {
				state.dataFome.threshold = foundType.threshold
			}
		}
		// 清空
		else {
			state.dataFome.unit = ''
			state.dataFome.threshold = undefined
		}
	}
)

// 打开弹窗
const openDialog = (type: string, row: rowDeviceType) => {
	// 弹窗数据初始化
	if (type === 'update') {
		// console.log(row);
		state.dataFome = { ...row }

		state.dialog.type = 'update'
		state.dialog.title = t('message.views.deviceDialogUpdateTitle');
		state.dialog.submitTxt = t('message.common.modify');
	} else {
		Object.assign(state.dataFome, useState().dataFome)
		// state.dataFome._id = ''
		// state.dataFome.deviceNum = ''
		// state.dataFome.name = ''
		// state.dataFome.publicPlaceId = ''
		// state.dataFome.position = ''
		// state.dataFome.status = 0
		// state.dataFome.type = ''
		// state.dataFome.unit = ''
		// state.dataFome.threshold = undefined

		state.dialog.type = 'add'
		state.dialog.title = t('message.views.placeholderDevicePlcae');
		state.dialog.submitTxt = t('message.common.add');
	}
	state.dialog.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		deviceDialogFormRef.value.resetFields();
	});
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
	formEl.validate(async (valid) => {
		if (!valid) {
			return
		}
		let { deviceNum, name, status, position, publicPlaceId, type, unit, threshold } = state.dataFome
		// let { } = state.dataFome.deviceType
		const data = { deviceNum, name, status, position, type, unit, threshold, publicPlaceId }
		try {
			if (state.dialog.type === 'add') {
				await createDevice(data)
				ElMessage.success(t('message.common.addSuccess'))
			}
			else {
				// 如果是修改，参数需要包含_id
				await updateDevice(Object.assign(data, { _id: state.dataFome._id }))
				ElMessage.success(t('message.common.modifySuccess'))
			}
		} catch (error) {
			// console.log('@@@@@', error);
			ElMessage.error('操作失败！请检查输入')
			return
			// closeDialog();
		}
		closeDialog();
		emit('refresh');
	})
};

// 加载设备类型
const getDeviceTypeList = async () => {
	const res = await getDeviceType()
	Object.assign(deviceTypeList, res.data)
	// console.log(deviceTypeList);
}

onBeforeMount(() => {
	getDeviceTypeList()
})

// 暴露变量
defineExpose({
	openDialog,
});
</script>
