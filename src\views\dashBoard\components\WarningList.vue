<template>
    <div class="warningList-outerContainer">
        <warningListBorder title="预警处置列表">
            <div class="warningList-container">
                <div class="warningList-header">
                    <div style="width: 15%;"><span>序号</span></div>
                    <div style="width: 30%;"><span>预警时间</span></div>
                    <div style="width: 30%;"><span>预警类别</span></div>
                    <div style="width: 30%;"><span>设备名称</span></div>
                    <div style="width: 30%;"><span>场所</span></div>

                    <div style="width: 20%;"><span>预警状态</span></div>
                </div>
                <Vue3SeamlessScroll v-if="warningList.length > 0" ref="seamlessScrollRef" class="scroll-wrap"
                    :list="warningList" hover :step="0.2">
                    <div v-for="(item, index ) in warningList" :key="index" class="vedioContainer">
                        <div class="warningList-item">
                            <div style="width: 15%;"><span>{{ index + 1 }}</span></div>
                            <div style="width: 30%;"><span>{{ item.createdAt }}</span></div>
                            <el-tooltip class="box-item" effect="dark" placement="top">
                                <div style="width: 30%;">
                                    <span>{{item.WarningType}}</span>
                                </div>
                                <template #content>
                                    <div style="width: 30vw;">{{ item.content }}</div>
                                </template>
                            </el-tooltip>
                            <div style="width: 30%;"><span>{{ item.WarningType=='CO2'? 'CO2传感器':item.deviceNickName }}</span></div>
                            <div style="width: 30%;"><span>{{ item.publicPlace }}</span></div>
                            <div style="width: 20%;">
                                <span v-if="item.status">已解除</span>
                                <span v-else>待处理</span>
                            </div>
                        </div>
                    </div>
                </Vue3SeamlessScroll>
                <div v-else class="scroll-wrap">
                    <div class="warningList-item" style="display: flex;justify-content: center;">
                        <span>暂无数据</span>
                    </div>
                </div>
            </div>
        </warningListBorder>
    </div>
</template>


<script setup lang="ts" name="WarningList">
import { onMounted, ref } from 'vue';
import warningListBorder from "./warningListBorder.vue";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import { getRobotDeviceList} from "/@/api/robotDevice/index";
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '/@/stores/dashBoard';
const dashBoardStore = useDashBoardStore();
const { warningList } = storeToRefs(dashBoardStore);
// 设备类型列表
const DeviceNumList = ref<any[]>([])
const getDeviceNumList = async () => {
    const res = await getRobotDeviceList({
        page: 1,
        pageSize: 99999,
    })
    const data = [...res.data.list]

    DeviceNumList.value=data
    dashBoardStore.warningList = dashBoardStore.warningList.map(warning => {
        const matchedDevice = DeviceNumList.value.find(device => device.deviceName === warning.deviceId);
        let publicPlace = '未知场所';
        if (warning.content) {
            const placeMatch = warning.content.match(/场所 - (.+?)出现/);
            if (placeMatch && placeMatch[1]) {
                publicPlace = placeMatch[1];
            }
        }
        return {
            ...warning,
            publicPlace,
            deviceNickName: matchedDevice ? matchedDevice.deviceNickName : '未知设备'
        };
    });
};
onMounted(()=>{
    getDeviceNumList()
})
</script>

<style scoped lang="scss">
.warningList-outerContainer {
    width: 27vw;
    height: 30vh;

    .warningList-container {
        width: 100%;
        height: 100%;

        @media screen and (max-width: 3840px) {
            font-size: 20px;
        }

        @media screen and (max-width: 3440px) {
            font-size: 16px;
        }

        @media screen and (max-width: 2560px) {
            font-size: 12px;
        }

        @media screen and (max-width: 1960px) {
            font-size: 10px;
        }

        @media screen and (max-width: 1440px) {
            font-size: 8px;
        }

        @media screen and (max-width: 1024px) {
            font-size: 6px;
        }

        .warningList-header {
            width: 100%;
            font-size: 1em;
            height: 2.25em;
            line-height: 2.25em;
            background-color: rgba(15, 128, 131, 1);
            color: #FFFFFF;
            border-radius: 0.5em;
            padding: 0 0.66em;
            display: flex;

            justify-content: space-between
            span{
                font-size: 1em;
            }
        }

        .scroll-wrap {
            width: 100%;
            height: calc(100% - 3em);
            overflow: hidden;

            .warningList-item {
                width: 100%;
                font-size: 0.7em;
                height: 2.1em;
                line-height: 2.1em;
                background-color: rgba(14, 117, 121, 0.29);
                color: #FFFFFF;
                border-radius: 0.5em;
                margin-top: 0.33em;
                padding: 0 0.66em;
                display: flex;
                justify-content: space-between;
            }
        }
    }
}
</style>