<template>
    <div class="system-elaryWarning-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-elaryWarning-search mb15">
                <!-- <el-input v-model="state.keyWord" placeholder="请输入查询关键字" style="max-width: 240px" class="mr10" clearable>
                </el-input> -->
                <div>
                    <el-select v-model="state.publicPlaceId" :placeholder="$t('message.views.placeholderDevicePlcae')"
                        clearable filterable class="mr10">
                        <el-option v-for="item in mergedPlaceList" :key="item._id" :label="item.placeName"
                            :value="item._id"></el-option>
                    </el-select>
                    <el-select v-model="state.deviceId" :placeholder="$t('message.views.placeholderWarningDeviceNum')"
                        clearable filterable class="mr10">
                        <el-option v-for="item in mergedDeviceList" :key="item._id" :label="item.deviceNum"
                            :value="item._id"></el-option>
                    </el-select>

                    <el-select v-model="state.WarningType" 
                        :placeholder="$t('message.views.placeholderWarningType')" clearable filterable class="mr10">
                        <el-option  label="空气质量巡检" value="空气质量巡检">
                        </el-option>
                        <el-option  label="CO2" value="CO2">
                        </el-option>
                    </el-select>
                    <el-select v-model="state.selectedStatus" :placeholder="$t('message.views.placeholderWarningStatus')"
                        class="mr10" clearable>
                        <el-option :label="$t('message.common.all')" :value="''"></el-option>
                        <el-option :label="$t('message.views.pending')" :value="0"></el-option>
                        <el-option :label="$t('message.views.lifted')" :value="1"></el-option>
                    </el-select>
                    <el-button type="primary" @click="getTableData()">
                        <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }} </el-button>
                  
                </div>
                <div style="display: flex; align-items: center;">
                   <div >
                    <span >{{$t('message.views.sendmsg')}}</span>
                    <el-switch style="margin: 0 10px; " v-model="state.sendmsg"   @change="onSendMsgChange"/>
                   </div>
                    <el-button type="success" @click="router.push('/warningTextMessage')"> <el-icon><ele-Link /></el-icon>
                        预警短信</el-button>
                </div>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%">
                <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
                <el-table-column prop="WarningType" :label="$t('message.common.WarningType')" width="120" />
                <el-table-column prop="content" :label="$t('message.views.messageContent')" min-width="360"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="createdAt" :label="$t('message.common.createdAt')" show-overflow-tooltip
                    width="180"></el-table-column>
                <el-table-column prop="status" :label="$t('message.views.elaryWarningStatus')" width="120">
                    <template #default="scope">
                        <el-tag type="danger" v-if="scope.row.status == 0">{{ $t('message.views.pending') }}</el-tag>
                        <el-tag type="success" v-else>{{ $t('message.views.lifted') }}</el-tag>
                    </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column :label="$t('message.common.operation')" width="120">
                    <template #default="scope">
                        <el-button size="small" text type="primary" :disabled="scope.row.status == 1"
                            @click="onWarningLift(scope.row)">{{ $t('message.common.lift') }}</el-button>
                        <el-button size="small" text type="primary" @click="onRowDel(scope.row)">{{
                            $t('message.common.delete') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
    </div>
</template>

<script setup lang="ts" name="AirElaryWarning">
import { reactive, onMounted ,computed} from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getWarningList, liftWarning, deleteWarning,getSMSSwitch ,SMSSwitch} from "/@/api/warning";
import { getDeviceListAll, getDeviceType } from "/@/api/device";
import { getPlaceAll } from "/@/api/publicPlace";
import { getRobotDeviceList} from "/@/api/robotDevice/index";
import { getRobotPlaceList } from "/@/api/publicPlace/index";
import { useRouter } from 'vue-router';
const router = useRouter();
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 定义变量内容
const state = reactive<earlyWarnningState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 查询输入
    keyWord: '',
    deviceId: '',
    WarningType: '',
    publicPlaceId: '',
    sendmsg:false,
    selectedStatus: undefined
});
// 设备类型列表
const deviceTypeList = reactive<deviceType[]>([])
// 设备列表
const deviceList = reactive<{ _id: string; deviceNum: string; }[]>([])
// robot设备列表
const robotdeviceList = reactive<{ _id: string; deviceNum: string; }[]>([])
// 场所列表
const placeList = reactive<{ _id: string; placeName: string }[]>([])
// robot场所列表
const robotplaceList = reactive<{ _id: string; placeName: string }[]>([])
// 获取表格数据
const getTableData = async (
    deviceId: string | undefined = state.deviceId,
    WarningType: string | undefined = state.WarningType,
    publicPlaceId: string | undefined = state.publicPlaceId,
    status: number | undefined = state.selectedStatus
) => {
    state.tableData.loading = true;
    const res = await getWarningList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        deviceId, WarningType, publicPlaceId, status
    })
    const data = res.data.list
    state.tableData.data = data;
    state.tableData.total = res.data.count;
    state.tableData.loading = false;
};
// 加载设备类型
const getDeviceTypeList = async () => {
    const res = await getDeviceType()
    Object.assign(deviceTypeList, res.data)
}

const getRobotPlaceLists = async () => { 
    const res = await getRobotPlaceList({
        page: 1,
        pageSize: 99999,
        placeName:'',
    })
    Object.assign(robotplaceList,res.data.list )
}
const getDeviceNumList = async () => { 
    const res = await getRobotDeviceList({
        page: 1,
        pageSize: 99999,
        keyWord:'',
        status:'',
        deviceModel:'',
        deviceName:''
    })
    let data=res.data.list.map((item:any)=>{
        return {_id:item.deviceName,deviceNum:item.deviceNum}
    })
    Object.assign(robotdeviceList,data )
}
// 加载所有设备列表
const getdeviceNumList = async () => {
    const res = await getDeviceListAll()
    Object.assign(deviceList,res.data )
}
// 加载场所列表
const getPlaceList = async () => {
    const res = await getPlaceAll({ _type: 'public_place' })
    Object.assign(placeList, res.data)
}

// 解除预警
const onWarningLift = (row: RowEarlyWarnningType) => {
    ElMessageBox.confirm(t(`${row.WarningType==='CO2'?'message.views.liftWarningConfirm':'message.views.robotliftWarningConfirm'}`, { createdAt: row.createdAt }), t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await liftWarning({ _id: row._id,WarningType:row.WarningType })
    }).then(() => {
        ElMessage.success('解除成功！');
        getTableData();
    }).catch(() => {
        // console.log(error);
    });
}
// 删除预警
const onRowDel = (row: RowEarlyWarnningType) => {
    ElMessageBox.confirm(t('message.views.deleteWarningConfirm', { createdAt: row.createdAt }), t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deleteWarning({ _id: row._id,WarningType:row.WarningType })
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};

// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
const getSMSSwitchs= async ()=>{
    const res= await getSMSSwitch()
    state.sendmsg=res.sendmsg
}
const onSendMsgChange= async (val: boolean) => {
    await SMSSwitch({enabled:val})
    getSMSSwitchs()
}
// 页面加载时
onMounted(() => {
    getTableData();
    getDeviceTypeList();
    getdeviceNumList()
    getPlaceList();
    getDeviceNumList()
    getRobotPlaceLists()
    getSMSSwitchs()
});
const mergedDeviceList = computed(() => {
    return [...robotdeviceList, ...deviceList];
});
const mergedPlaceList = computed(() => {
    return [...robotplaceList, ...placeList];
});

</script>

<style scoped lang="scss">
.system-elaryWarning-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-elaryWarning-search {
    display: flex;
    // flex-wrap: wrap;
    padding-right: 12px;
    justify-content: space-between;
}
</style>
