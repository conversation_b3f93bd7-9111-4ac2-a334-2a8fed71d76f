<template>
    <div class="system-device-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-device-search mb15">
                <!-- 登录账号 -->
                <el-input v-model="searchParams.keyWord" :placeholder="'请输入用户名、姓名、手机号'" style="max-width: 200px" clearable>
                </el-input>
                <el-select v-model="searchParams.active" :placeholder="$t('message.views.userStatus')" style="max-width:200px" clearable filterable
                    class="ml10">
                    <el-option label="全部" :value="''"></el-option>
                    <el-option label="启用" :value="true"></el-option>
                    <el-option label="禁用" :value="false"></el-option>
                </el-select>
                <!-- <el-select v-model="searchParams.storeId" :placeholder="'门店'" style="max-width:200px" clearable filterable
                    class="ml10">
                    <el-option v-for="item in storeList" :key="item._id" :label="item.storeName"
                        :value="item._id"></el-option>
                </el-select> -->
                <!-- 查询按钮 -->
                <el-button type="primary" class="ml10" @click="getTableData()">
                    <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }}</el-button>
                <!-- 新增用户 -->
                <el-button type="success" class="ml10" @click="onOpenAddDevice('add')">
                    <el-icon><ele-FolderAdd /></el-icon>{{ $t('message.common.add') }}</el-button>
            </div>
            <el-table :data="tableData" v-loading="tableLoading" class="w100">
                <!-- 序号 -->
                <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
                <!-- 账号 -->
                <el-table-column prop="userName" :label="'登录账号'" show-overflow-tooltip></el-table-column>
                <!-- 用户姓名 -->
                <el-table-column prop="name" :label="$t('message.common.name')"></el-table-column>
                <!-- 门店名称 -->
                <el-table-column prop="enterpriseName" :label="'门店名称'" min-width="100" show-overflow-tooltip>
                    <!-- <template #default="scope">
                        <el-text>{{ findStore(scope.row.storeId) }}</el-text>
                    </template> -->
                </el-table-column>
                <!-- 用户角色 -->
                <el-table-column prop="type" :label="'用户角色'">
                    <template #default="scope">
                        <!-- <el-text v-if="scope.row.store.parentId">总店</el-text>
                        <el-text v-else>门店</el-text> -->
                        <span v-if="scope.row.role == 'placeHead'">主要负责人</span>
                        <span v-else>工程部负责人</span>
                    </template>
                </el-table-column>
                <!-- 手机号 -->
                <el-table-column prop="phoneNum" :label="'手机号'"></el-table-column>
                <!-- 用户状态 -->
                <el-table-column prop="active" :label="$t('message.views.userStatus')" min-width="86">
                    <template #default="scope">
                        <el-tag type="success" v-if="scope.row.active">{{ $t('message.common.enable') }}</el-tag>
                        <el-tag type="info" v-else>{{ $t('message.common.disable') }}</el-tag>
                    </template>
                </el-table-column>
                <!-- 操作 -->
                <el-table-column :label="$t('message.common.operation')" :width="themeConfig.globalI18n == 'en' ? 105 : 90">
                    <template #default="scope">
                        <el-button size="small" text type="primary" @click="onOpenEditDevice('update', scope.row)">{{
                            $t('message.common.modify') }}</el-button>
                        <el-button size="small" text type="primary" @click="onRowDel(scope.row)">{{
                            $t('message.common.delete') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="paginationParams.pageNum" background
                v-model:page-size="paginationParams.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="paginationParams.total">
            </el-pagination>
        </el-card>
        <xdjUserDialog ref="xdjUserDialogRef" :storeList="storeList" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="xdjUserMana">
import { defineAsyncComponent, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getPlaceAll } from "/@/api/publicPlace/index";
import { getUserList, deleteUsers } from "/@/api/user/index";

import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
// 
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

// 引入组件
const xdjUserDialog = defineAsyncComponent(() => import('/@/views/air/user/dialog.vue'));

// 门店列表
const storeList = ref<StoreListRow[]>([])

// 根据门店ID获取对应门店名称
// const findStore = (_id: string) => {
//     return storeList.value.find((ele) => ele._id === _id)?.placeName
// }

// 查询参数
const searchParams = ref<StoreSearchParams>({
    keyWord: '',
    active: undefined,
    storeId: undefined,
})
// table
const tableLoading = ref(false)
const tableData = ref<any[]>([])
// pagination
const paginationParams = ref<paginationType>({
    total: 0,
    pageNum: 1,
    pageSize: 10,
})

// 获取表格数据
const getTableData = async () => {
    tableLoading.value = true;
    const res = await getUserList({
        page: paginationParams.value.pageNum,
        pageSize: paginationParams.value.pageSize,
        ...searchParams.value,
        roles: ['placeHead', 'engineeringHead']
    })
    tableData.value = res.data.list;

    paginationParams.value.total = res.data.count;
    tableLoading.value = false;
};

// 删除管理员
const onRowDel = (row: any) => {
    ElMessageBox.confirm(`此操作将永久删除场所管理员（${row.name}），是否继续？`, t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deleteUsers({ id: row._id })
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};

// 表格列行号
const indexMethod = (index: number) => {
    return (paginationParams.value.pageNum - 1) * paginationParams.value.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    paginationParams.value.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    paginationParams.value.pageNum = val;
    getTableData();
};

// 弹窗组件ref
const xdjUserDialogRef = ref();
// 打开新增设备弹窗
const onOpenAddDevice = (type: string) => {
    xdjUserDialogRef.value.openDialog(type);
};
// 打开修改设备弹窗
const onOpenEditDevice = (type: string, row: rowDeviceType) => {
    xdjUserDialogRef.value.openDialog(type, row);
};

// 页面加载时
onMounted(async () => {
    getTableData();
    const res = await getPlaceAll({ _type: 'public_place' })
    storeList.value = res.data
});
</script>

<style scoped lang="scss">
.system-device-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-device-search {
    display: flex;
    align-content: space-around;
}
</style>
