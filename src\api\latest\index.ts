import request from '/@/utils/request';


export function getAllDevicesWithLatestData() {
    return request({
        url: '/latest/devicesAndLatestData',
        method: 'get',
    });
}

export function getAllRobotData(query:API.filteredRobotDataParams) {
    return request({
        url: '/latest/filteredRobotData',
        method: 'get',
        params: query,
    });
}

export function getMeasuredPlacesByDeviceName(query:API.measuredPlacesParams) {
    return request({
        url: '/latest/measuredPlaces',
        method: 'get',
        params: query,
    });
}

export function getRobotDataByDeviceAndPlace(query:API.robotDataByDeviceAndPlaceParams) {
    return request({
        url: '/latest/robotDataByDeviceAndPlace',
        method: 'get',
        params: query,
    });
}

export function getLatestRobotDataByPlace(query:{robotPubilcPlace:string}) {
    return request({
        url: '/latest/getLatestRobotDataByPlace',
        method: 'get',
        params: query,
    });
}