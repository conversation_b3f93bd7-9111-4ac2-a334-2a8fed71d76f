export function downFileByA(filename: string, url: string) {
    let a = document.createElement('a');
    // a.style = 'display: none'; // 创建一个隐藏的a标签
    a.download = filename;
    a.href = url;
    document.body.appendChild(a);
    a.click(); // 触发a标签的click事件
    document.body.removeChild(a);
}

/**
 * 根据环境返回绝对路径
 * @param url 请求路径
 * @returns baseUrl(VITE_API_URL) + url
 */
export function getFullUrl(url: string) {
    const envBaseUrl = import.meta.env.VITE_API_URL
    // return envBaseUrl === '/api/' ? '*************:3000' + url : envBaseUrl + url
    return envBaseUrl + url;
}

// *************
// localhost

/**
 * 根据环境返回路径，开发环境返回完整路径，生产环境返回相对路径
 * @param url 请求路径
 * @returns 'http://*************:3000' + url / url
 */
export function getUrlByEnv(url: string) {
    const envBaseUrl = import.meta.env.VITE_API_URL
    return envBaseUrl === '/api/' ? '*************:3000' + url : url
}

// 读取到 文件 ，使用 FileReader 将其转换为 base64 编码
const fileToDataURL = (file: Blob): Promise<any> => {
    return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onloadend = (e) => resolve((e.target as FileReader).result)
        reader.readAsDataURL(file)
    })
}
// 新建 img ，使其 src 指向刚刚的 base64
const dataURLToImage = (dataURL: string): Promise<HTMLImageElement> => {
    return new Promise((resolve) => {
        const img = new Image()
        img.onload = () => resolve(img)
        img.src = dataURL
    })
}
// 新建 canvas ，将 img 画到 canvas 上
const canvastoFile = (canvas: HTMLCanvasElement, type: string, quality: number): Promise<Blob | null> => {
    return new Promise((resolve) => canvas.toBlob((blob) => resolve(blob), type, quality))
}
/**
 * 图片压缩方法
 * @param {Object}  file 图片文件
 * @param {String} type 想压缩成的文件类型
 * @param {Nubmber} quality 压缩质量参数
 * @returns 压缩后的新图片
 */
export const compressionFile = async (file: Blob, type = 'image/jpeg', quality = 0.5) => {
    const fileName = file.name
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d') as CanvasRenderingContext2D
    const base64 = await fileToDataURL(file)
    const img = await dataURLToImage(base64)
    canvas.width = img.width
    canvas.height = img.height
    context.clearRect(0, 0, img.width, img.height)
    // 在canvas绘制前填充白色背景(模拟png图片背景)
    context.fillStyle = '#fff'
    context.fillRect(0, 0, img.width, img.height)
    context.drawImage(img, 0, 0, img.width, img.height)
    const blob = (await canvastoFile(canvas, type, quality)) as Blob // quality:0.5可根据实际情况计算
    const newFile = await new File([blob], fileName, {
        type: type
    })
    return newFile
}

/**
 * 图片压缩方法
 * @param {Object} file 图片文件
 * @param {Nubmber} size 能接受的图片最大大小（单位：Byte
 * @returns 压缩后的新图片
 */
export const compressFileSize = async (file: Blob, size: number): Promise<Blob> => {
    if (file.size < size) {
        return file
    }
    else if (file.size > size * 10) {
        return await compressionFile(file, 'image/jpeg', 0.2)
    }
    else {
        return await compressionFile(file)
    }
}