<template>
	<div class="w100 h100">
		<AsideTopBar title="在线监测设备" />
		<div class="expot newdata" @click="exportToExcel">导出实时数据</div>

		<div class="w100 h100" style="padding-right: 1vw; position: relative; top: -1vh">
			<PointMonitor
				v-if="airParams.Ytype === 'time'"
				title="空气巡航"
				:xAxisData="airData.xAxisData"
				:seriesData="airData.seriesData"
				:threshold="airData.threshold"
				:series="robotData"
				:airParams="airParams"
			>
				<div style="display: flex; justify-content: space-between">
					<div style="display: flex; width: 80%">
						<div >
							<span
								>指标:
								<div class="PointMonitor-btn">
									<el-select v-model="airParams.sensorType" placeholder="请选择" :teleported="false">
										<el-option :label="item.sensorName" v-for="item in dashBoardStore.sensorData" :key="item.EnglishName" :value="item.EnglishName" />
									</el-select>
								</div>
							</span>
						</div>
						<div style="margin: 0 10px">
							<span
								>场所:
								<div class="PointMonitor-btn cs">
									<el-select
										v-model="airParams.robotPubilcPlace"
										placeholder="请选择"
										:teleported="false"
										@change="dashBoardStore.getRobotAllDatas(year.getFullYear() + '')"
									>
										<el-option v-for="item in dashBoardStore.RobotPlaceList" :key="item._id" :label="item.placeName" :value="item._id" />
									</el-select>
								</div>
							</span>
						</div>

						<div>
							<span
								>时间跨度:
								<div class="PointMonitor-btn">
									<el-select v-model="airParams.timeType" placeholder="请选择" :teleported="false">
										<el-option label="月" value="month_data" />
										<el-option label="日" value="day_data" />
										<el-option label="时" value="hour_data" />
										<el-option label="5分" value="minute5_data" />
									</el-select>
								</div>
							</span>
						</div>
					</div>
					<div style="flex:1">
						<div>
							<span
								>横轴:
								<div class="PointMonitor-btn">
									<el-select v-model="airParams.Ytype" placeholder="请选择" :teleported="false">
										<el-option label="时间" value="time" />
										<el-option label="场所" value="place" />
										<el-option label="设备" value="device" />
										<el-option label="采样点" value="point" />
									</el-select>
								</div>
							</span>
						</div>
					</div>
				</div>
			</PointMonitor>
			<!-- 柱状图 -->
			<PlacePointMonitor
				v-if="airParams.Ytype != 'time'"
				title="空气巡航"
				:xAxisData="airData.xAxisData"
				:seriesData="airData.seriesData"
				:threshold="airData.threshold"
				:series="robotDataList"
				:airParams="airParams"
				:selectPlaceName="selectPlaceName"
			>
				<div style="display: flex; justify-content: space-between">
					<div>
						<span
							>指标:
							<div class="PointMonitor-btn">
								<el-select v-model="airParams.sensorType" placeholder="请选择" :teleported="false">
									<el-option :label="item.sensorName" v-for="item in dashBoardStore.sensorData" :key="item.EnglishName" :value="item.EnglishName" />
								</el-select>
							</div>
						</span>
					</div>
					<div v-show="airParams.Ytype == 'point'">
						<span
							>采样点场所:
							<div class="PointMonitor-btn cs">
								<el-select v-model="selectPlaceName" placeholder="请选择" :teleported="false">
									<el-option :label="item.placeName" v-for="item in robotDataList" :key="item.EnglishName" :value="item.placeName" />
								</el-select>
							</div>
						</span>
					</div>
					<div>
						<span
							>横轴:
							<div class="PointMonitor-btn">
								<el-select v-model="airParams.Ytype" placeholder="请选择" :teleported="false">
									<el-option label="时间" value="time" />
									<el-option label="场所" value="place" />
									<el-option label="设备" value="device" />
									<el-option label="采样点" value="point" />
								</el-select>
							</div>
						</span>
					</div>
				</div>
			</PlacePointMonitor>

			<PointMonitor
				title="末梢水"
				:xAxisData="waterData.xAxisData"
				:series="waterData.seriesData"
				:threshold="waterData.threshold"
				:xmid="waterParams.xmid"
			>
				<div style="display: flex">
					<div style="width: 30%">
						<span
							>指标:
							<div class="PointMonitor-btn">
								<el-select v-model="waterParams.xmid" placeholder="空" :teleported="false">
									<el-option v-for="item in xmidList" :key="item.xmid" :label="item.name" :value="item.xmid" />
								</el-select>
							</div>
						</span>
					</div>
					<div style="width: 30%">
						<span
							>场所:
							<div class="PointMonitor-btn">
								<el-select v-model="waterParams.zbh" placeholder="请选择" :teleported="false">
									<el-option v-for="item in waterStatistics" :key="item.zbh" :label="item.name" :value="item.zbh" />
								</el-select>
							</div>
						</span>
					</div>
					
					<div >
						<span>时间跨度:5min</span>
					</div>
				</div>
			</PointMonitor>

			<PointMonitor title="公共场所CO2" :series="CO2Data">
				<div style="display: flex">
					<div style="width: 30%">
						<span>监测点位:{{ CO2Params.monitorPoint }}</span>
					</div>
					<div style="width: 30%"><span>监测内容:CO₂</span></div>
					<div style="width: 30%">
						<span
							>时间跨度:
							<div class="PointMonitor-btn">
								<el-select v-model="CO2Params.timeType" placeholder="请选择" :teleported="false">
									<el-option label="月" value="month_data" />
									<el-option label="日" value="day_data" />
									<el-option label="时" value="hour_data" />
									<el-option label="5分" value="minute5_data" />
								</el-select>
							</div>
						</span>
					</div>
				</div>
			</PointMonitor>
		</div>
	</div>
</template>

<script setup lang="ts" name="center">
import * as XLSX from 'xlsx';
import { getAllDevicesWithLatestData } from '/@/api/latest/index';
import AsideTopBar from './AsideTopBar.vue';
import PointMonitor from './PointMonitor.vue';
import PlacePointMonitor from './PlacePointMonitor.vue';
import { ref, watch, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '/@/stores/dashBoard';
const dashBoardStore = useDashBoardStore();
const { year, airParams, robotData } = storeToRefs(dashBoardStore);

import { getRobotAllDataByPlace, getRobotAllDataByDevice, getRobotAllDataBySamplePoint } from '/@/api/robotHistory/index';

// 空气巡航图表数据
const airData = ref<any>({
	xAxisData: [],
	seriesData: [],
	threshold: [],
});

const selectPlaceName=ref<string>('')
watch(
	airParams,
	() => {
		// getAirData();
	},
	{ immediate: true, deep: true }
);

// 末梢水
import { getWaterData } from '/@/api/dashBoard';
const { waterStatistics } = storeToRefs(dashBoardStore);
// 监测内容
const xmidList = ref<{ xmid: string; name: string; threshold: number; threshold2?: number }>({});
// 末梢水查询选择参数
const waterParams = ref({
	zbh: '',
	xmid: '',
});
// 末梢水图表数据
const waterData = ref({
	xAxisData: [] as any[],
	seriesData: [] as any[],
	threshold: [] as any[],
});
// 获取末梢水数据
const getWaterDataList = async () => {
	if (!(waterParams.value.zbh && waterParams.value.xmid)) {
		return;
	}
	const res = await getWaterData(waterParams.value);
	const xAxisData = Object.keys(res.data);
	const seriesData = [];
	for (let i = 0; i < xAxisData.length; i++) {
		const element = res.data[xAxisData[i]];
		seriesData.push(element);
	}
	waterData.value.xAxisData = xAxisData;
	waterData.value.seriesData = seriesData;
	// 获取阈值
	const xmid = xmidList.value.find((item:any) => item.xmid == waterParams.value.xmid);
	const thresholdList = [];
	xmid.threshold && thresholdList.push(xmid.threshold);
	xmid.threshold2 && thresholdList.push(xmid.threshold2);
	waterData.value.threshold = thresholdList;
};
// 监听store，当末梢水点位信息获取后默认选择第一项
watch(waterStatistics, () => {
	if (waterStatistics.value[0].statistics) {
		waterParams.value.zbh = waterStatistics.value[0].zbh;
		xmidList.value = waterStatistics.value[0].statistics;
		waterParams.value.xmid = xmidList.value[0].xmid;
	}
});
// 监听末梢水选项，改变后立刻获取数据
watch(
	waterParams,
	() => {
		getWaterDataList();
	},
	{ deep: true }
);

// 二氧化碳
import { getDeviceList } from '/@/api/device';
import { getDetectionData } from '/@/api/air';
// 查询选择参数
const CO2Params = ref({
	monitorPoint: '',
	timeType: 'hour_data',
});
// 二氧化碳图表数据
const CO2Data = ref<any>([]);
// 获取设备信息 设置点位和阈值
const getDeviceInfoData = async () => {
	const res = await getDeviceList({
		page: 1,
		pageSize: 99,
		keyWord: 'SHXH_CO2_23111301',
	});
	if (!res.data || res.data.list.length < 1) {
		return;
	}
	const device = res.data.list[res.data.list.length - 1];
	// 点位
	CO2Params.value.monitorPoint = device.publicPlace.placeName;
};
const CO2StartTime = () => year.value.toString();
const CO2EndTime = () => new Date(`${year.value.getFullYear() + 1}`).toString();
// 获取折线图数据
const getCO2Data = async (dataType: string = CO2Params.value.timeType, startTime: string = CO2StartTime(), endTime: string = CO2EndTime()) => {
	const res = await getDetectionData({
		deviceNum: 'SHXH_CO2_23111301',
		dataType,
		startTime,
		endTime,
	});
	CO2Data.value = res.data.map((item: any) => {
		return {
			time: item.time,
			value: item.data,
		};
	});
};
// 监听二氧化碳时间跨度
watch(
	[CO2Params, year],
	() => {
		getCO2Data();
	},
	{ deep: true }
);
getDeviceInfoData();
getCO2Data();

const robotDataList = ref<any>([]);
const isUpdatingRobotData = ref(false); // 添加状态标志
// 监听机器
watch(
  [ airParams],
  () => {
    // 如果正在更新数据，则跳过处理
    if (isUpdatingRobotData.value) return;

    // 标记开始更新
    isUpdatingRobotData.value = true;

    try {
      if (airParams.value.Ytype === 'place') {
        getRobotAllDataByPlaces();
      } else if (airParams.value.Ytype === 'device') {
        getRobotAllDataByDevices();
      } else if (airParams.value.Ytype === 'point') {
        getRobotAllDataBySamplePoints();
      } else if (robotData.value.length > 0 && airParams.value.Ytype == 'time') {
		dashBoardStore.getRobotAllDatas(year.value.getFullYear() + '')
      } else {
        robotDataList.value = [];
      }
    } finally {
      // 确保最终解除锁定
      isUpdatingRobotData.value = false;
    }
  },
  { deep: true }
);
const getRobotAllDataByPlaces = async () => {
	const res = await getRobotAllDataByPlace({ sensorType: airParams.value.sensorType, year: year.value.getFullYear() + '' });
	robotDataList.value = res.data.map((item: any) => ({
		label: item.placeName,
		value: item.value,
	}));
};
const getRobotAllDataByDevices = async () => {
	const res = await getRobotAllDataByDevice({ sensorType: airParams.value.sensorType, year: year.value.getFullYear() + '' });
	robotDataList.value = res.data.map((item: any) => ({
		label: item.deviceNickName,
		value: item.value,
	}));
};

const getRobotAllDataBySamplePoints = async () => {
	const res = await getRobotAllDataBySamplePoint({ sensorType: airParams.value.sensorType, year: year.value.getFullYear() + '' });
	robotDataList.value = res.data;
	selectPlaceName.value=robotDataList.value[0].placeName
};


dashBoardStore.getSensorDatas();
dashBoardStore.getRobotPlaceLists();
const DevicesWithLatestDatas = ref<any>({
	robotDevices: [],
	devices: [],
});
// 导出数据为 Excel
const exportToExcel = async () => {
	try {
		const res = await getAllDevicesWithLatestData();
		DevicesWithLatestDatas.value = res.data;

		// 处理 robotDevices 数据（过滤无 latestRobotData 的设备）
		const processedData = res.data.robotDevices.filter((item: any) => item.latestRobotData !== null); // 过滤掉无 latestRobotData 的设备

		// console.log(processedData,'processedData');
		
		DevicesWithLatestDatas.value.robotDevices = processedData;

		// 3. 格式化 devices 数据
		const formattedDevices = DevicesWithLatestDatas.value.devices.map((device: any) => ({
			检测时间: new Date(device.latestDetectedData.time).toLocaleString(),
			设备名称: device.name,
			设备编号: device.deviceNum,
			位置: device.position,
			最新检测值: device.latestDetectedData.data,
			阈值: device.threshold,
			单位: device.unit,
			状态: device.status === 1 ? '正常' : '异常',
		}));

		// 4. 格式化 robotDevices 数据（动态处理 sensorData）
		const formattedRobotDevices = DevicesWithLatestDatas.value.robotDevices.map((robot: any) => {
			const baseData: Record<string, any> = {
				检测时间: new Date(robot.latestRobotData.time).toLocaleString(),
				设备名称: robot.deviceNickName,
				设备编号: robot.deviceNum,
				设备型号: robot.deviceModel,
				任务编号: robot.latestRobotData.taskNum,
				场所名称: robot.robotPublicPlace.placeName,
				检测结果: robot.result,
				采样点: robot.latestRobotData.sensorData.sample_point,
			};

			// 动态添加 sensorData 指标（中文名称 + 单位）
			if (robot.latestRobotData && robot.latestRobotData.sensorData && robot.robotPublicPlace && robot.robotPublicPlace.sensorData) {
				robot.robotPublicPlace.sensorData.forEach((sensor: any) => {
					const englishName = sensor.EnglishName;
					const value = robot.latestRobotData.sensorData[englishName];
					if (value !== undefined) {
						baseData[`${sensor.sensorName} (${sensor.unit})`] = value;
					}
				});
			}

			return baseData;
		});

		// 5. 创建工作簿
		const workbook = XLSX.utils.book_new();

		// 6. 添加 devices 工作表
		const devicesSheet = XLSX.utils.json_to_sheet(formattedDevices);
		XLSX.utils.book_append_sheet(workbook, devicesSheet, 'CO2');

		// 7. 添加 robotDevices 工作表
		const robotDevicesSheet = XLSX.utils.json_to_sheet(formattedRobotDevices);
		XLSX.utils.book_append_sheet(workbook, robotDevicesSheet, '机器人设备');

		// 8. 导出为 Excel 文件
		const fileName = `实时数据_${new Date().toLocaleString()}.xlsx`;
		XLSX.writeFile(workbook, fileName);
	} catch (error) {
		// console.error('导出失败:', error);
	}
};
// 场所列表
// 页面加载时
onMounted(() => {});
</script>

<style scoped lang="scss">
.PointMonitor-btn {
	display: inline;

	:deep(.el-input) {
		@media screen and (max-width: 2560px) {
			font-size: 12px;
		}

		@media screen and (max-width: 1960px) {
			font-size: 10px;
		}

		@media screen and (max-width: 1440px) {
			font-size: 8px;
		}

		@media screen and (max-width: 1024px) {
			font-size: 6px;
		}

		top: -1px;
	}

	:deep(.el-input__wrapper) {
		background-color: transparent;
		padding: 0 !important;
		box-shadow: none;
	}

	:deep(.el-input__inner) {
		width: calc(4em + 2px);
		height: calc(1.2em + 1px);
		line-height: calc(1.2em + 1px);
		-webkit-appearance: none;
		background-color: transparent;

		border: none;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		font-size: 1em !important;
		font-family: Source Han Sans CN, Source Han Sans CN-Medium;
		font-weight: Medium;
		text-align: left;
		color: #18caca;
		display: inline-block;
		font-size: inherit;
		outline: 0;
		padding: 0;
		-webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
	}

	:deep(.el-select .el-input.is-focus .el-input__wrapper) {
		box-shadow: none;
	}

	:deep(.el-input__suffix-inner > :first-child) {
		margin-left: 0px;
	}

	// .el-button--primary:focus,
	// .el-button--primary:hover {
	//     background-color: transparent;
	//     width: 51px;
	//     height: 33px;
	//     border: none;
	//     //放大动效
	//     transform: scale(1.1);
	// }
}
.cs{
	:deep(.el-input__inner) {
		width: calc(6em + 2px);
		height: calc(1.2em + 1px);
		line-height: calc(1.2em + 1px);
		-webkit-appearance: none;
		background-color: transparent;

		border: none;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		font-size: 1em !important;
		font-family: Source Han Sans CN, Source Han Sans CN-Medium;
		font-weight: Medium;
		text-align: left;
		color: #18caca;
		display: inline-block;
		font-size: inherit;
		outline: 0;
		padding: 0;
		-webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
	}
}

.expot {
	@media screen and (max-width: 3840px) {
		font-size: 20px;
	}

	@media screen and (max-width: 3440px) {
		font-size: 16px;
	}

	@media screen and (max-width: 2560px) {
		font-size: 12px;
	}

	@media screen and (max-width: 1960px) {
		font-size: 10px;
	}

	@media screen and (max-width: 1440px) {
		font-size: 8px;
	}

	@media screen and (max-width: 1024px) {
		font-size: 6px;
	}
	position: absolute;
	box-shadow: none;
	background-color: transparent;
	padding: 0 0.5vw;
	width: 5vw;
	height: 1.3vw;
	display: flex;
	align-items: center;
	justify-content: center;
	background-image: url(../../../../src/assets/dashBoard/yearSelect.svg);
	background-repeat: no-repeat;
	background-size: auto 100%;
	cursor: pointer;
}
.newdata {
	right: 1vw;
	top: 7vh;
	color: #18caca;
}
</style>