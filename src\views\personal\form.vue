<template>
    <el-card shadow="hover" class="mt15 personal-edit" header="更新信息">
        <!-- <div class="personal-edit-title">基本信息</div> -->
        <el-form ref="useUpdateFormRef" :model="state.updateFrom" :rules="rules" size="default" label-width="60px"
            class="mt5 mb15">
            <el-row :gutter="35">
                <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb20">
                    <el-form-item :label="$t('message.common.name')">
                        <el-input v-model="state.updateFrom.name" placeholder="请输入姓名" clearable></el-input>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb20">
                    <el-form-item :label="$t('message.common.password')" prop="password">
                        <el-input v-model="state.updateFrom.password" placeholder="请输入密码" type="password" clearable
                            show-password></el-input>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb20">
                    <el-form-item :label="$t('message.common.phoneNum')" prop="phoneNum">
                        <el-input v-model="state.updateFrom.phoneNum" placeholder="请输入手机" clearable></el-input>
                    </el-form-item>
                </el-col>

            </el-row>

            <el-row>
                <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb20">
                    <el-form-item label="头像" prop="state.updateFrom.imageUrl">
                        <el-upload ref="upload" class="avatar-uploader" action="#" list-type="picture-card"
                            :auto-upload="false" :multiple="false" :limit="1" :on-change="uploadFile"
                            :show-file-list="false" :on-exceed="handleExceed" :on-remove="handleRemove" accept="image/*">
                            <img v-if="state.updateFrom.imageUrl" :src="state.updateFrom.imageUrl" class="avatar" />
                            <el-icon v-else class="avatar-uploader-icon">
                                <Plus />
                            </el-icon>

                            <!-- <template #file="{ file }">
                                <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                </span>
                            </template> -->
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col>
                    <el-form-item>
                        <el-button type="primary" @click="updateUserInfo(useUpdateFormRef)">
                            <el-icon>
                                <ele-Position />
                            </el-icon>
                            更新个人信息
                        </el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </el-card>
</template>

<script setup lang="ts">

import { reactive, ref } from 'vue';
import type { FormInstance, UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, genFileId } from "element-plus";
import { compressFileSize } from "/@/utils/common";
import { updateUsers } from '/@/api/user/index'
import { Session } from '/@/utils/storage';
const userInfo = Session.get('userInfo')

import { useUserInfo } from '/@/stores/userInfo';
const storesUserInfo = useUserInfo();

// 组件ref
const useUpdateFormRef = ref();
const upload = ref<UploadInstance>()
// 
const props = defineProps(['personalForm'])
const emit = defineEmits(['refresh'])

// 定义变量内容
const useState = () => reactive<PersonalUpdateFormState>({
    updateFrom: {
        name: '',
        password: '',
        phoneNum: '',
        file: undefined,
        imageUrl: ''
    }
});
const state = useState()

// 校验规则
const rules = reactive({
    password: [{ min: 6, max: 16, message: '密码的长度应在6到16之间', trigger: 'change' },],
    phoneNum: [{ pattern: /^1[3456789]\d{9}$/, message: '手机号码格式不正确', trigger: 'blur' }]
})

// upload文件替换
const handleExceed: UploadProps['onExceed'] = (files) => {
    upload.value!.clearFiles()
    const file = files[0] as UploadRawFile
    file.uid = genFileId()
    upload.value!.handleStart(file)
}

// 取消选择，清除state
const handleRemove = () => {
    state.updateFrom.file = undefined;
    state.updateFrom.imageUrl = undefined;
}

// 更新用户信息
const updateUserInfo = (formEl: FormInstance) => {
    // 表单验证
    formEl.validate(async (valid) => {
        if (!valid) {
            return
        }

        // 图片压缩
        if (state.updateFrom.file) {
            state.updateFrom.file = await compressFileSize(state.updateFrom.file, 1024 * 1024)
            // const res = await compressFileSize(state.updateFrom.file, 1024 * 1024)
            // console.log(res);
        }
        // console.log(state.updateFrom.file);

        const formData = new FormData();
        // 添加图片文件
        formData.append('avatar', state.updateFrom.file as Blob);
        // 添加其他表单数据
        formData.append('_id', userInfo._id);
        formData.append('name', state.updateFrom.name || props.personalForm.name);
        formData.append('password', state.updateFrom.password);
        formData.append('phoneNum', state.updateFrom.phoneNum || props.personalForm.phoneNum);
        formData.append('active', props.personalForm.active);
        // 发送请求
        updateUsers(formData)
            .then(() => {
                ElMessage.success('更新成功！')
                state.updateFrom.name = ''
                state.updateFrom.password = ''
                state.updateFrom.phoneNum = ''
                emit('refresh')
                storesUserInfo.setUserInfos(true);
                Object.assign(state, useState())
            })
            .catch((rea): void => {
                ElMessage.warning(rea)
            })
    })
}

const uploadFile = (item: Blob) => {
    state.updateFrom.file = item.raw; // 图片文件
    state.updateFrom.imageUrl = URL.createObjectURL(item.raw); // 图片上传浏览器回显地址
}

</script>


<style scoped lang="scss">
.personal-edit {
    .personal-edit-title {
        position: relative;
        padding-left: 10px;
        color: var(--el-text-color-regular);

        &::after {
            content: '';
            width: 2px;
            height: 10px;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background: var(--el-color-primary);
        }
    }

}

.avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
}

// .avatar-uploader {
//     border: 1px dashed var(--el-border-color);
//     border-radius: 6px;
//     cursor: pointer;
//     position: relative;
//     overflow: hidden;
//     transition: var(--el-transition-duration-fast);

//     &:hover {
//         border-color: var(--el-color-primary);
//     }
// }

// .avatar-uploader-icon {
//     font-size: 28px;
//     color: #8c939d;
//     width: 178px;
//     height: 178px;
//     text-align: center;
// }
</style>

<style>
.avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
}
</style>