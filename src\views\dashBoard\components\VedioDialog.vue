<template>
    <div class="system-device-dialog-container">
        <el-dialog :title="vedioItem.title" v-model="isShowDialog" width="auto">
            <img :src="vedioItem.imgUrl" style="height: 60vh;">
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="deviceDialog">
import { ref } from 'vue';

const isShowDialog = ref(false)
const vedioItem = ref<{ title?: string; imgUrl?: any }>({ title: '', imgUrl: null })

// 打开弹窗
const openDialog = (item: any) => {
    isShowDialog.value = true;
    vedioItem.value = item
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
