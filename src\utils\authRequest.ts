import request from '/@/utils/request';
import { AxiosRequestConfig } from 'axios';
import { useUserInfo } from '/@/stores/userInfo';
const stores = useUserInfo();

export function authRequest(option: AxiosRequestConfig<any>, value: string[]): Promise<any> {
    if (value.indexOf(stores.userInfos.roles[0]) > -1) {
        return request(option)
    }
    else return Promise.reject(stores.userInfos.roles[0] + '无权限访问' + option.url)
}