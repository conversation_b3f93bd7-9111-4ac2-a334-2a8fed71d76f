import request from '/@/utils/request';

/**
 *  最新数据 - 获取检测数据（折线图可视化）
 */
export function getDetectionData(query: API.DetectionDataParams) {
    return request({
        url: '/statistical-data',
        method: 'get',
        params: query,
    });
}

/**
 *  历史数据 - 获取检测数据列表
 */
export function getDetectionDataList(query: API.DetectionDataListParams) {
    return request({
        url: '/detected-data/list',
        method: 'get',
        params: query,
    });
}

/**
 *  历史数据 - 获取检测数据时间段内全部数据（用于excel导出）
 */
export function getDetectionDataAll(query: API.DetectionDataAllParams) {
    return request({
        url: '/detected-data/all',
        method: 'get',
        params: query,
    });
}