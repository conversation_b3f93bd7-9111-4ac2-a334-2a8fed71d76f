// common

/**
 * 最新消息
 */
type NewInfo = {
	title: string;
	date: string;
	link: string;
};

/**
 * 对话框配置
 */
type dialogConfig = {
	isShowDialog: boolean;
	type: string;
	title: string;
	submitTxt: string;
}

/**
 * 设备类型
 */
declare type deviceType = {
	type?: string;
	unit?: string;
	threshold?: number;
}

/**
 * 设备编号
 */
declare type robotDeviceNum = {
	deviceNum?: string ;
	deviceName?: string ;
	deviceModel?: string ;
}

/**
 * 设备编码及类型
 */
declare type deviceNumAndType = {
	type: string;
	DeviceNum: string;
}
/**
 * 简短用户信息
 */
type personShortInfo = {
	name?: string;
	phoneNum?: string | number;
}

/**
 * 用户角色
 */
type userRole = 'superAdmin' | 'admin' | 'headStoreManager' | 'storeManager' | 'placeHead' | 'engineeringHead';

/**
 * 场所类型 store: 消毒剂审核/门店, public_place: 空气质量/公共场所, supervise: 监管单位 , community: 末梢水/社区卫生服务中心
 */
type placeType = 'store' | 'public_place' | 'supervise' | 'community';


/**
 * 场所
 * _id 场所id
 * _type 场所类型 
 * placeName 场所名称
 * status 启用状态
 * creatorId 创建者ID
 * createdAt 创建时间
 * updatedAt 修改时间
 */
declare interface Place {
	_id: string;
	_type: placeType;
	placeName: string;
	status: boolean;
	creatorId: string;
	createdAt: string;
	updatedAt: string;
}

/**
 * views home
 */
declare type HomeState = {
	warningInfoList: NewInfo[];
	privateMessage: NewInfo[];
};

/**
 * views device
 */
// index
declare type rowDeviceType = {
	_id: string;
	name: string;
	deviceNum: string;
	type: string;
	unit: string;
	threshold: number;
	position?: string;
	status: 1 | 0;
	createdAt: string;
	updatedAt: string;
	publicPlaceId: string,
	sensorData:any
}

interface deviceTableType extends TableType {
	data: RowDeviceType[];
}

declare interface deviceState {
	tableData: deviceTableType;
	keyWord: string;
	publicPlaceId: string;
}

declare type rowRobotDeviceType = {
	_id: string;
	deviceName: string;
	deviceNum: string;
	deviceNickName: string;
	deviceModel: string;
	deviceAdminName: string;
	deviceAdminPhone: string;
	status: number;
	createdAt: string;
	updatedAt: string;
	hasRunningTask?: boolean;
}

interface robotdeviceTableType extends TableType {
	data: RowRobotDeviceType[];
}

declare interface robotdeviceState {
	tableData: deviceTableType;
	keyWord: string;
	status: string;
	deviceModel: string;
	deviceName: string;
}

// dialog
type deviceDataFome = {
	_id: string;
	name: string;
	deviceNum: string;
	type: string;
	unit: string;
	threshold?: number
	publicPlaceId?: string;
	position?: string;
	status: 0 | 1;
	sensorData?:any
}

type robotdeviceDataFome = {
	_id?: string;
	deviceName: string;
	deviceNum: string;
	deviceModel: string;
	deviceAdminName: string;
	deviceAdminPhone: string;
	deviceNickName?: string;
	status?: number;
}

declare interface deviceDialogState {
	dataFome: deviceDataFome;
	dialog: dialogConfig
}

declare interface robordeviceDialogState {
	dataFome: robotdeviceDataFome;
	dialog: dialogConfig
}

/**
 * views air
 */
// history
declare type AirHistoryType = {
	_id: string;
	deviceNum: string;
	data: number;
	time: string;
};

interface AirHistoryTableType extends TableType {
	data: AirHistoryType[];
}

declare interface AirHistoryState {
	tableData: SysUserTableType;
	dataUnit: string;
	keyWord: string;
	deviceType: string;
	exportBtnLoading: boolean;
}
/**
 * views air
 */
// history

declare interface RobotHistoryState {
	tableData: RobotHistoryTableType;
	taskNum: string;
	deviceName: string;
	robotPubilcPlace: string;
	result: string;
	exportBtnLoading: boolean;
}

interface RobotHistoryTableType extends TableType {
	data: RowRobotHistoryType[];
	total: number;
}

declare type RowRobotHistoryType = {
	_id: string;
	deviceID: string;
	deviceInfo: any;
	placeInfo: any;
	robotPubilcPlace: string;
	taskNum?: string;
	taskStatus: string;
	createdAt: string;
	updatedAt: string;
}
/**
 * views air placeManage index
 */
declare interface regionItemType {
	_id: string;
	name: string;
	area_code: string;
	parent_code: string;
}

declare type rowPlaceType = {
	_id: string;
	placeName: string;
	placeType: string;
	region: string[];
	area: string;
	area_code: string[];
	address: string;
	head: personShortInfo;
	engineeringHead: personShortInfo;
}

interface placeTableType extends TableType {
	data: RowDeviceType[];
}

declare interface robotplaceState {
	tableData: placeTableType;
	placeName: string;
}

declare interface placeState {
	tableData: placeTableType;
	keyWord: string;
}

declare interface taskState {
	tableData: taskTableType;
	taskNum?: string;
	taskStatus?: string;
	robotPubilcPlace?: string;
	deviceName?: string;
}

interface taskTableType extends TableType {
	data: RowTaskType[];
}

declare type RowTaskType = {
	_id: string;
	deviceName: string;
	deviceInfo: any;
	placeInfo: any;
	robotPubilcPlace: string;
	taskNum?: string;
	taskStatus: string;
	createdAt: string;
	updatedAt: string;
}
/**
 * views air placeManage dialog
 */
type placeDataFome = {
	_id: string;
	placeName: string;
	placeType: string;
	address: string;
	region: string[];
	area_code: string[];
	head: personShortInfo;
	engineeringHead: personShortInfo;
}

declare interface placeDialogState {
	dataFome: placeDataFome;
	dialog: dialogConfig;
	loading: boolean;
	headRelated: boolean
}

type taskDataFome = {
	_id: string;
	taskNum: string;
	deviceName: string;
	robotPubilcPlace: string;
	taskStatus: string;
	createdAt?: string;
	updatedAt?: string;
	deviceNickName?: string;
	deviceModel?: string;
	placeAddress?: string;
	startTime:string;
	intervalTime:{
		hour:string
	};
	samplePointList:Array<{index:number,pointName:string}>;
}

declare interface taskDialogState {
	dataFome: taskDataFome;
	dialog: dialogConfig;
	loading: boolean;
	headRelated: boolean
}
/**
 * views air placeManage dialog
 */
type RobotplaceDataFome = {
	_id: string;
	placeName: string;
	placeAddress: string;
	InchargeName: string;
	InchargePhone: string;
	SupervisionName: string;
	SupervisionPhone: string;
	sensorData:any
}

declare interface RobotplaceDialogState {
	dataFome: RobotplaceDataFome;
	dialog: dialogConfig;
	loading: boolean;
	headRelated: boolean
}
/**
 * views air earlyWarnning
 */
declare type RowEarlyWarnningType = {
	_id: string;
	status: 0 | 1;
	content: string;
	WarningType: string;
	createdAt: string;
	updatedAt: string;
}

interface earlyWarnningType extends TableType {
	data: RowEarlyWarnningType[];
}

declare interface earlyWarnningState {
	tableData: earlyWarnningType;
	keyWord?: string;
	selectedStatus?: number;
	deviceId?: string,
	WarningType?: string,
	publicPlaceId?: string,
	sendmsg:boolean;
}

/**
 * detection
 */
declare type socketMessageResult = {
	deviceNum: string;
	data?: number;
	time?: number;
}

declare interface detectionState {
	flag: boolean;
	desc: string;
	msg: socketMessageResult;
}

/**
 * views water
 */
// latest
declare type WaterLatestRowType = {
	sname: string;
	lasttesttime: string;
	pH: string;
	总氯: string;
	水温: string;
	浊度: string;
	电导率: string;
	[key: string]: string;
};

interface WaterLatestTableType {
	data: WaterLatestRowType[];
	total: number;
	loading: boolean;
}

declare interface WaterLatestState {
	tableData: WaterLatestTableType;
}

// history
declare type WaterHistoryRowType = {
	zbh: string;
	time: string;
	pH?: string;
	总氯?: string;
	水温?: string;
	浊度?: string;
	电导率?: string;
	[key: string]: string | undefined;
};

interface WaterHistoryTableType extends TableType {
	data: WaterHistoryRowType[];
}

declare interface WaterHistoryState {
	tableData: WaterHistoryTableType;
	zbhSelected: string;
	timeSelected?: date;
	datatime: string;
}
/**
 * views personal
 */

declare type PersonalIndexState = {
	personalForm: {
		name: string;
		userName: string;
		phoneNum: string;
		role: string;
		avatar: string;
		active: number
	};
	showUpdateForm: boolean;
};

declare type PersonalUpdateFormState = {
	updateFrom: {
		name: string;
		password: string;
		phoneNum: string;
		file?: Blob;
		imageUrl?: string;
	}
}

/**
 * userMana
 */
// index
declare type userRow = {
	userName: string;
	password: string;
	name: string;
	phoneNum?: string;
	role?: userRole;
	avatar?: string;
	active?: 0 | 1;
	createdAt?: string
}

declare type RowUserType = {
	userName: string;
	password: string;
	name: string;
	phoneNum: string;
	role?: userRole;
	avatar: string;
	active: 0 | 1;
	createdAt: string
	_id: string
};

interface SysUserTableType extends TableType {
	data: RowUserType[];
}

declare interface SysUserState {
	tableData: SysUserTableType;
	keyWord: string
}
// dialog
type userDataFome = {
	_id: string;
	userName: string;
	password?: string;
	name: string;
	phoneNum?: string;
	role?: userRole;
	active: 0 | 1;
	avatar?: string
}

declare interface userDialogState {
	dataFome: userDataFome;
	dialog: dialogConfig
}

/**
 * views page privateMessage
 */
declare type RowPrivateMessageType = {
	_id: string;
	title: string;
	content: string;
	createdAt: string;
	updatedAt: string;
	readTime?: string;
}

interface privateMessageType extends TableType {
	data: RowPrivateMessageType[];
}

declare interface privateMessageState {
	tableData: earlyWarnningType;
	keyWord?: string;
	selectedIsRead?: string;
}

/**
 * views xdj index
 */
declare type xdjState = {
	title: string;
	isLink: string;
	token: string;
};

/**
 * views xdj store
 */
// index
declare interface StoreSearchParams {
	keyWord?: string;
	active?: number
	// storeType?: string;
	// parentId?: string;
};

declare interface StoreListRow {
	_id: string
	placeName: string; //门店/总店名称
	parentId?: string; //总店ID
	creatorId?: string; // 用户标识
	createdAt: string; //创建时间
	updatedAt: string; //更新时间
};
// dialog
declare interface storeDialogFormType {
	_id: string;
	storeName: string;
	storeType: number; // 0总店 1门店
	parentId?: string;
};

/**
 * views xdj user
 */
// index
declare interface StoreSearchParams {
	keyWord?: string;
	active?: boolean;
	storeId?: string;
};

declare interface StoreManagerListRow {
	_id: string
	userName: string;
	name: string;
	password: string;
	storeId: string;
	phoneNum: string;
	active: boolean;

	createdAt: string;	// 创建时间
	updatedAt: string;	// 更新时间
};

// dialog
declare interface storeManagerDialogFormType {
	_id: string;
	userName: string;
	password?: string;
	name: string;
	phoneNum: string;
	storeId: string;
	active: 0 | 1;
	role?: 'storeManager' | 'headStoreManager'
};

/**
 * views xdj inspection
*/
// form
declare interface xdjForm {
	_id?: string;
	/**
	 * 一 产品名称
	 */
	name: string;
	/**
	 * 二 是否进口产品 '0': 否, '1': 是
	*/
	isImport: '0' | '1';
	/**
	 * 三 是否能提供国产产品生产企业卫生许可证或进口产品生产国（地区）允许生产销售的证明文件及报关单 '0': 否, '1': 是  否的话就直接传不能提供原因
	*/
	hasProve: string;
	/**
	 * 四 生产企业(制造商)名称
	 */
	producer: string;
	/**
	 * 五 是否为委托加工产品 '0': 否, '1': 是
	*/
	isEntrust: string;
	entrustName?: string;
	/**
	 * 六 如有生产企业卫生许可证，证上标准的生产类别是
	 */
	productCategory?: string;
	/**
	 * 七 净含量, 单位是%
	*/
	netContent?: number;
	/**
	 * 八 是否有标签和说明书  '0': 否, '1': 是
	 */
	hasLable: string;
	/**
	 * 九 是否有企业标准或质量标准 '0': 否, '1': 是
	*/
	hasStandard: string;
	/**
	 * 十 是否有检验报告 '0': 否, '1': 是
	*/
	hasReport: string;
	/**
	 * 十一 是否有配方 '0': 否, '1': 是
	*/
	hasFormula: string;
	/**
	 * 十二 消毒剂有效成分是， 传ActiveIngredient_id
	 */
	activeIngredient: string;
	/**
	 * 十三 有效成分含量
	 */
	ingredients?: string;
	/**
	 * 十四 产品标签和企业标准中有效成分及含量是否一致 '0': 否, '1': 是
	 */
	labelsConsistent: string;
	/**
	 * 十五 使用范围及其检测项目，只要传检测项目的_id即可
	 */
	detectedItems: string[];
	/**
	 * 十六 企业标准中显示产品有效期(保质期)为
	 */
	effectiveDuration: string;
	/**
	 * 十七 标签上是否注明是否标注有疗效   '0': 否, '1': 是
	 */
	hasCurativeEffect: string;
	curativeEffect?: string
	/**
	 * 十八 标签上是否注明是否标注减轻或缓解症状   '0': 否, '1': 是
	*/
	lessenSymptoms: string;
	lessenSymptomsReason?: string;
	/**
	 * 十九 标签上是否注明是否标注预防新冠肺炎   '0': 否, '1': 是
	*/
	preventionCOVID19: string;
	preventionCOVID19Reason?: string;
	/**
	 * 二十 不该用于部位, 可选项为：足部\眼睛\指甲\腋部\头皮\头发\鼻粘膜\肛肠\以上全无
	 */
	unusableParts?: string[];
	[property: string]: any;
}

// activeIngredient
/**
 * views xdj inspection
 * @description level 1有效成分  2使用范围  3检测项目
*/
declare interface activeIngredient {
	_id: string;
	name: string;
	level?: number;			// 1有效成分  2使用范围  3检测项目
	parentId?: string;		// level 2，3 包含此项
	mustDo?: 0 | 1			// 是否必做 level 3 检测项目 包含此项
}

// 使用范围
declare interface useRange {
	_id: string;
	name: string;
	level?: 2;
	parentId?: string;		//
}

// 检测项目
declare interface detectedItems {
	_id: string;
	name: string;
	level?: 3;
	parentId?: string;		// 
	mustDo: 0 | 1			// 是否必做
}

/**
 * views page warningTextMessage
 */
declare type RowWarningTextMessageType = {
	_id: string;
	content: string;
	sendToPhone: string;
	createdAt: string;
	failureReason?: string
}
interface warningTextMessageType extends TableType {
	data: RowWarningTextMessageType[];
}

declare interface warningTextMessageState {
	tableData: warningTextMessageType
	// 查询输入
	keyWord?: string,
	sendToPhone?: string,
	selectedRows: warningTextMessageRow[]
};

/**
 * views system
 */
// role
declare interface RowRoleType {
	roleName: string;
	roleSign: string;
	describe: string;
	sort: number;
	status: boolean;
	createTime: string;
}

interface SysRoleTableType extends TableType {
	data: RowRoleType[];
}

declare interface SysRoleState {
	tableData: SysRoleTableType;
}

declare type TreeType = {
	id: number;
	label: string;
	children?: TreeType[];
};

// dept
declare type DeptTreeType = {
	deptName: string;
	createTime: string;
	status: boolean;
	sort: number;
	describe: string;
	id: number | string;
	children?: DeptTreeType[];
};

declare interface RowDeptType extends DeptTreeType {
	deptLevel: string[];
	person: string;
	phone: string;
	email: string;
}

interface SysDeptTableType extends TableType {
	data: DeptTreeType[];
}

declare interface SysDeptState {
	tableData: SysDeptTableType;
}

// dic
type ListType = {
	id: number;
	label: string;
	value: string;
};

declare interface RowDicType {
	dicName: string;
	fieldName: string;
	describe: string;
	status: boolean;
	createTime: string;
	list: ListType[];
}

interface SysDicTableType extends TableType {
	data: RowDicType[];
}

declare interface SysDicState {
	tableData: SysDicTableType;
}

/**
 * views make
 */
// tableDemo
declare type TableDemoPageType = {
	pageNum: number;
	pageSize: number;
};

declare type TableHeaderType = {
	key: string;
	width: string;
	title: string;
	type: string | number;
	colWidth: string;
	width?: string | number;
	height?: string | number;
	isCheck: boolean;
};

declare type TableSearchType = {
	label: string;
	prop: string;
	placeholder: string;
	required: boolean;
	type: string;
	options?: SelectOptionType[];
};

declare type TableDemoState = {
	tableData: {
		data: EmptyObjectType[];
		header: TableHeaderType[];
		config: {
			total: number;
			loading: boolean;
			isBorder: boolean;
			isSelection: boolean;
			isSerialNo: boolean;
			isOperate: boolean;
		};
		search: TableSearchType[];
		param: EmptyObjectType;
		printName: string;
	};
};
