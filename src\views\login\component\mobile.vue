<template>
	<el-form ref="formRef" size="large" class="login-content-form" :model="state.ruleForm" :rules="rules">
		<el-form-item prop="phoneNum" class="login-animation1">
			<el-input text :placeholder="$t('message.mobile.placeholder1')" v-model="state.ruleForm.phoneNum" clearable
				autocomplete="off">
				<template #prefix>
					<i class="iconfont icon-dianhua el-input__icon"></i>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item prop="code" class="login-animation2">
			<el-col :span="15">
				<el-input text maxlength="6" :placeholder="$t('message.mobile.placeholder2')" v-model="state.ruleForm.code"
					clearable autocomplete="off">
					<template #prefix>
						<el-icon class="el-input__icon"><ele-Position /></el-icon>
					</template>
				</el-input>
			</el-col>
			<el-col :span="1"></el-col>
			<el-col :span="8">
				<el-button @click="getCode(formRef)" v-waves class="login-content-code" :loading="state.loading.codeBtn"
					:disabled="state.getCodeBtnDisable">{{ state.codeBtnText
					}}</el-button>
			</el-col>
		</el-form-item>
		<el-form-item class="login-animation3">
			<el-button @click="login" round type="primary" v-waves class="login-content-submit"
				:loading="state.loading.login">
				<span>{{ $t('message.mobile.btnText') }}</span>
			</el-button>
		</el-form-item>
		<div class="font12 mt30 login-animation4 login-msg">{{ $t('message.mobile.msgText') }}</div>
	</el-form>
</template>

<script setup lang="ts" name="loginMobile">
import { reactive, ref, computed } from 'vue';
import { Session } from '/@/utils/storage';
import { getCodeByPhone, loginByCode } from "/@/api/login";
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
// 定义变量内容
const useState = () => reactive({
	ruleForm: {
		phoneNum: '',
		code: '',
	},
	loading: {
		codeBtn: false,
		login: false,
	},
	codeBtnText: t('message.mobile.codeText'),
	waitTime: 120,
	getCodeBtnDisable: false,
})
const state = useState()
// 检验规则
const rules = reactive({
	phoneNum: [
		// { required: true, message: computed(() => t('message.common.placeholderPhoneNum')), trigger: 'blur' },
		{ pattern: /^1[3456789]\d{9}$/, message: computed(() => t('message.views.rulesUserPhoneNumReg')), trigger: 'blur' }
	]
})
// 组件ref
const formRef = ref()

// 获取验证码
const getCode = async (formEl: FormInstance) => {
	// 校验手机号
	formEl.validate(async (valid) => {
		if (!valid) {
			return
		}
		state.loading.codeBtn = true
		await getCodeByPhone({ phoneNum: state.ruleForm.phoneNum })
			// new Promise((resolve) => {
			// 	return resolve('success')
			// })
			.then(() => {
				// loading end
				state.loading.codeBtn = false
				ElMessage.success('验证码发送成功！验证码有效期为10分钟')
				state.getCodeBtnDisable = true
				state.codeBtnText = `${state.waitTime}秒后重新发送`
				// 按钮disable倒计时
				let timer = setInterval(() => {
					if (state.waitTime > 1) {
						state.waitTime--
						state.codeBtnText = `${state.waitTime}秒后重新发送`
					} else {
						clearInterval(timer)
						state.codeBtnText = t('message.mobile.codeText')
						state.getCodeBtnDisable = false
						state.waitTime = 120
					}
				}, 1000)
			})
			.catch(() => {
				state.loading.codeBtn = false
				ElMessage.error('验证码发送失败！')
			})
	})
}

const login = async () => {
	state.loading.login = true;
	try {
		const res = await loginByCode({ phoneNum: state.ruleForm.phoneNum, code: state.ruleForm.code })
		Session.set('token', res.data.token);
		const isNoPower = await initFrontEndControlRoutes();
		state.loading.login = false;
		signInSuccess(isNoPower);
		// 添加 loading，防止第一次进入界面时出现短暂空白
		NextLoading.start();
	} catch (error) {
		state.loading.login = false;
	}
}

// 登录成功后的跳转
const signInSuccess = (isNoPower: boolean | undefined) => {
	if (isNoPower) {
		ElMessage.warning('抱歉，您没有登录权限');
		Session.clear();
	} else {
		// 初始化登录成功时间问候语
		let currentTimeInfo = currentTime.value;
		// 登录成功，跳到转首页
		// 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
		if (route.query?.redirect) {
			router.push({
				path: <string>route.query?.redirect,
				query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
			});
		} else {
			router.push('/');
		}
		// 登录成功提示
		const signInText = t('message.signInText');
		ElMessage.success(`${currentTimeInfo}，${signInText}`);
	}
};


// 时间获取
const currentTime = computed(() => {
	return formatAxis(new Date());
});

</script>

<style scoped lang="scss">
.login-content-form {
	margin-top: 20px;

	@for $i from 1 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}

	.login-content-code {
		width: 100%;
		padding: 0;
	}

	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
	}

	.login-msg {
		color: var(--el-text-color-placeholder);
	}
}
</style>
