<template>
    <div class="totalCount-container">
        <miniBorder title="总体统计数据">
            <div class="four-box">
                <div class="box-item">
                    <div style="width:100%;height: auto;display: flex;justify-content: center;">
                        <Flipper :size="size" style="margin: 0 1px;" ref="placeRef1"></Flipper>
                        <div class="chart-Flipper" style="margin: 0 1px;">
                            <div class="chart">,</div>
                        </div>
                        <Flipper :size="size" style="margin: 0 1px;" ref="placeRef2"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="placeRef3"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="placeRef4"></Flipper>
                        <!-- <Flipper :size="size" style="margin: 0 1px;" ref="placeRef5"></Flipper> -->
                    </div>
                    <div class="box-item-bottom-title">监测场所数</div>
                </div>
                <div class="box-item">
                    <div style="width:100%;height: auto;display: flex;justify-content: center;">
                        <Flipper :size="size" style="margin: 0 1px;" ref="pointRef1"></Flipper>
                        <div class="chart-Flipper" style="margin: 0 1px;">
                            <div class="chart">,</div>
                        </div>
                        <Flipper :size="size" style="margin: 0 1px;" ref="pointRef2"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="pointRef3"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="pointRef4"></Flipper>
                    </div>
                    <div class="box-item-bottom-title">监测点位数</div>
                </div>
                <div class="box-item">
                    <div style="width:100%;height: auto;display: flex;justify-content: center;">
                        <Flipper :size="size" style="margin: 0 1px;" ref="warningRef1"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="warningRef2"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="warningRef3"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="warningRef4"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="warningRef5"></Flipper>
                    </div>
                    <div class="box-item-bottom-title">报警次数</div>
                </div>
                <div class="box-item">
                    <div style="width:100%;height: auto;display: flex;justify-content: center;">
                        <Flipper :size="size" style="margin: 0 1px;" ref="rateRef1"></Flipper>
                        <Flipper :size="size" style="margin: 0 1px;" ref="rateRef2"></Flipper>
                        <div class="chart-Flipper" style="margin: 0 1px;">
                            <div class="chart">%</div>
                        </div>
                    </div>
                    <div class="box-item-bottom-title">总合格率</div>
                </div>

            </div>
        </miniBorder>
    </div>
</template>

<script setup lang="ts" name="RegionalRank">
import miniBorder from './miniBorder.vue';
import Flipper from './Flipper.vue'
import { ref, onMounted, watch } from "vue";
import mittBus from '/@/utils/mitt';

const size = ref(11)
const resizeFun = () => {
    const offsetWidth = document.getElementsByClassName('dashboard-container')[0].offsetWidth
    if (offsetWidth <= 1024) {
        size.value = 4 * 1.2
    }
    else if (offsetWidth <= 1440) {
        size.value = 6 * 1.2
    }
    else if (offsetWidth <= 1960) {
        size.value = 8 * 1.2
    }
    else if (offsetWidth <= 2560) {
        size.value = 10 * 1.2
    }
    else if (offsetWidth <= 3440) {
        size.value = 13 * 1.2
    }
    else if (offsetWidth <= 3840) {
        size.value = 16 * 1.2
    }
}

const placeRef1 = ref()
const placeRef2 = ref()
const placeRef3 = ref()
const placeRef4 = ref()
// const placeRef5 = ref()
const place = [placeRef1, placeRef2, placeRef3, placeRef4]
const updatePlcae = (newData: number | string, oldData: number | string) => {
    if (typeof newData === 'number') {
        newData = Math.round(newData).toString()
    }
    const newArr = newData.split('')
    while (newArr.length < place.length) {
        newArr.unshift('0')
    }

    if (typeof oldData === 'number') {
        oldData = Math.round(oldData).toString()
    }
    const oldArr = oldData.split('')
    while (oldArr.length < place.length) {
        oldArr.unshift('0')
    }

    for (let i = 0; i < place.length; i++) {
        place[i].value.flipDown(oldArr[i], newArr[i])
    }
}

const pointRef1 = ref()
const pointRef2 = ref()
const pointRef3 = ref()
const pointRef4 = ref()
const point = [pointRef1, pointRef2, pointRef3, pointRef4]
const updatePoint = (newData: number | string, oldData: number | string) => {
    if (typeof newData === 'number') {
        newData = Math.round(newData).toString()
    }
    const newArr = newData.split('')
    while (newArr.length < point.length) {
        newArr.unshift('0')
    }

    if (typeof oldData === 'number') {
        oldData = Math.round(oldData).toString()
    }
    const oldArr = oldData.split('')
    while (oldArr.length < point.length) {
        oldArr.unshift('0')
    }

    for (let i = 0; i < point.length; i++) {
        point[i].value.flipDown(oldArr[i], newArr[i])
    }
}

const warningRef1 = ref()
const warningRef2 = ref()
const warningRef3 = ref()
const warningRef4 = ref()
const warningRef5 = ref()
const warning = [warningRef1, warningRef2, warningRef3, warningRef4, warningRef5]
const updateWarning = (newData: number | string, oldData: number | string) => {
    if (typeof newData === 'number') {
        newData = Math.round(newData).toString()
    }
    const newArr = newData.split('')
    while (newArr.length < warning.length) {
        newArr.unshift('0')
    }

    if (typeof oldData === 'number') {
        oldData = Math.round(oldData).toString()
    }
    const oldArr = oldData.split('')
    while (oldArr.length < warning.length) {
        oldArr.unshift('0')
    }

    for (let i = 0; i < warning.length; i++) {
        warning[i].value.flipDown(oldArr[i], newArr[i])
    }
}

const rateRef1 = ref()
const rateRef2 = ref()
const rate = [rateRef1, rateRef2]
const updateRate = (newData: number | string, oldData: number | string) => {
    if (typeof newData === 'number') {
        newData = Math.round(newData).toString()
    }
    const newArr = newData.split('')
    while (newArr.length < rate.length) {
        newArr.unshift('0')
    }

    if (typeof oldData === 'number') {
        oldData = Math.round(oldData).toString()
    }
    const oldArr = oldData.split('')
    while (oldArr.length < rate.length) {
        oldArr.unshift('0')
    }

    for (let i = 0; i < rate.length; i++) {
        rate[i].value.flipDown(oldArr[i], newArr[i])
    }
}

const props = withDefaults(defineProps<{ totalCount: any }>(),
    {
        totalCount: {
            place: 0,
            point: 0,
            warning: 0,
            rate: 0
        },
    })

// 监听变化
watch(
    () => [props.totalCount.place, props.totalCount.point, props.totalCount.warning, props.totalCount.rate],
    (newData, oldData) => {
        setTimeout(() => {
            updatePlcae(newData[0], oldData[0])
            updatePoint(newData[1], oldData[1])
            updateWarning(newData[2], oldData[2])
            updateRate(newData[3], oldData[3])
        }, 250)
    },
)

onMounted(() => {
    resizeFun()
    mittBus.on('resize', resizeFun)
})

</script>

<style scoped lang="scss">
.totalCount-container {
    width: 11vw;
    height: 30vh;

    @media screen and (max-width: 3840px) {
        font-size: 16px;
    }

    @media screen and (max-width: 3440px) {
        font-size: 13px;
    }

    @media screen and (max-width: 2560px) {
        font-size: 10px;
    }

    @media screen and (max-width: 1960px) {
        font-size: 8px;
    }

    @media screen and (max-width: 1440px) {
        font-size: 6px;
    }

    @media screen and (max-width: 1024px) {
        font-size: 4px;
    }

    .four-box {
        width: 100%;
        height: 100%;
        padding: 0.75vw 0.1vw;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        justify-content: space-around;
        align-items: center;

        .box-item {
            width: 4vw;
            height: 2.45vw;
            padding: 0.25vw 0.5vw;
            background-image: url('../../../../src/assets/images/trisynchronization/trisynchronization-bottom.png');
            background-size: 100% 80%;
            background-repeat: no-repeat;

            .box-item-bottom-title {
                height: 0.6em;
                text-align: center;
                font-size: 0.6em;
                font-family: Source Han Sans CN, Source Han Sans CN-Regular;
                font-weight: Regular;
                text-align: center;
                color: #ffffff;
                line-height: 0.6em;
                margin-top: 10%;
            }

            .chart-Flipper {
                display: inline-block;
                position: relative;
                background: #fff;
                color: #fff;
                box-shadow: 0 0 6px rgba(0, 0, 0, 0.5);
                text-align: center;
                font-family: 'Helvetica Neue';
                // width: $size ;
                // height: calc(1.6*$size );
                // font-size: calc(1.1*$size);
                // line-height: calc(1.6*$size);

                width: calc(1em);
                height: calc(1.6em);
                font-size: calc(1em);
                line-height: calc(1.6em);

                .chart {
                    position: absolute;
                    left: 0;
                    right: 0;
                    background-color: #0B3833;
                    overflow: hidden;
                    box-sizing: border-box;
                    z-index: 3;
                }
            }
        }

        // box-item 奇数的时候 margin-right: 10px;
        // .box-item:nth-child(odd) {
        //     margin-right: 20px;
        // }
    }



}
</style>