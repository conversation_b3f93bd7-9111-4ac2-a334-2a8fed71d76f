import request from '/@/utils/request';

/**
 *
 * 登录api接口集合
 * @method login 用户登录
 * @method getUsetInfo 获取当前登录用户信息
 */

// 用户登录
export function login(data: API.UserInfoParams) {
	return request({
		url: '/auth/login',
		method: 'post',
		data,
	});
}

// 获取当前登录用户信息
export function getUserInfo() {
	return request({
		url: '/user/info',
		method: 'get'
	});
}

// 发送验证码
export function getCodeByPhone(data: { phoneNum: string }) {
    return request({
        url: '/auth/sendCode',
        method: 'post',
        data
    });
}

// 手机验证码登录
export function loginByCode(data: { phoneNum: string; code: string }) {
    return request({
        url: '/auth/smsLogin',
        method: 'post',
        data
    });
}
