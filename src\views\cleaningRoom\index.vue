<template>
    <div class="monitorList layout-pd">
        <el-card shadow="hover">
            <div class="monitorList-wrap">
                <div class="monitorList-item" v-for="item in vedioList" :key="item.imgUrl" @click="handleClick(item)">
                    <div class="item-img">
                        <img :src="item.imgUrl">
                    </div>
                    <div class="item-txt">
                        <div class="item-txt-title"><strong>{{ item.title }}</strong></div>
                    </div>
                </div>
            </div>

        </el-card>
        <el-dialog ref="VedioDialogRef" :title="dialogData.title" v-model="isShowDialog" width="auto">
            <img :src="dialogData.imgUrl" style="height: 66vh;">
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="MonitorList">
import { ref } from 'vue'
import imgUrl1 from "/@/assets/dashBoard/1.jpg";
import imgUrl2 from "/@/assets/dashBoard/2.jpg";
import imgUrl3 from "/@/assets/dashBoard/3.jpg";
import imgUrl4 from "/@/assets/dashBoard/4.jpg";
import imgUrl5 from "/@/assets/dashBoard/5.jpg";
import imgUrl6 from "/@/assets/dashBoard/6.jpg";

const isShowDialog = ref(false)
const dialogData = ref<{ title?: string; imgUrl?: any }>({ title: '', imgUrl: null })
const vedioList = ref<{ title: string; imgUrl: any }[]>([
    {
        title: "监控位 A1",
        imgUrl: imgUrl1,
    },
    {
        title: "监控位 B1",
        imgUrl: imgUrl2,
    },
    {
        title: "监控位 C1",
        imgUrl: imgUrl3,
    },
    {
        title: "监控位 A2",
        imgUrl: imgUrl4,
    },
    {
        title: "监控位 B2",
        imgUrl: imgUrl5,
    },
    {
        title: "监控位 C2",
        imgUrl: imgUrl6,
    },
])

const handleClick = (item: { title: string; imgUrl: any }) => {
    isShowDialog.value = true;
    dialogData.value = item
}

</script>

<style scoped lang="scss">
@import '../../theme/mixins/index.scss';

.monitorList-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;


    .monitorList-item {
        flex: 1 1 0.3;
        border: 1px solid var(--next-border-color-light);
        width: 25%;
        height: 100%;
        border-radius: 2px;
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
        margin: 27px;

        &:hover {
            cursor: pointer;
            border: 1px solid var(--el-color-primary);
            transition: all 0.3s ease;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);

            .item-txt-title {
                color: var(--el-color-primary) !important;
                transition: all 0.3s ease;
            }

            .item-img {
                img {
                    transition: all 0.3s ease;
                    transform: translateZ(0) scale(1.05);
                }
            }
        }

        .item-img {
            width: 100%;
            height: 210px;
            overflow: hidden;

            img {
                transition: all 0.3s ease;
                width: 100%;
                height: 100%;
            }
        }

        .item-txt {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            overflow: hidden;

            .item-txt-title {
                text-overflow: ellipsis;
                overflow: hidden;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                color: #666666;
                transition: all 0.3s ease;

                &:hover {
                    color: var(--el-color-primary);
                    text-decoration: underline;
                    transition: all 0.3s ease;
                }
            }

        }
    }
}
</style>
