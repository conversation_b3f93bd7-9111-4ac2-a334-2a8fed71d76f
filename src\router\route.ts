import { RouteRecordRaw } from 'vue-router';

/**
 * 建议：路由 path 路径与文件夹名称相同，找文件可浏览器地址找，方便定位文件位置
 *
 * 路由meta对象参数说明
 * meta: {
 *      title:          菜单栏及 tagsView 栏、菜单搜索名称（国际化）
 *      isLink：        是否超链接菜单，开启外链条件，`1、isLink: 链接地址不为空 2、isIframe:false`
 *      isHide：        是否隐藏此路由
 *      isKeepAlive：   是否缓存组件状态
 *      isAffix：       是否固定在 tagsView 栏上
 *      isIframe：      是否内嵌窗口，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
 *      roles：         当前路由权限标识，取角色管理。控制路由显示、隐藏。超级管理员：admin 普通角色：common
 *      icon：          菜单、tagsView 图标，阿里：加 `iconfont xxx`，fontawesome：加 `fa xxx`
 * }
 */

// 扩展 RouteMeta 接口
declare module 'vue-router' {
	interface RouteMeta {
		title?: string;
		isLink?: string;
		isHide?: boolean;
		isKeepAlive?: boolean;
		isAffix?: boolean;
		isIframe?: boolean;
		roles?: string[];
		icon?: string;
	}
}

/**
 * 定义动态路由
 * 前端添加路由，请在顶级节点的 `children 数组` 里添加
 * @description 未开启 isRequestRoutes 为 true 时使用（前端控制路由），开启时第一个顶级 children 的路由将被替换成接口请求回来的路由数据
 * @description 各字段请查看 `/@/views/system/menu/component/addMenu.vue 下的 ruleForm`
 * @returns 返回路由菜单数据
 */
export const dynamicRoutes: Array<RouteRecordRaw> = [
	{
		path: '/',
		name: '/',
		component: () => import('/@/layout/index.vue'),
		redirect: '/home',
		meta: {
			isKeepAlive: true,
		},
		children: [
			{
				path: '/home',
				name: 'home',
				component: () => import('/@/views/home/<USER>'),
				meta: {
					title: 'message.router.home',
					isLink: '',
					isHide: false,
					isKeepAlive: true,
					isAffix: true,
					isIframe: false,
					roles: ['superAdmin', 'admin', 'common', 'headStoreManager', 'storeManager'],
					icon: 'iconfont icon-shouye',
				},
			},

			// 用户管理
			{
				path: '/system/user',
				name: 'systemUser',
				component: () => import('/@/views/system/user/index.vue'),
				meta: {
					title: 'message.router.systemUser',
					isLink: '',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin'],
					// roles: ['admin'],
					icon: 'iconfont icon-icon-',
				},
			},

			// 设备管理
			{
				path: '/device',
				name: 'device',
				component: () => import('/@/views/device/index.vue'),
				meta: {
					title: 'message.router.deviceMana',
					isLink: '',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin'],
					icon: 'iconfont icon-crew_feature',
				},
			},

			// 公共场所空气监测
			{
				path: '/air',
				name: 'air',
				component: () => import('/@/layout/routerView/parent.vue'),
				redirect: '/air/latest',
				meta: {
					title: 'message.router.air',
					isLink: '',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin', 'common'],
					icon: 'iconfont icon-bolangnengshiyanchang',
				},
				children: [
					{
						path: '/air/latest',
						name: 'airLatest',
						component: () => import('/@/views/air/latest/index.vue'),
						meta: {
							title: 'message.router.latest',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common'],
							icon: 'iconfont icon-putong',
						},
						children: [
							{
								path: '/air/latest/CO2',
								name: 'airLatest_CO2',
								component: () => import('/@/views/air/latest/latest_CO2.vue'),
								meta: {
									title: 'message.router.latest_CO2',
									isLink: '',
									isHide: true,
									isKeepAlive: true,
									isAffix: false,
									isIframe: false,
									roles: ['superAdmin', 'admin', 'common'],
								},
							},
							{
								path: '/air/latest/Robot',
								name: 'airLatest_Robot',
								component: () => import('/@/views/air/latest/latest_Robot.vue'),
								meta: {
									title: 'message.router.latest_Robot',
									isLink: '',
									isHide: true,
									isKeepAlive: true,
									isAffix: false,
									isIframe: false,
									roles: ['superAdmin', 'admin', 'common'],
								},
							},
						],
					},
					{
						path: '/air/history',
						name: 'airHistory',
						component: () => import('/@/views/air/history/index.vue'),
						meta: {
							title: 'message.router.history',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common'],
							icon: 'iconfont icon-jinridaiban',
						},
					},
					{
						path: '/air/elaryWarning',
						name: 'airElaryWarning',
						component: () => import('/@/views/air/elaryWarning/index.vue'),
						meta: {
							title: 'message.router.elaryWarning',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common'],
							icon: 'ele-Warning',
						},
					},
					{
						path: '/air/placeManage',
						name: 'airPlaceManage',
						component: () => import('/@/views/air/placeManage/index.vue'),
						meta: {
							title: 'message.router.placeManage',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common'],
							icon: 'ele-House',
						},
					},
					{
						path: '/air/taskManage',
						name: 'airTaskManage',
						component: () => import('/@/views/air/taskManage/index.vue'),
						meta: {
							title: 'message.router.taskManage',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common'],
							icon: 'ele-DocumentAdd',
						},
					},
					{
						path: '/air/head',
						name: 'airHead',
						component: () => import('/@/views/air/user/index.vue'),
						meta: {
							title: 'message.router.placeHead',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common'],
							icon: 'iconfont icon-icon-',
						},
					},
				]
			},

			// 末梢水
			{
				path: '/water',
				name: 'water',
				component: () => import('/@/layout/routerView/parent.vue'),
				redirect: '/water/history',
				meta: {
					title: 'message.router.water',
					isLink: '',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin'],
					icon: 'iconfont icon-bolangneng',
				},
				children: [{
					path: '/water/latest',
					name: 'waterLatest',
					component: () => import('/@/views/water/latest/index.vue'),
					meta: {
						title: 'message.router.latest',
						isLink: '',
						isHide: false,
						isKeepAlive: true,
						isAffix: false,
						isIframe: false,
						roles: ['superAdmin', 'admin'],
						icon: 'iconfont icon-putong',
					},
				},
				{
					path: '/water/history',
					name: 'waterHistory',
					component: () => import('/@/views/water/history/index.vue'),
					meta: {
						title: 'message.router.history',
						isLink: '',
						isHide: false,
						isKeepAlive: true,
						isAffix: false,
						isIframe: false,
						roles: ['superAdmin', 'admin'],
						icon: 'iconfont icon-jinridaiban',
					},
				},
				]
			},

			// 住宿场所清洗消毒间监控
			{
				path: '/cleaningRoom',
				name: 'cleaningRoom',
				component: () => import('/@/layout/routerView/parent.vue'),
				redirect: '/cleaningRoom/monitorList',
				meta: {
					title: 'message.router.cleaningRoomMonitor',
					isLink: '',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin'],
					icon: 'iconfont icon-zhongduancanshuchaxun',
				},
				children: [
					{
						path: '/cleaningRoom/monitorList',
						name: 'MonitorList',
						component: () => import('/@/views/cleaningRoom/index.vue'),
						meta: {
							title: 'message.router.monitorList',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common'],
							icon: 'ele-VideoCamera',
						},
					},
					// {
					// 	path: '/cleaningRoom/index',
					// 	name: 'cleaningRoomIndex',
					// 	component: () => import('/@/views/error/developing.vue'),
					// 	meta: {
					// 		title: 'message.router.developing',
					// 		isLink: '',
					// 		isHide: false,
					// 		isKeepAlive: true,
					// 		isAffix: false,
					// 		isIframe: false,
					// 		roles: ['superAdmin', 'admin', 'common'],
					// 		icon: 'iconfont icon-AIshiyanshi',
					// 	},
					// }
				]
			},

			// 消毒产品销售监管审核
			{
				path: '/disinfectant',
				name: 'disinfectant',
				component: () => import('/@/views/xdj/index.vue'),
				redirect: '/disinfectant/inspection',
				meta: {
					title: 'message.router.disinfectionProductAudit',
					// isLink: 'http://xhwjxdj.pilelot.cn/xdj/manage/login/ssoLogin',
					isLink: '',
					isHide: false,
					isKeepAlive: false,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin', 'common', 'headStoreManager', 'storeManager'],
					icon: 'iconfont icon-siweidaotu',
				},
				children: [
					// 用户管理
					{
						path: '/disinfectant/user',
						name: 'xdjUserMana',
						component: () => import('/@/views/xdj/user/index.vue'),
						meta: {
							title: 'message.router.xdjUser',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common', 'headStoreManager'],
							icon: 'ele-Notebook',
						},
					},
					// 门店管理
					{
						path: '/disinfectant/store',
						name: 'storeMana',
						component: () => import('/@/views/xdj/store/index.vue'),
						meta: {
							title: 'message.router.xdjStore',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common', 'headStoreManager'],
							icon: 'ele-OfficeBuilding',
						},
					},
					// 消毒剂检测（报告）
					{
						path: '/disinfectant/inspection',
						name: 'xdjInspection',
						component: () => import('/@/views/xdj/inspection/index.vue'),
						meta: {
							title: 'message.router.xdjDetection',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							roles: ['superAdmin', 'admin', 'common', 'headStoreManager', 'storeManager'],
							icon: 'iconfont icon-gongju',
						},
					},
				]
			},

			// 个人中心
			{
				path: '/personal',
				name: 'personal',
				component: () => import('/@/views/personal/index.vue'),
				meta: {
					title: 'message.router.personal',
					isLink: '',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin', 'common', 'headStoreManager', 'storeManager'],
					icon: 'iconfont icon-gerenzhongxin',
				},
			},

			// 站内消息
			{
				path: '/privateMessage',
				name: 'privateMessage',
				component: () => import('/@/views/page/privateMessage/index.vue'),
				meta: {
					title: 'message.router.privateMessage',
					isLink: '',
					isHide: true,
					isKeepAlive: true,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin', 'common'],
					icon: '',
				},
			},

			// 站内消息
			{
				path: '/warningTextMessage',
				name: 'warningTextMessage',
				component: () => import('/@/views/page/warningTextMessage/index.vue'),
				meta: {
					title: 'message.router.warningTextMessage',
					isLink: '',
					isHide: true,
					isKeepAlive: true,
					isAffix: false,
					isIframe: false,
					roles: ['superAdmin', 'admin', 'common'],
					icon: '',
				},
			},

			// // 大数据图表
			// {
			// 	path: '/chart',
			// 	name: 'chartIndex',
			// 	component: () => import('/@/views/chart/index.vue'),
			// 	meta: {
			// 		title: 'message.router.chartIndex',
			// 		isLink: '',
			// 		isHide: false,
			// 		isKeepAlive: true,
			// 		isAffix: false,
			// 		isIframe: false,
			// 		roles: ['superAdmin','admin', 'common'],
			// 		icon: 'iconfont icon-ico_shuju',
			// 	},
			// },

			// // 表格封装演示
			// {
			// 	path: '/make/tableDemo',
			// 	name: 'makeTableDemo',
			// 	component: () => import('/@/views/make/tableDemo/index.vue'),
			// 	meta: {
			// 		title: 'message.router.makeTableDemo',
			// 		isLink: '',
			// 		isHide: false,
			// 		isKeepAlive: true,
			// 		isAffix: false,
			// 		isIframe: false,
			// 		roles: ['superAdmin','admin', 'common'],
			// 		icon: 'iconfont icon-shuju',
			// 	},
			// },
		],
	},
];

/**
 * 定义404、401界面
 */
export const notFoundAndNoPower = [
	{
		path: '/:path(.*)*',
		name: 'notFound',
		component: () => import('/@/views/error/404.vue'),
		meta: {
			title: 'message.staticRoutes.notFound',
			isHide: true,
		},
	},
	{
		path: '/401',
		name: 'noPower',
		component: () => import('/@/views/error/401.vue'),
		meta: {
			title: 'message.staticRoutes.noPower',
			isHide: true,
		},
	},
];

/**
 * 定义静态路由（默认路由）
 * 此路由不要动，前端添加路由的话，请在 `dynamicRoutes 数组` 中添加
 * @description 前端控制直接改 dynamicRoutes 中的路由，后端控制不需要修改，请求接口路由数据时，会覆盖 dynamicRoutes 第一个顶级 children 的内容（全屏，不包含 layout 中的路由出口）
 * @returns 返回路由菜单数据
 */
export const staticRoutes: Array<RouteRecordRaw> = [
	/**
	 * 提示：写在这里的为全屏界面，一般界面不建议写在这里
	 */
	{
		path: '/login',
		name: 'login',
		component: () => import('/@/views/login/index.vue'),
		meta: {
			title: '登录',
		},
	},
	{
		path: '/dashBoard',
		name: 'dashBoard',
		component: () => import('/@/views/dashBoard/index.vue'),
		meta: {
			title: 'message.router.dashBoard',
		},
	},
];
