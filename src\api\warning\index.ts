import request from '/@/utils/request';
import { authRequest } from '/@/utils/authRequest'
/**
 *  预警管理 - 获取预警列表 
 */
export function getWarningList(query: API.getWarningListParams) {
    return request({
        url: '/warning/list',
        method: 'get',
        params: query,
    });
}

/**
 *  预警管理 - 获取预警详情
 */
export function getWarningDetail(query: { _id: string; }) {
    return request({
        url: '/warning/' + query._id.toString(),
        method: 'get',
    });
}

/**
 *  预警管理 - 解除预警
 */
export function liftWarning(query: { _id: string; WarningType:string}) {
    return request({
        url: '/warning/liftWarning',
        method: 'get',
        params: query,
    });
}

/**
 *  预警管理 - 删除预警
 */
export function deleteWarning(query: { _id: string; WarningType:string}) {
    return request({
        url: '/warning/deleteWarning',
        method: 'delete',
        params: query,
    });
}


/**
 *  预警管理 - 获取预警列表 
 */
export function authGetWarningList(query: API.getWarningListParams) {
    return authRequest({
        url: '/warning',
        method: 'get',
        params: query,
    },
        ['superAdmin', 'admin']);
}

export function getSMSSwitch() {
    return request({
        url: '/ali-message/smsSwitch',
        method: 'get',
    });
}

export function SMSSwitch(body:{enabled:boolean}) {
    return request({
        url: '/ali-message/smsSwitch',
        method: 'post',
        data: body
    });
}