<template>
    <div class="warningList-border ">
        <div ref="lottieBorderRef" style="width: 100%; height: 100%; pointer-events: none;position: absolute;"></div>
        <div class="sub-box" :style="{ height: resizeHeight + 'px' }">
            <div class="sub-box-title">
                {{ props.title }}
            </div>
            <div class="sub-box-line"></div>
            <div class="sub-box-slot ">
                <slot></slot>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts" name="DashBoardBorder">
import lottie from 'lottie-web';
import animationData from '../../../../src/assets/lottie/warningListBorder.json'; // 路径需要根据实际情况修改
import { ref, onMounted } from "vue";
import mittBus from '/@/utils/mitt';
const props = defineProps<{ title: string }>()

const lottieBorderRef = ref()
const initLottie = () => {
    lottie.loadAnimation({
        container: lottieBorderRef.value, // 在模板中添加一个 ref="lottieContainer" 的容器
        animationData: animationData,
        renderer: 'svg', // 选择渲染器，可以是 'svg'、'canvas'、'html'
        loop: true, // 是否循环播放
        autoplay: true, // 是否自动播放
    });
}

// box自适应高度
const resizeHeight = ref(192)
const resizeFun = () => {
    resizeHeight.value = document.getElementsByClassName('dashboard-container')[0].offsetWidth / 2080 * 0.3 * 640
}
onMounted(() => {
    resizeFun()
    initLottie()
    mittBus.on('resize', resizeFun)
})

</script>

<style scoped lang="scss">
/* largeBorder */
.warningList-border {
    width: 100%;
    height: 100%;
    position: relative;

    display: flex;
    flex-direction: column;
    justify-content: center;

    @font-face {
        font-family: Source Han Sans CN-Medium;
        src: url("../../../../src/assets/font/Source Han Sans CN Medium.otf");
    }

    .sub-box {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 1.65em 1.25em 0.4em 1.65em;

        @media screen and (max-width: 3840px) {
            font-size: 20px;
        }

        @media screen and (max-width: 3440px) {
            font-size: 16px;
        }

        @media screen and (max-width: 2560px) {
            font-size: 12px;
        }

        @media screen and (max-width: 1960px) {
            font-size: 10px;
        }

        @media screen and (max-width: 1440px) {
            font-size: 8px;
        }

        @media screen and (max-width: 1024px) {
            font-size: 6px;
        }

        .sub-box-title {
            font-size: 1em;
            font-family: Source Han Sans CN, Source Han Sans CN-Medium;
            font-weight: Medium;
            text-align: left;
            color: #18caca;
            line-height: 2em;
            margin-left: 0.6em;
            text-shadow: 0px 0px 0.75em 0px rgba(86, 254, 254, 0.53);
        }

        .sub-box-line {
            width: 100%;
            height: 0.35em;
            background-image: url("../../../../src/assets/images/subBox/sub-box-line.png");
            background-size: 100% auto;
        }

        .sub-box-slot {
            width: 100%;
            height: calc(100% - 34px);
            padding: 0.6em;
        }
    }

}
</style>