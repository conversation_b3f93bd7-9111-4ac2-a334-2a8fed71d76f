import request from '/@/utils/request';

// 消毒剂审核
// 获取报告列表
export function getDisinfectantList(query: { keyWord?: string; page?: number; pageSize?: number; }) {
    return request({
        url: '/disinfectant/list',
        method: 'get',
        params: query,
    });
}

// 获取报告详情
export function getDisinfectantDetail(query: { id: string }) {
    return request({
        url: '/disinfectant',
        method: 'get',
        params: query,
    });
}

// 创建报告
export function createDisinfectant(data: any) {
    return request({
        url: '/disinfectant',
        method: 'post',
        data
    });
}

// 更新报告
export function updateDisinfectant(data: { _id: string }) {
    return request({
        url: '/disinfectant/' + data._id.toString(),
        method: 'put',
        data
    });
}

// 删除报告
export function deleteDisinfectant(id: string) {
    return request({
        url: '/disinfectant/' + id.toString(),
        method: 'delete',
    });
}

// 审核报告
export function reviewDisinfectant(data: { _id: string; reviewStatus: number; returnReason?: string; }) {
    return request({
        url: '/disinfectant/review',
        method: 'post',
        data
    });
}

// 
/**
 * 获取基础数据
 * @description 包含：使用范围、检测项目、不该用于的部位、消毒剂有效成分
 */
export function getDisinfectantDetectedItems() {
    return request({
        url: '/disinfectant/detectedItems',
        method: 'get',
    });
}

