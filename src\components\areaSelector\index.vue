<template>
    <div class="w100">
        <!-- :options="state.options"  -->
        <el-cascader class="w100" v-model="state.innerValue" :props="props" @change="handleChange" placeholder="请选择地区"
            clearable />
    </div>
    <!-- 
        配置项 | 描述 | 类型
        v-model:value="val" | val:选中项绑定值 | string[]
     -->
</template>

<script setup lang="ts" name="areaSelector">
import { reactive, watch } from 'vue'
import { getAreaDataList } from '/@/api/components/index'
const state = reactive<{ innerValue: string[] }>({
    innerValue: [],
    // 选项
    // options: [],
    // 配置项
    // props: {}
})
// props
const useProps = defineProps<{ value: Array<string> }>();
// emits
const useEmits = defineEmits(['clear'])
// 监听props.value是否改变并更新innerValue
watch(
    () => useProps.value,
    () => {
        state.innerValue = useProps.value
    },
    { immediate: true }
)

// 初始加载，获取省份
// onBeforeMount(async () => {
    // 默认获取省份列表（一级节点）
    // const res = await getAreaDataList({})
    // state.options = res.data
    // Object.assign(state.options, res.data)
// })

// 异步加载子节点(包括初始状态加载一级节点)
const getSubdata = async (node: { value: string }, resolve: (data: any) => void) => {
    const res = await getAreaDataList({ parent_code: node.value })
    const subNodes = res.data.map((item: { area_code: any; name: string; level: number; }) => ({
        value: item.area_code,
        label: item.name,
        // 第四级节点为叶子节点
        // leaf: item.level >= 3
        leaf: item.name == '市辖区' || item.level >= 3
    }))
    resolve(subNodes)
}

// 当选中叶子节点或是清空选择时触发，修改父组件v-model:value绑定值
const handleChange = (newVal: string[] | null) => {
    // console.log(newVal);
    if (newVal) {
        Object.assign(useProps.value, newVal)
    }
    else {
        useEmits('clear')
    }
    // console.log(useProps.value);
}

// 配置项
const props = {
    // value: 'area_code',
    // label: 'name',
    // 懒加载
    lazy: true,
    lazyLoad: getSubdata,
}

</script>

<style scoped lang="scss"></style>