stages:
  - check
  - build
  - release

eslint check:
  image: node:18-alpine
  stage: check
  tags:
    - nodejs-dock
  cache:
    key: GWFCache
    paths:
      - ~/.npm
  script:
    - ls $CI_PROJECT_DIR
    - npm config set registry https://registry.npmmirror.com
    - npm install --include=dev
    - npm run lint-fix
  except:
    - main

build production:
  image: node:18-alpine
  stage: build
  tags:
    - nodejs-dock
  cache:
    key: GWFCache
    paths:
      - ~/.npm
  script:
    - ls $CI_PROJECT_DIR
    - npm config set registry https://registry.npmmirror.com
    - npm install
    - export NODE_ENV=production
    - npm run build
  artifacts:
    expire_in: 1 week
    paths:
      - dist
  only:
    - main
    - fix-ggws09

release-image:
  stage: release
  image: docker:20.10.16
  tags:
    - nodejs-dock
  variables:
    CONTAINER_TEST_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:latest
  script:
    - ls -l .
    - source ./version
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker pull $CONTAINER_RELEASE_IMAGE || true
    - docker build -t $CONTAINER_TEST_IMAGE -t $CONTAINER_RELEASE_IMAGE -t $CI_REGISTRY_IMAGE:$VERSION .
    - docker push $CONTAINER_RELEASE_IMAGE
    - docker push $CI_REGISTRY_IMAGE:$VERSION
    - docker image rm $CONTAINER_TEST_IMAGE $CONTAINER_RELEASE_IMAGE $CI_REGISTRY_IMAGE:$VERSION
  only:
    - main
    - fix-ggws09