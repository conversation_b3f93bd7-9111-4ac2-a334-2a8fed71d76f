<template>
    <div class="w100 h100">
        <AsideTopBar title="公共场所监管视频" />
        <div class="vedio">
            <Vue3SeamlessScroll class="scroll-wrap " hover :list="vedioList1" :singleWaitTime="2000"
                :singleHeight="singleHeight" :step="singleHeight / 400">
                <div v-for="(item, index ) in vedioList1" :key="index" class="vedioContainer" @click="handleClick(item)">
                    <Vedio :title="item.title">
                        <!-- <img :src="item.imgUrl" style="width:100%;height: 18vh;padding: 8px;"> -->
                        <img :src="item.imgUrl" style="width: 8.4vw;height: 4.8vw;">
                    </Vedio>
                </div>
            </Vue3SeamlessScroll>
            <Vue3SeamlessScroll class="scroll-wrap " hover :list="vedioList2" :singleWaitTime="2000"
                :singleHeight="singleHeight" :step="singleHeight / 400">
                <div v-for="(item, index ) in vedioList2" :key="index" class="vedioContainer" @click="handleClick(item)">
                    <Vedio :title="item.title">
                        <!-- <img :src="item.imgUrl" style="width: 100%;"> -->
                        <img :src="item.imgUrl" style="width: 8.4vw;height: 4.8vw;">
                    </Vedio>
                </div>
            </Vue3SeamlessScroll>

            <VedioDialog ref="VedioDialogRef" />
        </div>
    </div>
</template>

<script setup lang="ts" name="center">
import { ref, onMounted } from "vue";
import AsideTopBar from './AsideTopBar.vue';
import Vedio from "./Vedio.vue";
import VedioDialog from "./VedioDialog.vue";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";
import mittBus from '/@/utils/mitt';
// 图片资源
import imgUrl1 from "/@/assets/dashBoard/1.jpg";
import imgUrl2 from "/@/assets/dashBoard/2.jpg";
import imgUrl3 from "/@/assets/dashBoard/3.jpg";
import imgUrl4 from "/@/assets/dashBoard/4.jpg";
import imgUrl5 from "/@/assets/dashBoard/5.jpg";
import imgUrl6 from "/@/assets/dashBoard/6.jpg";
import imgUrl7 from "/@/assets/dashBoard/7.jpg";

const vedioList1 = ref(
    [
        {
            title: "天平路街道-自助超市",
            imgUrl: imgUrl1,
        },
        {
            title: "长桥街道-公共影院",
            imgUrl: imgUrl3,
        },
        {
            title: "万灵街道-公共吸烟区",
            imgUrl: imgUrl5,
        },
        {
            title: "凌云街道-检测中心",
            imgUrl: imgUrl7,
        }
    ]
)

const vedioList2 = ref(
    [
        {
            title: "斜土路街道-山河博物馆",
            imgUrl: imgUrl2,
        },
        {
            title: "田林街道-田林公园",
            imgUrl: imgUrl4,
        },
        {
            title: "华光街道-服务中心",
            imgUrl: imgUrl6,
        }
    ]
)

const VedioDialogRef = ref()
const handleClick = (item: any) => {
    VedioDialogRef.value.openDialog(item);
}

// box自适应高度
const singleHeight = ref(192)
const resizeFun = () => {
    singleHeight.value = document.getElementsByClassName('dashboard-container')[0].offsetHeight * 0.3
}

onMounted(() => {
    resizeFun()
    mittBus.on('resize', resizeFun)
})

</script>

<style lang="scss" scoped>
.vedio {
    height: 90%;
    width: 100%;
    padding-right: 0.5vw;

    display: flex;
    position: relative;
    top: -1vh;

    font-size: 12px;

    @media screen and (max-width: 3840px) {
        font-size: 20px;
    }

    @media screen and (max-width: 3440px) {
        font-size: 16px;
    }

    @media screen and (max-width: 2560px) {
        font-size: 12px;
    }

    @media screen and (max-width: 1960px) {
        font-size: 10px;
    }

    @media screen and (max-width: 1440px) {
        font-size: 8px;
    }

    @media screen and (max-width: 1024px) {
        font-size: 6px;
    }


    .scroll-wrap {
        height: 100%;
        width: 50%;
        overflow: hidden;

        .vedioContainer {
            height: 30vh;
            width: 100%;
            padding-left: 1vw;
        }
    }


}
</style>