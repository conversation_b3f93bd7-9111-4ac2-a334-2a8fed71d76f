<template>
    <el-form :model="formData" :rules="rules" ref="xdjFormRef" label-position="top" scroll-to-error
        style="padding: 0 10% 2.5em 10%;">
        <el-row :gutter="50">
            <!-- 产品名称 -->
            <el-col :span="24" class="mb20">
                <el-form-item :label="'一.产品名称'" prop="name">
                    <el-input v-model="formData.name" clearable :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col :span="24" class="mb20">
                <el-form-item :label="'二.是否进口产品'" prop="isImport">
                    <el-radio-group v-model="formData.isImport" :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'三.是否能提供国产产品生产企业卫生许可证或进口产品生产国（地区）允许生产销售的证明文件及报关单'" prop="hasProve"
                    style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.hasProve" @change="() => { formData.hasProveReason = '' }"
                        :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.hasProve == '0'" :label="'不能提供原因'" prop="hasProveReason">
                    <el-input v-model="formData.hasProveReason" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'四.生产企业(制造商)名称'" prop="producer">
                    <el-input v-model="formData.producer" clearable :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'五.是否为委托加工产品'" prop="isEntrust" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.isEntrust" @change="() => { formData.entrustName = '' }"
                        :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.isEntrust == '1'" :label="'若是，委托方名称'" prop="entrustName">
                    <el-input v-model="formData.entrustName" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'六.如有生产企业卫生许可证，证上标准的生产类别是'" prop="productCategory">
                    <el-input v-model="formData.productCategory" placeholder="如：液体消毒剂，抗（抑）菌制剂" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'七.净含量'" prop="netContent">
                    <el-input v-model.number="formData.netContent" placeholder="单位%" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'八.是否有标签和说明书'" prop="hasLable" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.hasLable" @change="() => { formData.hasLableReason = '' }"
                        :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.hasLable == '0'" :label="'若无，原因'" prop="hasLableReason">
                    <el-input v-model="formData.hasLableReason" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'九.是否有企业标准或质量标准'" prop="hasStandard" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.hasStandard" @change="() => { formData.hasStandardReason = '' }"
                        :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.hasStandard == '0'" :label="'若无，原因'" prop="hasStandardReason">
                    <el-input v-model="formData.hasStandardReason" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十.是否有检验报告'" prop="hasReport" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.hasReport" @change="() => { formData.hasReportReason = '' }"
                        :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.hasReport == '0'" :label="'若无，原因'" prop="hasReportReason">
                    <el-input v-model="formData.hasReportReason" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十一.是否有配方'" prop="hasFormula" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.hasFormula" @change="() => { formData.hasFormulaReason = '' }"
                        :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.hasFormula == '0'" :label="'若无，原因'" prop="hasFormulaReason">
                    <el-input v-model="formData.hasFormulaReason" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十二.消毒剂有效成分是'" prop="activeIngredient" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.activeIngredient" :disabled="type == 'readonly'">
                        <el-radio v-for="item in activeIngredientList" :key="item._id" :label="item._id">
                            {{ item.name }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十三.有效成分的含量'" prop="ingredients">
                    <el-input v-model="formData.ingredients" placeholder="例：0.1-0.2%(w/v)" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十四.产品标签和企业标准中有效成分及含量是否一致'" prop="labelsConsistent" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.labelsConsistent"
                        @change="() => { formData.labelsConsistentReason = '' }" :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.labelsConsistent == '0'" :label="'有效成分及含量不一致原因'"
                    prop="labelsConsistentReason">
                    <el-input v-model="formData.labelsConsistentReason" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十五.使用范围'" prop="detectedItems" style="margin-bottom: 0;">
                    <el-checkbox-group v-model="useRangeSelected" :disabled="type == 'readonly'">
                        <el-checkbox v-for="item in useRange" :key="item._id" :label="item._id" style="display: block;"
                            :disabled="type == 'readonly'">
                            {{ item.name }}
                        </el-checkbox>
                    </el-checkbox-group>

                    <div v-for="detectedItem in detectedItems" :key="detectedItem._id" class="w100">
                        <div :style="{ border: `2px${themeConfig.primary} solid` }" class="detectedItem">
                            <div :style="{ border: `1px ${themeConfig.primary} solid`, 'background-color': `${themeConfig.primary}` }"
                                class="detectedItemTiTle">
                                <el-text style="color:#FFF;font-size: 1.2em;font-weight: 600;">{{ detectedItem.name
                                    }}</el-text>
                            </div>
                            <div class="detectedItemMustDo">检测项目（必做）：</div>
                            <el-checkbox-group v-model="formData.detectedItems" :disabled="type == 'readonly'">
                                <div v-for=" item  in  detectedItem.children " :key="item._id">
                                    <el-checkbox v-if="item.mustDo" :label="item._id" style="display: block;">
                                        {{ item.name }}
                                    </el-checkbox>
                                </div>
                            </el-checkbox-group>
                            <div v-if="findMustDoIs0(detectedItem.children)" class="detectedItemMustDo">检测项目（选做）：</div>
                            <el-checkbox-group v-model="formData.detectedItems" :disabled="type == 'readonly'">
                                <div v-for=" item  in  detectedItem.children " :key="item._id">
                                    <el-checkbox v-if="!item.mustDo" :label="item._id" style="display: block;">
                                        {{ item.name }}
                                    </el-checkbox>
                                </div>
                            </el-checkbox-group>
                        </div>
                    </div>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十六.企业标准中显示产品有效期(保质期)为'" prop="effectiveDuration">
                    <el-input v-model="formData.effectiveDuration" placeholder="请按格式填写。例：2年0月或0年6月" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十七.标签上是否注明是否标注有疗效'" prop="hasCurativeEffect" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.hasCurativeEffect"
                        @change="() => { formData.curativeEffect = '' }" :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.hasCurativeEffect == '1'" :label="'若是，标注有疗效原因'" prop="curativeEffect">
                    <el-input v-model="formData.curativeEffect" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十八.标签上是否注明是否标注减轻或缓解症状'" prop="lessenSymptoms" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.lessenSymptoms"
                        @change="() => { formData.lessenSymptomsReason = '' }" :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.lessenSymptoms == '1'" :label="'若是，标注减轻或缓解症状原因'"
                    prop="lessenSymptomsReason">
                    <el-input v-model="formData.lessenSymptomsReason" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'十九.标签上是否注明是否标注预防新冠肺炎'" prop="preventionCOVID19" style="margin-bottom: 0;">
                    <el-radio-group v-model="formData.preventionCOVID19"
                        @change="() => { formData.preventionCOVID19Reason = '' }" :disabled="type == 'readonly'">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="formData.preventionCOVID19 == '1'" :label="'若是，标注预防新冠肺炎原因'"
                    prop="preventionCOVID19Reason">
                    <el-input v-model="formData.preventionCOVID19Reason" :rows="2" type="textarea" clearable
                        :disabled="type == 'readonly'"></el-input>
                </el-form-item>
            </el-col>

            <el-col class="mb20">
                <el-form-item :label="'二十.不该用于部位'" prop="unusableParts" style="margin-bottom: 0;">
                    <el-checkbox-group v-model="formData.unusableParts" :disabled="type === 'readonly'">
                        <el-checkbox label="足部" style="display: block;"></el-checkbox>
                        <el-checkbox label="眼睛" style="display: block;"></el-checkbox>
                        <el-checkbox label="指甲" style="display: block;"></el-checkbox>
                        <el-checkbox label="腋部" style="display: block;"></el-checkbox>
                        <el-checkbox label="头皮" style="display: block;"></el-checkbox>
                        <el-checkbox label="头发" style="display: block;"></el-checkbox>
                        <el-checkbox label="鼻粘膜" style="display: block;"></el-checkbox>
                        <el-checkbox label="肛肠" style="display: block;"></el-checkbox>
                        <el-checkbox label="以上全无" style="display: block;"></el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-col>
            <!-- 按钮 -->
            <el-col :span="24" class="mb20">
                <el-form-item>
                    <div class="w100" style="display: flex; justify-content:center">
                        <el-button v-if="type !== 'readonly'" type="primary" @click="onSubmit(xdjFormRef)"
                            :loading="loading">提交</el-button>
                        <el-button @click="cancel">{{ type === 'readonly' ? '返回' : '取消' }}</el-button>
                    </div>
                </el-form-item>
            </el-col>

        </el-row>
    </el-form>
</template>

<script setup lang="ts" name="disinfectantForm">
import { reactive, ref, onBeforeMount, watch } from "vue";
import mittBus from '/@/utils/mitt';
import { createDisinfectant, updateDisinfectant, getDisinfectantDetail } from '/@/api/xdj/inspection'
import { getActiveIngredientList } from '/@/api/xdj/activeIngredient'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
// 携带数据首次进入
const isFirst = ref(false)
const type = ref<'add' | 'update' | 'readonly'>('add')

const xdjFormRef = ref()

const loading = ref(false)
// 
const useFormData = () => ref<xdjForm>({
    _id: '',
    name: '',                       //一 产品名称
    isImport: '0',                  //二 是否进口产品 '0': 否, '1': 是
    hasProve: '1',                  //三 是否能提供国产产品生产企业卫生许可证或进口产品生产国（地区）允许生产销售的证明文件及报关单 '0': 否, '1': 是  否的话就直接传不能提供原因
    hasProveReason: '',             //三 不能提供原因
    producer: '',                   //四 生产企业(制造商)名称
    isEntrust: '0',                 //五 是否为委托加工产品 '0': 否, '1': 是
    entrustName: '',                //五 若是，委托方名称
    productCategory: '',            //六 如有生产企业卫生许可证，证上标准的生产类别是
    netContent: undefined,          //七 净含量, 单位是%
    hasLable: '1',                  //八 是否有标签和说明书  '0': 否, '1': 是
    hasLableReason: '',             //八 若无，原因
    hasStandard: '1',               //九 是否有企业标准或质量标准 '0': 否, '1': 是
    hasStandardReason: '',          //九 若无，原因
    hasReport: '1',                 //十 是否有检验报告，'1': 是; 其他字符串代表否以及没有的原因
    hasReportReason: '',            //十 若无，原因
    hasFormula: '1',                //十一 是否有配方 '0': 否, '1': 是
    hasFormulaReason: '',           //十一 若无，原因
    activeIngredient: '',           //十二 消毒剂有效成分是， 传ActiveIngredient_id
    ingredients: '',                //十三 有效成分含量, 单位是%
    labelsConsistent: '1',          //十四 产品标签和企业标准中有效成分及含量是否一致 '0': 否, '1': 是
    labelsConsistentReason: '',     //十四 有效成分及含量不一致原因
    detectedItems: [],              //十五 使用范围及其检测项目，只要传检测项目的_id即可
    effectiveDuration: '',          //十六 企业标准中显示产品有效期(保质期)为
    hasCurativeEffect: '0',         //十七 标签上是否注明是否标注有疗效   '0': 否, '1': 是
    curativeEffect: '',             //十七 若标注有疗效原因
    lessenSymptoms: '0',            //十八 标签上是否注明是否标注减轻或缓解症状   '0': 否, '1': 是
    lessenSymptomsReason: '',       //十八
    preventionCOVID19: '0',         //十九 标签上是否注明是否标注预防新冠肺炎   '0': 否, '1': 是
    preventionCOVID19Reason: '',    //十九
    unusableParts: []               //二十 不该用于部位, 可选项为：足部\眼睛\指甲\腋部\头皮\头发\鼻粘膜\肛肠\以上全无
})
const formData = useFormData()
// 校验
const rules = reactive({
    name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
    netContent: [
        { required: true, message: '请输入净含量', trigger: 'blur' },
        { pattern: /^\d+$/, message: '净含量必须为一个数字', trigger: 'change' }
    ],
    ingredients: [{ required: true, message: '请输入有效成分的含量', trigger: 'blur' }],
    effectiveDuration: [{ required: true, message: '请输入产品有效期(保质期)', trigger: 'blur' }],
    detectedItems: [{ required: true, message: '请选择检测项目', trigger: 'blur' }],
    unusableParts: [{ required: true, message: '请选择不该用于部位', trigger: 'blur' }],
})

// 有效成分 总列表
const activeIngredientList = ref<activeIngredient[]>([])
// 有效成分 所选项 formData.value.activeIngredient 

// 使用范围
const useRangeList = ref<activeIngredient[]>([])       //总列表
const useRange = ref<activeIngredient[]>([])           //表单展示项（根据有效成分）
const useRangeSelected = ref<string[]>([])             //所选项

// 监听有效成分变化,获取使用范围
watch(
    () => formData.value.activeIngredient,
    async (newValue) => {
        useRange.value = useRangeList.value.filter(item => item.parentId === newValue)
        if (isFirst.value) {
            isFirst.value = false
        }
        else {
            // 清除 使用范围 已选值
            useRangeSelected.value = []
            formData.value.detectedItems = []
        }
    }
)

// 检测项目
const detectedItemsList = ref<activeIngredient[]>([])       //总列表
const detectedItems = ref<activeIngredient[]>([])           //表单展示项（根据有效成分）
// 检测项目 所选项 formData.value.detectedItems

// 监听 使用范围 勾选，获取 检测项目
watch(
    () => useRangeSelected.value,
    () => {
        const childrenList: string[] = []
        const detectedItemsMaped = useRangeSelected.value.map((ele) => {
            const name = useRangeList.value.find(item => item._id === ele).name
            const children = detectedItemsList.value.filter(item => item.parentId === ele)
            children.forEach(element => {
                childrenList.push(element._id)
            });
            return { _id: ele, name, children }
            // name: 使用范围.name
            // id: 使用范围.id 即检测项目的parentId
            // children：检测项目 obj[]
        })
        detectedItems.value = detectedItemsMaped
        formData.value.detectedItems = formData.value.detectedItems.filter(ele => childrenList.some(item => item === ele))
    }
)

// 获取有效成分、使用范围、检测项目
const getActiveIngredient = async () => {
    activeIngredientList.value = (await getActiveIngredientList({ level: 1 })).data
    useRangeList.value = (await getActiveIngredientList({ level: 2 })).data
    detectedItemsList.value = (await getActiveIngredientList({ level: 3 })).data
}

// 查询 检测项目 是否必做
const findMustDoIs0 = (objArray: { mustDo: 0 | 1 }[]) => {
    for (const obj of objArray) {
        if (obj.mustDo === 0) {
            return true
        }
    }
    return false
}

// 提交
const onSubmit = async (formEl: FormInstance) => {
    formEl.validate(async (valid) => {
        if (!valid) {
            return
        }
        loading.value = true

        const { name, isImport, producer, isEntrust, productCategory, netContent, activeIngredient, ingredients, detectedItems, effectiveDuration, hasCurativeEffect, lessenSymptoms, preventionCOVID19, unusableParts } = formData.value
        const data: xdjForm = { name, isImport, producer, isEntrust, productCategory, netContent, activeIngredient, ingredients, detectedItems, effectiveDuration, hasCurativeEffect, lessenSymptoms, preventionCOVID19, unusableParts }
        data.hasProve = formData.value.hasProve == '1' ? '1' : formData.value.hasProveReason
        formData.value.entrustName && (data.entrustName = formData.value.entrustName)
        data.hasLable = formData.value.hasLable == '1' ? '1' : formData.value.hasLableReason
        data.hasStandard = formData.value.hasStandard == '1' ? '1' : formData.value.hasStandardReason
        data.hasReport = formData.value.hasReport == '1' ? '1' : formData.value.hasReportReason
        data.hasFormula = formData.value.hasFormula == '1' ? '1' : formData.value.hasFormulaReason
        data.labelsConsistent = formData.value.labelsConsistent == '1' ? '1' : formData.value.labelsConsistentReason
        formData.value.curativeEffect && (data.curativeEffect = formData.value.curativeEffect)
        formData.value.lessenSymptomsReason && (data.lessenSymptomsReason = formData.value.lessenSymptomsReason)
        formData.value.preventionCOVID19Reason && (data.preventionCOVID19Reason = formData.value.preventionCOVID19Reason)

        try {
            if (type.value === 'add') {
                createDisinfectant(data)
                    .then(() => {
                        ElMessage.success('报告创建成功！')
                        mittBus.emit('xdjInspectShowTable')
                        mittBus.emit('xdjTableRefresh')
                    })
            }
            if (type.value === 'update') {
                data._id = formData.value._id
                updateDisinfectant(data)
                    .then(() => {
                        ElMessage.success('报告修改成功！')
                        mittBus.emit('xdjInspectShowTable')
                        mittBus.emit('xdjTableRefresh')
                    })
            }
        } catch (error: any) {
            // ElMessage.error('操作失败')
        } finally {
            loading.value = false
        }
    })
}
// 取消
const cancel = () => {
    mittBus.emit('xdjInspectShowTable')
}

// 获取详情并填入数据
const getFormData = async (id: string) => {
    const res = await getDisinfectantDetail({ id })
    const { name, isImport, producer, isEntrust, productCategory, netContent, activeIngredient, ingredients, detectedItems, effectiveDuration, hasCurativeEffect, lessenSymptoms, preventionCOVID19, unusableParts } = res.data
    const data: xdjForm = { name, isImport, producer, isEntrust, productCategory, netContent, activeIngredient, ingredients, detectedItems, effectiveDuration, hasCurativeEffect, lessenSymptoms, preventionCOVID19, unusableParts }
    data.hasProveReason = res.data.hasProve == '1' ? '' : res.data.hasProve
    data.hasProve = res.data.hasProve == '1' ? '1' : '0'
    res.data.entrustName && (data.entrustName = res.data.entrustName)
    data.hasLableReason = res.data.hasLable == '1' ? '' : res.data.hasLable
    data.hasLable = res.data.hasLable == '1' ? '1' : '0'
    data.hasStandardReason = res.data.hasStandard == '1' ? '' : res.data.hasStandard
    data.hasStandard = res.data.hasStandard == '1' ? '1' : '0'
    data.hasReportReason = res.data.hasReport == '1' ? '' : res.data.hasReport
    data.hasReport = res.data.hasReport == '1' ? '1' : '0'
    data.hasFormulaReason = res.data.hasFormula == '1' ? '' : res.data.hasFormula
    data.hasFormula = res.data.hasFormula == '1' ? '1' : '0'
    data.labelsConsistentReason = res.data.labelsConsistent == '1' ? '' : res.data.labelsConsistent
    data.labelsConsistent = res.data.labelsConsistent == '1' ? '1' : '0'
    res.data.curativeEffect && (data.curativeEffect = res.data.curativeEffect)
    res.data.lessenSymptomsReason && (data.lessenSymptomsReason = res.data.lessenSymptomsReason)
    res.data.preventionCOVID19Reason && (data.preventionCOVID19Reason = res.data.preventionCOVID19Reason)

    Object.assign(formData.value, data)
    formData.value._id = id
    useRangeSelected.value = Object.keys(res.data.userScopes)
    // 为了 使用范围、检测项目 不被清空
    if (formData.value.activeIngredient !== activeIngredientList.value[0]._id) {
        isFirst.value = true
    }
}

onBeforeMount(() => {
    getActiveIngredient()
})

// 打开表单
const openForm = (params: { type: 'add' | 'update' | 'readonly', id?: string }) => {
    // 重置数据
    isFirst.value = false
    Object.assign(formData.value, useFormData().value)
    useRangeSelected.value = []
    detectedItems.value = []
    // 有效成分 默认选择第一项 
    formData.value.activeIngredient = activeIngredientList.value[0]._id
    // if (params.type == 'add') { }
    if (params.type == 'update' || params.type == 'readonly') {
        params.id && getFormData(params.id)
    }
    // if (params.type == 'readonly') { }
    type.value = params.type
}

// 暴露
defineExpose({
    openForm
})

</script>

<style scoped lang="scss">
.detectedItem {

    padding: 0.5em 1em 0 1em;
    width: 100%;
    border-radius: 12px;
    margin: 3px 0;

    .detectedItemTiTle {
        border-radius: 6px;
        margin-bottom: 6px;
        padding-left: 1em;
    }

    .detectedItemMustDo {
        font-size: 1.1em;
        font-weight: 500;
        margin-bottom: 0.5em;
    }
}
</style>