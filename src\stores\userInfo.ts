import { defineStore } from 'pinia';
// import Cookies from 'js-cookie';
import { Session } from '/@/utils/storage';

import { getUserInfo } from "/@/api/login/index";
import { getFullUrl } from "/@/utils/common";


/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore('userInfo', {
	state: (): UserInfosState => ({
		userInfos: {
			_id: '',
			userName: '',
			photo: '',
			time: 0,
			roles: [],
			authBtnList: [],
			enterpriseId: ''
		},
	}),
	actions: {
		async setUserInfos(needUpdate: Boolean = false) {
			// 存储用户信息到浏览器缓存
			if (Session.get('userInfo') && !needUpdate) {
				this.userInfos = Session.get('userInfo');
			} else {
				const userInfos = <UserInfos>await this.getApiUserInfo();
				this.userInfos = userInfos;
			}
		},
		async getApiUserInfo() {
			return new Promise((resolve) => {
				let userRoles: Array<string> = [];
				let userAuthBtnList: Array<string> = [];
				// 默认基础权限
				let defaultRoles: Array<string> = ['common'];
				let defaultAuthBtnList: Array<string> = ['common', 'btn.search'];
				// superAdmin 用户页面权限
				let superAdminRoles: Array<string> = ['superAdmin'];
				// superAdmin 用户按钮权限标识
				let superAdminAuthBtnList: Array<string> = ['superAdmin', 'btn.add', 'btn.del', 'btn.edit', 'btn.search', 'api.store', 'api.user', 'api.xdj.Inspection.add', 'api.xdj.Inspection.edit'];
				// admin 用户页面权限，对应路由 meta.roles，用于控制路由的显示/隐藏
				let adminRoles: Array<string> = ['admin'];
				// admin 用户按钮权限标识
				let adminAuthBtnList: Array<string> = ['admin', 'btn.add', 'btn.del', 'btn.edit', 'btn.search', 'api.store', 'api.user'];
				// 门店负责人
				let storeManagerRoles: Array<string> = ['storeManager'];
				let storeManagerBtnList: Array<string> = ['storeManager', 'btn.add', 'btn.del', 'btn.edit', 'btn.search', 'api.xdj.Inspection.add'];
				// 总店负责人
				let headStoreManagerRoles: Array<string> = ['headStoreManager'];
				let headStoreManagerBtnList: Array<string> = ['headStoreManager', 'btn.add', 'btn.del', 'btn.edit', 'btn.search', 'api.xdj.Inspection.edit', 'api.store', 'api.user'];
				// 场所负责人
				let placeHeadRoles: Array<string> = ['placeHead'];
				let placeHeadBtnList: Array<string> = ['btn.add', 'btn.del', 'btn.edit', 'btn.search'];
				// 工程负责人
				let engineeringHeadRoles: Array<string> = ['engineeringHead'];
				let engineeringHeadBtnList: Array<string> = ['btn.add', 'btn.del', 'btn.edit', 'btn.search'];
				getUserInfo().then((res) => {
					// 不同用户给予不同的用户权限
					if (res.data.role && res.data.role === 'admin') {
						userRoles = adminRoles;
						userAuthBtnList = adminAuthBtnList;
					}
					else if (res.data.role && res.data.role === 'superAdmin') {
						userRoles = superAdminRoles;
						userAuthBtnList = superAdminAuthBtnList;
					}
					else if (res.data.role && res.data.role === 'headStoreManager') {
						userRoles = headStoreManagerRoles;
						userAuthBtnList = headStoreManagerBtnList;
					}
					else if (res.data.role && res.data.role === 'storeManager') {
						userRoles = storeManagerRoles;
						userAuthBtnList = storeManagerBtnList;
					}
					else {
						userRoles = defaultRoles;
						userAuthBtnList = defaultAuthBtnList;
					}
					const userInfos = {
						userName: res.data.name || res.data.userName,
						photo: res.data.avatar && getFullUrl(res.data.avatar),
						time: new Date().getTime(),
						roles: userRoles,
						authBtnList: userAuthBtnList,
						_id: res.data._id,
						enterpriseId: res.data?.enterpriseId
					};
					Session.set('userInfo', userInfos);
					resolve(userInfos);
				})
				// setTimeout(() => {
				// 	// 模拟数据，请求接口时，记得删除多余代码及对应依赖的引入
				// 	const userName = Cookies.get('userName');
				// 	// 模拟数据
				// 	let defaultRoles: Array<string> = [];
				// 	let defaultAuthBtnList: Array<string> = [];
				// 	// admin 页面权限标识，对应路由 meta.roles，用于控制路由的显示/隐藏
				// 	let adminRoles: Array<string> = ['admin'];
				// 	// admin 按钮权限标识
				// 	let adminAuthBtnList: Array<string> = ['btn.add', 'btn.del', 'btn.edit', 'btn.search'];
				// 	// test 页面权限标识，对应路由 meta.roles，用于控制路由的显示/隐藏
				// 	let testRoles: Array<string> = ['common'];
				// 	// test 按钮权限标识
				// 	let testAuthBtnList: Array<string> = ['btn.add', 'btn.search'];
				// 	// 不同用户模拟不同的用户权限
				// 	if (userName === 'admin') {
				// 		defaultRoles = adminRoles;
				// 		defaultAuthBtnList = adminAuthBtnList;
				// 	} else {
				// 		defaultRoles = testRoles;
				// 		defaultAuthBtnList = testAuthBtnList;
				// 	}
				// 	// 用户信息模拟数据
				// 	const userInfos = {
				// 		userName: userName,
				// 		photo:
				// 			userName === 'admin'
				// 				? 'https://img2.baidu.com/it/u=1978192862,2048448374&fm=253&fmt=auto&app=138&f=JPEG?w=504&h=500'
				// 				: 'https://img2.baidu.com/it/u=2370931438,70387529&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
				// 		time: new Date().getTime(),
				// 		roles: defaultRoles,
				// 		authBtnList: defaultAuthBtnList,
				// 	};
				// 	Session.set('userInfo', userInfos);
				// 	resolve(userInfos);
				// }, 0);
			});
		},
	},
});
