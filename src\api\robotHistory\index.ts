import request from '/@/utils/request';


export function getRobotDataList(query: API.robotDataListParams) {
    return request<API.getRobotDataListResult>({
        url: '/robot-data/list',
        method: 'get',
        params: query,
    });
}
export function getRobotAllData(query:any) {
    return request({
        url: '/robot-data/RobotAllData',
        method: 'get',
        params: query,
    });
}

export function getRobotAllDataByPlace(query:{sensorType:string,year:string}) {
    return request({
        url: '/robot-data/RobotAllDataByPlace',
        method: 'get',
        params: query,
    });
}

export function getRobotAllDataByDevice(query:{sensorType:string,year:string}) {
    return request({
        url: '/robot-data/RobotAllDataByDevice',
        method: 'get',
        params: query,
    });
}

export function getRobotAllDataBySamplePoint(query:{sensorType:string,year:string}) {
    return request({
        url: '/robot-data/RobotAllDataBySamplePoint',
        method: 'get',
        params: query,
    });
}

