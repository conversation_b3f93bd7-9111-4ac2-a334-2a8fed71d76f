<template>
    <div >
        <div class="system-device-search mb15">
            <!-- 搜索关键字 -->
            <el-input v-model="state.keyWord" :placeholder="$t('message.views.placeholderRobotDeviceKeyword')"
                style="max-width: 200px" clearable>
            </el-input>
            <!-- deviceName -->
            <el-select v-model="state.deviceName" :placeholder="$t('message.views.placeholderdeviceNickName')"
                style="max-width: 200px" clearable filterable class="ml10">
                <el-option v-for="item in DeviceNumList" :key="item.deviceModel" :label="item.deviceName"
                    :value="item.deviceName"></el-option>
            </el-select>
            <!-- 设备型号 -->
            <el-select v-model="state.deviceModel" :placeholder="$t('message.views.placeholderDeviceModel')"
                style="max-width: 200px" clearable filterable class="ml10">
                <el-option v-for="item in DeviceNumOptions" :key="item.deviceModel" :label="item.label"
                    :value="item.value"></el-option>
            </el-select>
            <!-- 设备状态 -->
            <el-select v-model="state.status" :placeholder="$t('message.views.placeholderchangeDeviceStatus')"
                style="max-width: 200px" clearable filterable class="ml10">
                <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                    :value="item.value"></el-option>
            </el-select>
            <!-- 查询按钮 -->
            <el-button type="primary" class="ml10" @click="getTableData()">
                <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }}</el-button>
            <!-- 新增设备 -->
            <el-button type="success" class="ml10" @click="onOpenAddDevice('add')">
                <el-icon><ele-FolderAdd /></el-icon>{{ $t('message.views.deviceAdd') }}</el-button>
        </div>
        <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%;height: 57vh;">
            <!-- 序号 -->
            <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
            <!-- 设备编号 -->
            <el-table-column prop="deviceNum" :label="$t('message.views.deviceNum')"
                show-overflow-tooltip></el-table-column>
            <!-- 设备编号 -->
            <el-table-column prop="deviceName" label="deviceName"
                show-overflow-tooltip></el-table-column>
            <!-- 设备名称 -->
            <el-table-column prop="deviceNickName" :label="$t('message.views.deviceName')"
                show-overflow-tooltip></el-table-column>
            <!-- 设备型号 -->
            <el-table-column prop="deviceModel" :label="$t('message.views.deviceModel')"></el-table-column>
            <!-- 设备管理员姓名 -->
            <el-table-column prop="deviceAdminName" :label="$t('message.views.deviceAdminName')"></el-table-column>
            <!-- 设备管理员电话 -->
            <el-table-column prop="deviceAdminPhone" :label="$t('message.views.deviceAdminPhone')"></el-table-column>

            <!-- 设备状态 -->
            <el-table-column prop="status" :label="$t('message.views.deviceStatus')" min-width="86">
                <template #default="scope">
                    <el-tag type="info" v-if="scope.row.status == '0'">{{ $t('message.common.status1') }}</el-tag>
                    <el-tag type="primary" v-if="scope.row.status == '1'">{{ $t('message.common.status2') }}</el-tag>
                    <el-tag type="danger" v-if="scope.row.status == '2'">{{ $t('message.common.status3') }}</el-tag>
                    <el-tag type="warning" v-if="scope.row.status == '3'">{{ $t('message.common.status4') }}</el-tag>
                    <el-tag type="danger" v-if="scope.row.status == '4'">{{ $t('message.common.status5') }}</el-tag>
                   
                </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column :label="$t('message.common.operation')" width="120">
                <template #default="scope">
                    <el-button size="small" text type="primary" @click="onOpenEditDevice('update', scope.row)">{{
                        $t('message.common.modify') }}</el-button>
                    <el-button size="small" text type="danger" @click="onRowDel(scope.row)">{{
                        $t('message.common.delete') }}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
            :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
            v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="state.tableData.total">
        </el-pagination>
        <DeviceDialog ref="DeviceDialogRef" :DeviceNumList="DeviceNumList" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="deviceMana">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getRobotDeviceList, deleteRobotDevice ,getDeviceNums} from "/@/api/robotDevice/index";

// 
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 引入组件
const DeviceDialog = defineAsyncComponent(() => import('/@/views/robotdevice/dialog.vue'));
// 组件ref
const DeviceDialogRef = ref();
// 定义变量内容
const state = reactive<robotdeviceState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 查询参数
    keyWord: '',
    status: '',
    deviceModel: '',
    deviceName:''
});

// 设备类型列表
const DeviceNumList = reactive<robotDeviceNum[]>([])

const DeviceNumOptions = ref<any>([])
const statusList=ref([
	{
		label: '待机',
		value: '0',
	},
	{
		label: '运动中',
		value: '1',
	},
	{
		label: '错误',
		value: '2',
	},
	{
		label: '断开连接',
		value: '3',
	},
	{
		label: '关机',
		value: '4',
	}
])
// 获取表格数据
const getTableData = async (keyWord: string = state.keyWord, status: string = state.status,deviceModel: string = state.deviceModel,deviceName:string=state.deviceName) => {
    state.tableData.loading = true;
    const res = await getRobotDeviceList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        keyWord,
        status,
        deviceModel,
        deviceName
    })

    const data = [...res.data.list]
    state.tableData.data = data;
    state.tableData.total = res.data.total;
    state.tableData.loading = false;
};
// 加载场所列表

// 打开新增设备弹窗
const onOpenAddDevice = (type: string) => {
    DeviceDialogRef.value.openDialog(type);
};
// 打开修改设备弹窗
const onOpenEditDevice = (type: string, row: rowRobotDeviceType) => {
    DeviceDialogRef.value.openDialog(type, row);
};

// 删除设备
const onRowDel = (row: rowRobotDeviceType) => {
    ElMessageBox.confirm(t('message.views.deleteRobotDeviceConfirm', { deviceNickName: row.deviceNickName }), t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deleteRobotDevice(row._id)
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};

// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 加载设备类型
const getDeviceNumList = async () => { 
	const res = await getDeviceNums()
	Object.assign(DeviceNumList, res.data)
    // 转换数据
    DeviceNumOptions.value = Array.from(
    new Set(res.data.map((item:any) => item.deviceModel)) // 去重
    ).map(deviceModel => ({
        label: deviceModel,
        value: deviceModel
    }));
}
// 页面加载时
onMounted(() => {
    getTableData();
    getDeviceNumList()
});
</script>

<style scoped lang="scss">
.system-device-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-device-search {
    display: flex;
    align-content: space-around;
}
</style>
