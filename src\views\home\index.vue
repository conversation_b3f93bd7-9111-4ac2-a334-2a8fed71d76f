<template>
	<div class="home layout-pd">
		<el-row :gutter="10">

			<!-- 消息通知 -->
			<el-col :xs="24" :sm="10" :md="10" :lg="8" :xl="8" class="home-info mb10">
				<el-card shadow="hover">
					<template #header>
						<span>{{ $t('message.router.elaryWarningMsg') }}</span>
						<span class="home-info-more" @click="router.push('/air/elaryWarning')">
							{{ $t('message.common.more') }}</span>
					</template>
					<div class="home-info-box">
						<ul v-if="warningList.length > 0" class="home-info-ul">
							<li v-for="item in warningList" :key="item._id" class="home-info-li">
								<a class="home-info-li-title">{{ item.content }}</a>
							</li>
						</ul>
						<!-- 暂无消息 -->
						<el-empty v-else :description="$t('message.common.noMessage')" :image-size="80" />
					</div>
				</el-card>

				<el-card shadow="hover" class="mt10">

					<template #header>
						<span>{{ $t('message.router.privateMessage') }}</span>
						<span class="home-info-more" @click="router.push('/privateMessage')">
							{{ $t('message.common.more') }}</span>
					</template>
					<div class="home-info-box">
						<ul v-if="noticeList.length > 0" class="home-info-ul">
							<li v-for="item in noticeList" :key="item._id" class="home-info-li">
								<a class="home-info-li-title">{{ item.content }}</a>
							</li>
						</ul>
						<el-empty v-else :description="$t('message.common.noMessage')" :image-size="80" />
					</div>
				</el-card>
			</el-col>
			<!-- 右侧图片 -->
			<el-col :xs="24" :sm="14" :md="14" :span="16" :lg="16" :xl="16">
				<el-card shadow="hover">

					<div class="flex-warp-item">
						<div class="flex-warp-item-box" @click="router.push('/air/latest')">
							<div class="item-img">
								<img :src="airImgUrl">
							</div>
							<div class="item-txt">
								<div class="item-txt-title"><strong>{{ $t('message.router.air') }}</strong></div>
							</div>
						</div>
						<div class="flex-warp-item-box" @click="router.push('/water/latest')">
							<div class="item-img">
								<img :src="waterImgUrl">
							</div>
							<div class="item-txt">
								<div class="item-txt-title"><strong>{{ $t('message.router.water') }}</strong></div>
							</div>
						</div>
					</div>

					<div class="flex-warp-item mt10">
						<div class="flex-warp-item-box" @click="router.push('/cleaningRoom')">
							<div class="item-img">
								<img :src="roomImgUrl">
							</div>
							<div class="item-txt">
								<div class="item-txt-title"><strong>{{ $t('message.router.cleaningRoomMonitor')
										}}</strong>
								</div>
							</div>
						</div>
						<div class="flex-warp-item-box" @click="router.push('/disinfectant')">
							<div class="item-img">
								<img :src="auditImgUrl">
							</div>
							<div class="item-txt">
								<div class="item-txt-title"><strong>{{ $t('message.router.disinfectionProductAudit')
										}}</strong></div>
							</div>
						</div>
					</div>
				</el-card>
			</el-col>
		</el-row>
	</div>
</template>

<script setup lang="ts" name="home">
import { reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { getWarningList } from "/@/api/warning";
import { getNoticeList } from "/@/api/notice";

import airImgUrl from "/@/assets/home/<USER>";
import waterImgUrl from "/@/assets/home/<USER>";
import roomImgUrl from "/@/assets/home/<USER>";
import auditImgUrl from "/@/assets/home/<USER>";
// router
const router = useRouter();
// 预警信息列表
const warningList = reactive<RowEarlyWarnningType[]>([])
// 站内消息列表
const noticeList = reactive<RowPrivateMessageType[]>([])

// 获取预警信息列表
const getWarningListData = async () => {
	// const res = await getWarningList({ page: 1, pageSize: 3 })
	const res = await getWarningList({ page: 1, pageSize: 10 })
	Object.assign(warningList, res.data.list)
}
// 获取站内消息列表
const getNoticeListData = async () => {
	const res = await getNoticeList({ page: 1, pageSize: 4 })
	Object.assign(noticeList, res.data.list)
}

onMounted(() => {
	getWarningListData()
	getNoticeListData()
})
</script>

<style scoped lang="scss">
@import '../../theme/mixins/index.scss';

.home {
	.home-info {
		.home-info-more {
			float: right;
			color: var(--el-text-color-secondary);
			font-size: 13px;

			&:hover {
				color: var(--el-color-primary);
				cursor: pointer;
			}
		}

		.home-info-box {
			height: 200px;
			overflow: hidden;
			padding-bottom: 60px;

			.home-info-ul {
				list-style: none;

				.home-info-li {
					font-size: 13px;
					padding-bottom: 10px;

					.home-info-li-title {
						display: inline-block;
						// @include text-ellipsis(1);
						color: var(--el-text-color-secondary);
						text-decoration: none;
					}

					& a:hover {
						color: var(--el-color-primary);
						cursor: pointer;
					}
				}
			}
		}
	}
}

.flex-warp-item {
	width: 100%;
	height: 100%;
	display: flex;

	.flex-warp-item-box {
		border: 1px solid var(--next-border-color-light);
		width: 100%;
		height: 100%;
		border-radius: 2px;
		display: flex;
		flex-direction: column;
		transition: all 0.3s ease;
		margin: 5px 10px;

		&:hover {
			cursor: pointer;
			border: 1px solid var(--el-color-primary);
			transition: all 0.3s ease;
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.03);

			.item-txt-title {
				color: var(--el-color-primary) !important;
				transition: all 0.3s ease;
			}

			.item-img {
				img {
					transition: all 0.3s ease;
					transform: translateZ(0) scale(1.05);
				}
			}
		}

		.item-img {
			width: 100%;
			height: 210px;
			overflow: hidden;

			img {
				transition: all 0.3s ease;
				width: 100%;
				height: 100%;
			}
		}

		.item-txt {
			flex: 1;
			padding: 15px;
			display: flex;
			flex-direction: column;
			overflow: hidden;

			.item-txt-title {
				text-overflow: ellipsis;
				overflow: hidden;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				display: -webkit-box;
				color: #666666;
				transition: all 0.3s ease;

				&:hover {
					color: var(--el-color-primary);
					text-decoration: underline;
					transition: all 0.3s ease;
				}
			}

		}
	}
}
</style>
