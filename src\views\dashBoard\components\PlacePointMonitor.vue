<template>
	<div class="large-border">
		<div ref="lottieBorderRef" style="width: 100%; height: 100%; pointer-events: none; position: absolute"></div>
		<div class="sub-box" :style="{ height: resizeHeight + 'px' }">
			<div class="sub-box-title">
				<div style="width: 20%">
					<span>{{ props.title }}</span>
				</div>
				<slot></slot>
			</div>
			<div class="sub-box-line"></div>
			<div class="sub-box-slot">
				<div ref="lineChartRef" class="lineChart" style="width:100% ;height: 100%;"></div>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts" name="MonthlyConformityRate">
import lottie from 'lottie-web';
import animationData from '../../../../src/assets/lottie/largeBorder.json';

import * as echarts from 'echarts';
import { type EChartsType } from 'echarts';

import { ref, onMounted, markRaw, watch, nextTick,onUnmounted } from 'vue';

import mittBus from '/@/utils/mitt';
import { debounce } from 'lodash';

const lottieBorderRef = ref();
const initLottie = () => {
	lottie.loadAnimation({
		container: lottieBorderRef.value, // 在模板中添加一个 ref="lottieContainer" 的容器
		animationData: animationData,
		renderer: 'svg', // 选择渲染器，可以是 'svg'、'canvas'、'html'
		loop: true, // 是否循环播放
		autoplay: true, // 是否自动播放
	});
};
onMounted(() => {
	initLottie();
});

const props = withDefaults(defineProps<{ title?: string; series?: any; xAxisData?: any; threshold?: any; xmid?: any; airParams?: any ;selectPlaceName?:string; }>(), {
	title: 'lineChart',
});

// const chartRef = ref<Ref<HTMLDivElement>>(null);
// const chartInstance = ref<EChartsType>();
const lineChartRef = ref();
const chartInstance = ref<EChartsType>();


const option = {
	tooltip: {
		trigger: 'axis',
	},
	grid: {
		top: '4%',
		right: '3%',
		bottom: 0,
		left: 0,
		containLabel: true,
	},
	xAxis: {
		type: 'category',
		data: [] as string[],
		axisLabel: {
			// rotate: 45, // 如果x轴标签太长可以旋转45度
			fontSize: 10, // 缩小字体大小
			interval: 0, // 强制显示所有标签
			formatter: (value: string) => {
				// 截断显示，例如只显示前5个字符
				return value.length > 5 ? value.substring(0, 5) + '...' : value;
			},
		},
	},
	yAxis: {
		type: 'value',
		axisLabel: {
			textStyle: {
				color: '#D9D9D9',
			},
		},
		splitLine: {
			lineStyle: {
				color: '#444',
			},
		},
	},
	series: [
		{
			name: '监测数据',
			type: 'bar', // 改为柱状图
			barWidth: '60%', // 柱子宽度
			itemStyle: {
				color: '#18CACA', // 柱子颜色
			},
			data: [] as number[],
		},
	],
	dataZoom: [
		{
			type: 'inside',
			start: 0,
			end: 100,
			minValueSpan: 5,
		},
	],
};

// 绘制
const draw = () => {
	if (chartInstance.value) {
		chartInstance.value.setOption(option);
	}
};

//初始化
const init = () => {
	if (!lineChartRef.value) return;
	// 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
	chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

	if (!chartInstance.value) {
		// chartInstance.value = echarts.init(lineChartRef.value, undefined, { renderer: 'svg' })
		chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: 'svg' }));
		draw();
	}
};

watch(
	props,
	() => {
		if (props.airParams.Ytype === 'point') {
			const selectedPlace = props.series.find((place: any) => place.placeName === props.selectPlaceName);
			option.xAxis.data=selectedPlace.samplePoints.map((point: any) => point.pointName);
			option.series[0].data = selectedPlace.samplePoints.map((item: any) => item.value);
		} else {
			// 原有单数据逻辑
			option.xAxis.data = props.series.map((item: any) => item.label);
			option.series[0].data = props.series.map((item: any) => item.value);
		}
		resizeChartfun()
	},
	{ deep: true }
);

// box自适应高度
const resizeHeight = ref(192);
const boxResize = () => {
	resizeHeight.value = (document.getElementsByClassName('dashboard-container')[0].offsetWidth / 2080) * 0.3 * 640;
};

const resizeChartfun = debounce(() => {
	boxResize();
	nextTick(() => {
		chartInstance.value.resize();
		draw();
	});
}, 500);

onMounted(() => {
	boxResize();
	init();
	mittBus.on('resize', resizeChartfun);
});
// 3. 销毁时清理资源
onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
    mittBus.off('resize', resizeChartfun);
  }
});
defineExpose({
	getInstance: () => chartInstance.value,
	draw,
});
</script>

<style scoped lang="scss">
@font-face {
	font-family: Source Han Sans CN-Medium;
	src: url('../../../../src/assets/font/Source Han Sans CN Medium.otf');
}

.lineChart {
	width: 100%;
	height: 100%;
	// 17px
	padding-bottom: 1.5em;
}

.large-border {
	width: 100%;
	height: 30vh;
	box-sizing: content-box;
	position: relative;
	//
	display: flex;
	flex-direction: column;
	justify-content: center;
}

/* subBoxBorder */
@font-face {
	font-family: Source Han Sans CN-Medium;
	src: url('../../../../src/assets/font/Source Han Sans CN Medium.otf');
}

.sub-box {
	position: relative;
	width: 100%;
	height: 100%;
	padding: 1.65em 1.25em 0.4em 1.65em;
	font-size: 12px;

	@media screen and (max-width: 2560px) {
		font-size: 12px;
	}

	@media screen and (max-width: 1960px) {
		font-size: 10px;
	}

	@media screen and (max-width: 1440px) {
		font-size: 8px;
	}

	@media screen and (max-width: 1024px) {
		font-size: 6px;
	}

	.sub-box-title {
		font-family: Source Han Sans CN, Source Han Sans CN-Medium;
		font-weight: Medium;
		text-align: left;
		color: #18caca;
		line-height: 2em;
		margin-left: 0.6em;
		text-shadow: 0px 0px 0.75em 0px rgba(86, 254, 254, 0.53);

		padding-right: 3%;
	}

	.sub-box-line {
		width: 100%;
		height: 0.35em;
		background-image: url('../../../../src/assets/images/subBox/sub-box-line.png');
		background-size: 100% auto;
	}

	.sub-box-slot {
		width: 100%;
		height: calc(100% - 3em);
	}
}
</style>