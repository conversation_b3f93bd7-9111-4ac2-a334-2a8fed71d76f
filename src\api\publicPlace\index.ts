import request from '/@/utils/request';

/**
 *  场所管理 - 获取指标数据
 */
export function getSensorData() {
    return request({
        url: '/sensor/sensorData',
        method: 'get'
    });
}

/**
 *  场所管理 - 获取场所列表(详细信息)
 */
export function getPlaceList(query: API.PlaceListParams) {
    return request<API.getPlaceListResult>({
        url: '/public-place',
        method: 'get',
        params: query,
    });
}

export function getRobotPlaceList(query: { page?: number; pageSize?: number; placeName?: string }) {
    return request({
        url: '/robotpublic-place',
        method: 'get',
        params: query,
    });
}

/**
 *  场所管理 - 获取场所详情（单个场所的全部信息）
 */
export function getPlaceDetail(query: { _id: string }) {
    return request({
        url: '/public-place/' + query._id.toString(),
        method: 'get'
    });
}

export function getRobotPlaceDetail(query: { _id: string }) {
    return request({
        url: '/robotpublic-place/' + query._id.toString(),
        method: 'get'
    });
}

/**
 *  场所管理 - 获取场所列表(只包含基本信息)
 */
export function getPlaceAll(query: { _type: 'store' | 'public_place' }) {
    return request({
        url: '/public-place/all',
        method: 'get',
        params: query,
    });
}

/**
 *  场所管理 - 新增场所
 */
export function addPlace(data: API.AddPlaceParams) {
    return request({
        url: '/public-place/',
        method: 'post',
        data
    });
}

/**
 *  场所管理 - 新增机器人场所
 */
export function addRobotPlace(data: API.AddRobotPlaceParams) {
    return request({
        url: '/robotpublic-place/',
        method: 'post',
        data
    });
}
/**
 *  场所管理 - 删除场所
 */
export function deletePlace(id: string) {
    return request({
        url: '/public-place/' + id.toString(),
        method: 'delete',
    });
}

export function deleteRobotPlace(id: string) {
    return request({
        url: '/robotpublic-place/' + id.toString(),
        method: 'delete',
    });
}

/**
 *  场所管理 - 编辑场所
 */
export function updatePlaceInfo(data: API.updatePlaceParams) {
    return request({
        url: '/public-place/',
        method: 'put',
        data
    });
}

export function updateRobotPlaceInfo(data: API.updateRobotPlaceParams) {
    return request({
        url: '/robotpublic-place/',
        method: 'put',
        data
    });
}

/**
 *  场所管理 - 获取公共场所的所有场所类型
 */
export function getPlaceTypeList() {
    return request({
        url: '/public-place/placeType',
        method: 'get',
    });
}