<template>
    <div class="system-water-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%">
                <el-table-column type="index" :label="$t('message.common.index')" width="66" />
                <el-table-column prop="sname" :label="$t('message.views.waterSname')" min-width="270"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="lasttesttime" min-width="180"
                    :label="$t('message.views.receiveTime')"></el-table-column>
                <el-table-column prop="pH" label="pH" min-width="120"></el-table-column>
                <el-table-column prop="浊度" :label="$t('message.views.turbidity')" min-width="120"></el-table-column>
                <el-table-column prop="电导率" :label="$t('message.views.conductivity')" min-width="120"></el-table-column>
                <el-table-column prop="水温" :label="$t('message.views.waterTemperature')" min-width="120"></el-table-column>
                <el-table-column prop="总氯" :label="$t('message.views.totalChlorine')" min-width="120"></el-table-column>
            </el-table>

        </el-card>
    </div>
</template>

<script setup lang="ts" name="waterLatest">
import { reactive, onMounted } from 'vue'
import { getWaterLatest } from "/@/api/water/index"
import { Session } from '/@/utils/storage';
import { tofixN } from "/@/utils/toolsValidate";
import { ElMessage } from "element-plus";

const state = reactive<WaterLatestState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
    },
})

// 获取表格数据
const getTableData = async () => {
    state.tableData.loading = true
    const sessionLatest = Session.get('WaterLatestData')
    // 如果session中存在数据且数据在五分钟内获取则直接使用
    const isNotEmpty = sessionLatest != 'undefined' && sessionLatest != null
    if (isNotEmpty) {
        const lasttestTime = Date.parse(sessionLatest[0].lasttesttime)
        const nowTime = new Date().getTime()
        const timeDiff = nowTime - lasttestTime
        // 五分钟之内，直接获取session
        if (timeDiff < 1000 * 60 * 5) {
            state.tableData.data = sessionLatest
            state.tableData.total = sessionLatest.length
            state.tableData.loading = false
            return
        }
    }
    // 否则发送请求获取数据
    try {
        const res = await getWaterLatest()
        // 映射
        const tableData = res.data.map((item: { sname: string; lasttesttime: string; data: object; }) => {
            const obj = <WaterLatestRowType>{
                sname: item.sname,
                lasttesttime: item.lasttesttime
            }
            for (let i of item.data as Array<{ xmid: string; val: string }>) {
                let key: string = i.xmid
                if (key === '浊度') { obj[key] = tofixN(i.val, 3) }
                else { obj[key] = tofixN(i.val, 2) }
            }
            return obj
        })
        Session.set('WaterLatestData', tableData)
        state.tableData.data = tableData
        state.tableData.total = tableData.length
        state.tableData.loading = false
    } catch (error) {
        ElMessage.error('网络请求失败')
        state.tableData.loading = false
    }
}

// 初始化
onMounted(() => {
    getTableData()
})

</script>

<style scoped>
.system-water-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}
</style>
