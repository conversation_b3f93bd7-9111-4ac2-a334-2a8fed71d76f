import request from '/@/utils/request';


export function getPoi(deviceName:string) {
    return request({
        url: '/poi',
        method: 'get',
        params: {deviceName},
    });
}
/**
 *  任务管理 - 获取列表(详细信息)
 */
export function getTaskList(query: API.TaskListParams) {
    return request<API.getTaskListResult>({
        url: '/task',
        method: 'get',
        params: query,
    });
}

export function checkDeviceStatus(deviceID:string) {
    return request({
        url: '/task/runningTask/'+deviceID.toString(),
        method: 'get',
    });
}

export function addTask(data: API.AddTaskParams) {
    return request({
        url: '/task/',
        method: 'post',
        data
    });
}

export function deleteTask(id: string) {
    return request({
        url: '/task/' + id.toString(),
        method: 'delete',
    });
}

export function updateTaskInfo(data: API.updateTaskParams) {
    return request({
        url: '/task/',
        method: 'put',
        data
    });
}
