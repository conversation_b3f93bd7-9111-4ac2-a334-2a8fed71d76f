declare namespace API {

    type measuredPlacesParams = {
        deviceName: string;
    }
    type robotDataByDeviceAndPlaceParams={
        deviceName: string;
        robotPubilcPlace:string;
    }
    type filteredRobotDataParams={
        deviceName: string;
        robotPubilcPlace:string;
        sensorType:string;
        timeType:string;
        startTime:string,
        endTime:string
    }
}