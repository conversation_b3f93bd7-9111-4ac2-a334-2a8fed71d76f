<template>
	<div class="system-place-container layout-padding">
		<el-card shadow="hover" class="layout-padding-auto">
			<div class="devices" v-if="DevicesWithLatestDatas.devices.length > 0">
				<div class="device_title">{{ $t('message.router.latest_CO2') }}</div>
				<div class="cards">
					<el-card class="elcard" v-for="(item, index) in DevicesWithLatestDatas.devices" :key="index" shadow="hover">
						<template #header>
							<div class="card-header">
								<div class="card-header-left">{{ item.name }}</div>
								<div class="card-header-right">{{ item.deviceNum }}</div>
							</div>
						</template>
						<div class="content">
							<div class="place">{{ item.publicPlace.placeName }}</div>
							<div class="latest">
								<div class="time">{{ formatTime(item.latestDetectedData.time) }}</div>
								<div class="value">
									<el-tag :type="item.latestDetectedData.data >= item.threshold ? 'danger' : 'success'">{{ item.latestDetectedData.data }}</el-tag>
								</div>
							</div>
						</div>
						<el-link @click="lookCO2info" type="primary" :underline="false">查看详情</el-link>
					</el-card>
				</div>
			</div>
			<div class="devices" v-if="DevicesWithLatestDatas.robotDevices.length > 0">
				<div class="device_title">{{ $t('message.router.latest_Robot') }}</div>
				<div class="cards">
					<el-card class="elcard" v-for="(item, index) in DevicesWithLatestDatas.robotDevices" :key="index" shadow="hover">
						<template #header>
							<div class="card-header">
								<div class="card-header-left">{{ item.deviceNickName }}</div>
								<div class="card-header-right">{{ item.deviceNum }}</div>
							</div>
						</template>
						<div class="content">
							<div class="place" v-if="item.robotPublicPlace">
								<div>{{ item.robotPublicPlace.placeName }}</div>
								<div>{{ item.latestRobotData.sensorData.sample_point }}</div>
							</div>
							<div class="place" v-else>未关联场所</div>
							<div class="latest" v-if="item.latestRobotData">
								<div class="time">{{ formatTime(item.latestRobotData.time) }}</div>
								<div class="value" v-if="item.latestRobotData.result">
									<el-tag :type="item.latestRobotData.result === '不合格' ? 'danger' : 'success'" @click="handleClick(item)">{{ item.latestRobotData.result }} ></el-tag>
								</div>
								<div class="value" v-else>
									<el-tag type="primary" @click="handleClick2(item)">查看数据</el-tag>
								</div>
							</div>
							<div class="latest" v-else>
								<div class="time">暂无检测数据</div>
							</div>
						</div>
						<el-link type="primary" @click="lookRobotinfo(item)" :underline="false">查看详情</el-link>
					</el-card>
				</div>
			</div>
		</el-card>
		<Dialog :showrow="showrow" :showNoRangerow="showNoRangerow" ref="dialogRef"></Dialog>
	</div>
</template>

<script setup lang="ts" name="disinfectant">
import { formatTime } from '/@/utils/formatTime';
import { defineAsyncComponent, onMounted, ref ,nextTick} from 'vue';
// import { ElMessageBox, ElMessage } from 'element-plus';
// import { useI18n } from 'vue-i18n';
// const { t } = useI18n();
import { useRouter } from 'vue-router';
import { getAllDevicesWithLatestData } from '/@/api/latest/index';

const router = useRouter();
const DevicesWithLatestDatas = ref<any>({
	robotDevices: [],
	devices: [],
});
const Dialog = defineAsyncComponent(() => import('/@/views/air/robothistory/dialog.vue'));

const showrow = ref({});

const showNoRangerow = ref({});

const dialogRef = ref();

const getAllDevicesWithLatestDatas = async () => {
	const res = await getAllDevicesWithLatestData();
	DevicesWithLatestDatas.value = res.data;
};

const handleClick = (data: any) => {
	// console.log(data, 'data');
	if(data.robotPublicPlace){
		showrow.value = {
			testsensorData: data.latestRobotData.sensorData,
			sensorData: data.robotPublicPlace ? data.robotPublicPlace.sensorData : [],
			time: formatTime(data.latestRobotData.time),
		};
		nextTick(() => {
			dialogRef.value.opendialogTableVisible();
		});
	}else{
		handleClick2(data)
	}

};

const handleClick2 = (data: any) => {
	showNoRangerow.value = {
		time:formatTime(data.latestRobotData.time),
		testsensorData:data.latestRobotData.sensorData,
	};
	// console.log(showNoRangerow.value, 'showNoRangerow.value');

	nextTick(() => {
		dialogRef.value.opendialogTableVisible2();
	});
};

const lookCO2info = () => {
	router.push({
		name: 'airLatest_CO2',
	});
};
const lookRobotinfo = (data: any) => {
	// console.log(data, 'data');
	router.push({
		name: 'airLatest_Robot',
		query: { deviceName: data.deviceName,deviceNickName:data.deviceNickName,deviceNum:data.deviceNum,robotPublicPlace:data.robotPublicPlace?data.robotPublicPlace._id:'norobotPubilcPlace'},
	});
};
onMounted(() => {
	getAllDevicesWithLatestDatas();
});
</script>

<style scoped lang="scss">
.layout-padding-auto{
	overflow-y: auto;
}
.devices {
	margin-bottom: 20px;
	.device_title {
		position: relative;
		padding-left: 16px;
		font-weight: bold;
		margin-bottom: 16px;
		&::before {
			position: absolute;
			left: 0;
			top: 50%;
			transform: translate(0%, -50%);
			content: '';
			display: inline-block;
			width: 4px;
			height: 20px;
			background-color: #409eff;
			margin-right: 8px;
		}
	}
	.cards {
		display: flex;
		flex-wrap: wrap;
		.elcard {
			width: 30%;
			margin: 10px;
			cursor: pointer;
			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				.card-header-right {
					text-align: right;
				}
				div {
					flex: 1;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
			.content {
				.latest {
					margin: 10px 0;
					display: flex;
					justify-content: space-between;
					align-items: center;
					.time{
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}
				.place {
					display: flex;
					justify-content: space-between;
					align-items: center;
					div {
						flex: 1;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						&:last-child{
							text-align: right;
						}
					}
				}
			}
		}
	}
}
</style>