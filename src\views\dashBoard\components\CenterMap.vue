<template>
	<div class="centerMap">
		<div class="centerMap-border">
			<img src="../../../../src/assets/dashBoard/mapBorderLeft.svg" style="height: 65vh; position: relative; left: -1vw" />
			<img src="../../../../src/assets/dashBoard/mapBorderRight.svg" style="height: 65vh; position: relative; left: 1vw" />
		</div>
		<!-- <div id="map" ref="mapRef" class="w100 h100"></div> -->
		<div class="mapbox w100 h100" >
			<div id="map" style="width: 100%; height: 88%"></div>
		</div>
		<div class="expot Img" @click="exportFullMapWithOverlay" >
			<!-- 导出图片 -->
			<el-button type="primary" :class="{ 'loading-transparent': isLoading }" :loading="isLoading" style="background-color: #ffffff00; border: #ffffff00;color: #18caca;">导出图片</el-button>
		</div>
		<!-- 年份选择 -->
		<div class="yearSelect-btn">
			<!-- :disabledDate="disabledDate" -->
			<el-date-picker
				v-model="year"
				@change="yearChanged"
				type="year"
				size="small"
				placeholder="Pick a year"
				style="position: absolute; top: 3vh; left: 1vw"
				:teleported="false"
				:clearable="false"
				format="YYYY年"
			>
			</el-date-picker>
		</div>
		<!-- 地图点击时展示的DOM元素 -->
		<div v-show="false">
			<!-- 数据来源：watch监听store中的检测数据更新 -> 更新用于渲染图表的option -> tooltip点击事件映射option数据到mapDetailData -->
			<!-- list:柱条数据 { name:柱条左侧名称，value:值，total：总数}  value和total用于计算柱条长度 -->
			<!-- <MapDetail id="detailTooltip" :title="'IM title'" :list="[{ name: 'test', value: 100000, total: 100 },{ name: 'test', value: 75, total: 100 },{ name: 'test', value: 50, total: 100 },{ name: 'test', value: 25, total: 100 },{ name: 'test', value: 0, total: 100 }]" /> -->
			<!-- <MapDetail id="detailTooltip" :mapDetailData="mapDetailData" /> -->
		</div>
	</div>
</template>
<script setup lang="ts" name="CenterMap">
import { getDetectionDataList } from '/@/api/air';
import { getLatestRobotDataByPlace } from '/@/api/latest/index';
import { ref, onMounted, watch } from 'vue';
// import * as echarts from 'echarts';
// 标点详情组件
// import MapDetail from './MapDetail.vue';
// 引入地图json数据
// import map from '../../../../src/assets/dashBoard/map.json'
// import map from '../../../../src/assets/dashBoard/xh_town.json';
// echarts配置项图片资源
// import regionName from '/@/assets/dashBoard/regionName.svg';
// import WhiteSymbol from '/@/assets/dashBoard/scatterWhitePosition.png';
import buleSymbol from '/@/assets/dashBoard/scatterBluePosition.png';
import orangeSymbol from '/@/assets/dashBoard/scatterOrangePosition.png';
import redSymbol from '/@/assets/dashBoard/scatterRedPosition.png';
// store
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '/@/stores/dashBoard';
// tools
// import mittBus from '/@/utils/mitt';
// import { debounce } from 'lodash';

const dashBoardStore = useDashBoardStore();
const { year, waterStatistics, RobotPlaceList, CO2PlaceList } = storeToRefs(dashBoardStore);
const isLoading=ref(false)
const mapLegendItems = [
  { icon: redSymbol, label: '公共场所CO2' },
  { icon: orangeSymbol, label: '空气巡航' },
  { icon: buleSymbol, label: '末梢水检测点' },
];
// 不能选择超过当前事件的日期
// const disabledDate = (time: Date) => {
//   return time.getTime() > Date.now()
// }
// 获取数据
dashBoardStore.selectedYearChange();

// 选择年度变化时获取对应年份数据
const yearChanged = () => {
	dashBoardStore.selectedYearChange();
};

watch(RobotPlaceList, () => {
	scatterData.value.air = RobotPlaceList.value.map((item: any) => {
		return {
			name: item.placeName,
			value: [item.location[0], item.location[1]],
			type: '空气质量巡检',
			...item,
		};
	});
	addMapMarkers();
});

// 监听末梢水数据变化
watch(waterStatistics, () => {
	scatterData.value.water = waterStatistics.value.map((item) => ({
		name: item.placeName,
		type: '末梢水',
		value: [item.location[0] * 0.9999, item.location[1]],
		list: item.statistics ? item.statistics.map((ele: any) => ({ name: ele.name, value: ele.exceedCount, total: ele.totle })) : [],
	}));
	addMapMarkers();
});

// 监听公共场所CO2
watch(CO2PlaceList, () => {
	// console.log('CO2ByYear', CO2ByYear.value);
	scatterData.value.CO2 = CO2PlaceList.value.map((item) => ({
		name: item.placeName,
		type: 'CO2',
		value: [item.location[0], item.location[1]],
		...item,
	}));
	addMapMarkers();
});

// resize
// const resizeChartfun = debounce(() => {
// 	nextTick(() => {
// 	});
// }, 500);
// import html2canvas from 'html2canvas';

// const exportMapByHtml2Canvas = async () => {
//   const mapElement = document.getElementById('map');
//   const exportButton = document.querySelector('.expot.Img');
//   if (!mapElement || !exportButton) return;
//   isLoading.value=true
//   exportButton.setAttribute('disabled', 'true');
//   try {
//     // 等待地图瓦片加载（经验值，也可监听加载事件）
//     await new Promise((resolve) => setTimeout(resolve, 1000));

//     // 执行截图
//     const canvas = await html2canvas(mapElement, {
// 	  scale: 1,
// 	  removeContainer: true,
//       useCORS: true, // 允许跨域图片
//       logging: false,
//       backgroundColor: null, // 保留透明背景
//       allowTaint: true, // 允许截图非同源图像
// 	  imageTimeout:10000,
// 	  ignoreElements: (e) => {
		
// 		return true;
// 	  },
//     });

//     // 生成图片下载
//     const now = new Date();
//     const timestamp =
//       now.getFullYear() +
//       String(now.getMonth() + 1).padStart(2, '0') +
//       String(now.getDate()).padStart(2, '0') +
//       String(now.getHours()).padStart(2, '0') +
//       String(now.getMinutes()).padStart(2, '0');

//     const link = document.createElement('a');
//     link.href = canvas.toDataURL('image/png');
//     link.download = `地图截图_${timestamp}.png`;
//     link.click();
//   } catch (err) {
//     console.error('截图失败', err);
//   } finally {
//     exportButton.removeAttribute('disabled');
// 	isLoading.value=false
//   }
// };
// 导出图片
const exportFullMapWithOverlay = async () => {
	try {
		isLoading.value=true
		const mapElement = document.getElementById('map');
		if (!mapElement) {
			// console.warn('未找到地图容器');
			return;
		}

		// console.log('🧩 开始拼接瓦片图...');
		const tileImgs = Array.from(mapElement.querySelectorAll('img')).filter((img) => {
			const src = img.src || '';
			return (
			src.includes('tianditu.gov.cn') &&
			!src.includes('marker') &&
			!src.includes('icon')
			);
		});

		if (!tileImgs.length) {
			// console.warn('找不到瓦片图层');
			return;
		}

		const mapRect = mapElement.getBoundingClientRect();
		const ratio = window.devicePixelRatio || 2;

		const baseCanvas = document.createElement('canvas');
		baseCanvas.width = mapRect.width * ratio;
		baseCanvas.height = mapRect.height * ratio;
		baseCanvas.style.width = `${mapRect.width}px`;
		baseCanvas.style.height = `${mapRect.height}px`;
		const ctx = baseCanvas.getContext('2d');
		if (!ctx) return;
		ctx.scale(ratio, ratio); // ⬅️ 关键：缩放上下文，绘制保持不变但导出更清晰

		// 拼接瓦片图
		await Promise.all(tileImgs.map((img) => {
			return new Promise<void>((resolve) => {
			const tileRect = img.getBoundingClientRect();
			const x = tileRect.left - mapRect.left;
			const y = tileRect.top - mapRect.top;

			const tempImg = new Image();
			tempImg.crossOrigin = 'anonymous';
			tempImg.src = img.src;
			tempImg.onload = () => {
				ctx?.drawImage(tempImg, x, y, tileRect.width, tileRect.height);
				resolve();
			};
			tempImg.onerror = () => {
				// console.warn('⚠️ 瓦片加载失败', img.src);
				resolve();
			};
			});
		}));

		// 获取地图对象
		const map = mapInstance.value;
		if (!map || !ctx) {
			// console.error('❌ 无法获取地图对象或绘图上下文');
			return;
		}

		const zoom = map.getZoom?.();
		const center = map.getCenter?.();
		if (!center || typeof center.lng !== 'number' || typeof center.lat !== 'number') {
			// console.error('❌ 获取中心点失败:', center);
			return;
		}

		// console.log(`📍 当前 zoom: ${zoom}, 中心点:`, center);

		// 经纬度转 canvas 像素
		const TILE_SIZE = 256;
		const lng2tile = (lng: number) => ((lng + 180) / 360) * Math.pow(2, zoom);
		const lat2tile = (lat: number) => {
			const rad = (lat * Math.PI) / 180;
			return (
			(1 - Math.log(Math.tan(rad) + 1 / Math.cos(rad)) / Math.PI) / 2 *
			Math.pow(2, zoom)
			);
		};

		const centerTileX = lng2tile(center.lng);
		const centerTileY = lat2tile(center.lat);

		const iconCache = new Map<string, HTMLImageElement>();

		// 绘制 markers
		if (!markers.value || !Array.isArray(markers.value)) {
			// console.warn('⚠️ markers.value 不存在或不是数组');
			return;
		}

		// console.log('🧩 绘制标注点总数：', markers.value.length);

		for (const marker of markers.value) {
			try {
			const { lng, lat } = marker.or;
			const x = (lng2tile(lng) - centerTileX) * TILE_SIZE + baseCanvas.width / (2 * ratio);
			const y = (lat2tile(lat) - centerTileY) * TILE_SIZE + baseCanvas.height / (2 * ratio);

			const iconUrl = marker.options?.icon?.options?.iconUrl;
			if (!iconUrl) {
				// console.warn('⚠️ 找不到图标 URL：', marker);
				continue;
			}

			let iconImg = iconCache.get(iconUrl);
			if (!iconImg) {
				iconImg = new Image();
				iconImg.crossOrigin = 'anonymous';
				iconImg.src = iconUrl;
				await new Promise((resolve) => {
				iconImg!.onload = resolve;
				iconImg!.onerror = () => {
					// console.warn('❌ 图标加载失败：', iconUrl);
					resolve(null);
				};
				});
				iconCache.set(iconUrl, iconImg);
			}

			if (iconImg) {
				const iconWidth = 20;
				const iconHeight = 24;
				ctx.drawImage(iconImg, x - iconWidth / 2, y - iconHeight / 2, iconWidth, iconHeight);
			}
			} catch (e) {
			// console.error('❌ 绘制 marker 失败:', marker, e);
			}
		}

		// Step 3: 绘制图例（右上角贴边）
		const legendPadding = 0;
		const legendWidth = 140;
		const legendLineHeight = 24;
		const legendHeight = mapLegendItems.length * legendLineHeight + 20;

		const legendX = baseCanvas.width / ratio - legendWidth - legendPadding;
		const legendY = legendPadding;

		ctx.fillStyle = 'rgba(0,0,0,0.5)';
		ctx.fillRect(legendX, legendY, legendWidth, legendHeight);

		ctx.font = '12px sans-serif';
		ctx.fillStyle = '#fff';
		ctx.textBaseline = 'middle';

		for (let i = 0; i < mapLegendItems.length; i++) {
			const item = mapLegendItems[i];
			const iconImg = new Image();
			iconImg.src = item.icon;
			await new Promise((resolve) => {
			iconImg.onload = resolve;
			iconImg.onerror = resolve;
			});

			const iconY = legendY + 10 + i * legendLineHeight;
			ctx.drawImage(iconImg, legendX + 10, iconY, 16, 20);
			ctx.fillText(item.label, legendX + 10 + 24, iconY + 10);
		}

		// 下载图片
		const now = new Date();
		const timestamp =
			now.getFullYear() +
			String(now.getMonth() + 1).padStart(2, '0') +
			String(now.getDate()).padStart(2, '0') +
			String(now.getHours()).padStart(2, '0') +
			String(now.getMinutes()).padStart(2, '0');

		const link = document.createElement('a');
		link.href = baseCanvas.toDataURL('image/png');
		link.download = `天地图${timestamp}.png`;
		link.click();
		isLoading.value=false
	} catch (error) {
		isLoading.value=false
	}
};

const tableData = ref<any>({});
const getLatestRobotDataByPlaces = async () => {
	const res = await getLatestRobotDataByPlace({
		robotPubilcPlace: mapDetailData.value._id,
	});

	// 2. 提取基础字段
	const deviceInfo = res.data.deviceInfo;
	const deviceNickName = deviceInfo && deviceInfo.deviceNickName ? deviceInfo.deviceNickName : '未知设备';

	const robotData = res.data.robotData;
	const timestamp = robotData.time;

	// 3. 时间戳转换 (格式: YYYY-MM-DD HH:mm:ss)
	const formatTime = (ts: number) => {
		if (!ts) return '无时间数据';
		const date = new Date(ts);
		const pad = (num: number) => (num < 10 ? '0' + num : num.toString());
		return (
			`${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +
			`${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
		);
	};

	// 4. 数据校验逻辑
	let finalResult = '无有效数据';
	if (robotData.sensorData && mapDetailData.value.sensorData) {
		let allValid = true;
		let hasData = false;

		mapDetailData.value.sensorData.forEach((sensor: any) => {
			if (sensor && sensor.choose) {
				const sensorData = robotData.sensorData;
				const value = sensorData && sensorData[sensor.EnglishName];
				if (value !== undefined && value !== null) {
					hasData = true;
					const rangeArray = sensor.sensorPlaceAndRange;
					const range = rangeArray && rangeArray[0] && rangeArray[0].Range;
					if (range) {
						const minValue = range.minValue ? parseFloat(range.minValue) : NaN;
						const maxValue = range.maxValue ? parseFloat(range.maxValue) : NaN;
						const minType = range.minValueType;
						const maxType = range.maxValueType;

						let isValid = true;
						if (!isNaN(minValue)) {
							isValid = minType === 2 ? value >= minValue : value > minValue;
						}
						if (!isNaN(maxValue) && isValid) {
							isValid = maxType === 2 ? value <= maxValue : value < maxValue;
						}

						if (!isValid) allValid = false;
					}
				}
			}
		});

		finalResult = hasData ? (allValid ? '合格' : '不合格') : '无有效数据';
	}
	tableData.value = {
		deviceName: deviceNickName,
		time: formatTime(timestamp),
		result: finalResult,
	};
};

const CO2Data = ref<any>({});
const getLatestCO2Data = async () => {
	const res = await getDetectionDataList({
		page: 1,
		pageSize: 1,
	});
	CO2Data.value = res.data.list[0];
};
// 声明天地图全局类型
declare global {
	interface Window {
		T: any;
	}
}
// // 从store映射散点图数据
const scatterData = ref({
	air: [] as any,
	water: [] as any,
	CO2: [] as any,
});
const tdtToken = ref('925af82a336ce5cec50e8529f2267a3f'); // 替换为实际API Key
const mapInstance = ref<any>(null); // 存储地图实例
// 初始化天地图
const initTiandituMap = () => {
	if (!window.T) {
		// console.error('天地图API未加载');
		return;
	}

	const mapElement = document.getElementById('map');
	if (!mapElement) return;

	mapInstance.value = new window.T.Map(mapElement, {
		projection: 'EPSG:4326', // 使用经纬度坐标系
	});
	// 徐汇区中心点坐标（约）
	const xuhuiCenter = new window.T.LngLat(121.43, 31.18);

	// 设置初始视图（中心点+缩放级别）
	mapInstance.value.centerAndZoom(xuhuiCenter, 13);

	// 可选：设置地图范围限制（徐汇区边界坐标）
	const southWest = new window.T.LngLat(120.85, 30.67); // 西南角（金山附近）
	const northEast = new window.T.LngLat(122.2, 31.87); // 东北角（崇明岛附近）
	const bounds = new window.T.LngLatBounds(southWest, northEast);

	// 限制地图显示范围
	mapInstance.value.setMaxBounds(bounds);
	mapInstance.value.setMinZoom(10); // 最小缩放级别
	mapInstance.value.setMaxZoom(18); // 最大缩放级别
	//   mapInstance.value.setStyle('black')
	// 添加标记点
	addMapMarkers();
	// 添加图例控件
	addMapLegend();

	// 添加地图点击事件监听器
	mapInstance.value.addEventListener('click', () => {
		// 关闭所有信息窗口
		markers.value.forEach((marker: any) => {
			marker.closeInfoWindow();
		});
	});
};
// 加载天地图脚本
const loadTiandituScript = (): Promise<void> => {
	return new Promise((resolve, reject) => {
		if (window.T) {
			initTiandituMap();
			resolve();
			return;
		}

		const tdtScript = document.createElement('script');
		tdtScript.type = 'text/javascript';
		tdtScript.src = `https://api.tianditu.gov.cn/api?v=4.0&tk=${tdtToken.value}`;
		tdtScript.onerror = (e: Event | string) => {
			// console.error('天地图API加载失败', e);
			reject(e);
		};

		tdtScript.onload = () => {
			// console.log('天地图API加载成功');
			initTiandituMap();
			resolve();
		};

		document.head.appendChild(tdtScript);
	});
};
const markers = ref<any>([]);
const mapDetailData = ref<any>(null);
// 添加标注
const addMapMarkers = () => {
	if (!mapInstance.value || !window.T) return;

	// 清除现有标注
	if (markers.value.length > 0) {
		markers.value.forEach((marker: any) => {
			mapInstance.value?.removeOverLay(marker);
		});
		markers.value = [];
	}
	// 根据数据类型选择图标
	const getMarkerIcon = (type: string) => {
		switch (type) {
			case 'CO2':
				return redSymbol;
			case '空气质量巡检':
				return orangeSymbol;
			case '末梢水':
				return buleSymbol;
			default:
				return redSymbol;
		}
	};

	// 遍历 scatterData 添加标注
	Object.entries(scatterData.value).forEach(([category, items]) => {
		if (!Array.isArray(items)) return;

		items.forEach((item: any) => {
			if (!item.value || item.value.length !== 2) return;

			const [lng, lat] = item.value;
			const icon = getMarkerIcon(item.type || category);

			const marker = new window.T.Marker(new window.T.LngLat(lng, lat), {
				icon: new window.T.Icon({
					iconUrl: icon,
					iconSize: new window.T.Point(20, 24), // 明确宽高
					iconAnchor: new window.T.Point(10, 12), // 锚点居中
				}),
			});
			let infoWin1 = new window.T.InfoWindow();
			// 添加点击事件
			marker.addEventListener('click', async () => {
				mapDetailData.value = item;

				// 立即打开信息窗口，显示加载状态
				const loadingContent = `
          <div style="width:300px;">
            <h3 style="margin:0 0 10px 0;color:#333;">${item.name}</h3>
            <p style="margin:0 0 5px 0;color:#666;">类型：${item.type}</p>
            <p style="margin:10px 0;color:#666;text-align:center;">数据加载中...</p>
          </div>
        `;
				infoWin1.setContent(loadingContent);
				marker.openInfoWindow(infoWin1);

				if (item.type === '空气质量巡检') {
					try {
						await getLatestRobotDataByPlaces();
						// 数据加载完成后，更新信息窗口内容
						const updatedContent = createInfoWindowContent(item);
						infoWin1.setContent(updatedContent);
						marker.openInfoWindow(infoWin1); // 重新打开以刷新内容
					} catch (error) {
						// console.error('数据加载失败:', error);
						const errorContent = `
              <div style="width:300px;">
                <h3 style="margin:0 0 10px 0;color:#333;">${item.name}</h3>
                <p style="margin:0 0 5px 0;color:#666;">类型：${item.type}</p>
                <p style="margin:10px 0;color:#ff4d4f;text-align:center;"暂无数据</p>
              </div>
            `;
						infoWin1.setContent(errorContent);
						marker.openInfoWindow(infoWin1);
					}
				} else if (item.type === 'CO2') {
					try {
						await getLatestCO2Data();
						// 数据加载完成后，更新信息窗口内容
						const updatedContent = createInfoWindowContent(item);
						infoWin1.setContent(updatedContent);
						marker.openInfoWindow(infoWin1); // 重新打开以刷新内容
					} catch (error) {
						// console.error('数据加载失败:', error);
						const errorContent = `
                  <div style="width:300px;">
                    <h3 style="margin:0 0 10px 0;color:#333;">${item.name}</h3>
                    <p style="margin:0 0 5px 0;color:#666;">类型：${item.type}</p>
                    <p style="margin:10px 0;color:#ff4d4f;text-align:center;"暂无数据</p>
                  </div>
                `;
						infoWin1.setContent(errorContent);
						marker.openInfoWindow(infoWin1);
					}
				} else {
					infoWin1.setContent(createInfoWindowContent(item));
					marker.openInfoWindow(infoWin1);
				}
			});
			marker.type=item.type
			mapInstance.value?.addOverLay(marker);
			markers.value.push(marker);
		});
	});
};
// 添加图例函数
const addMapLegend = () => {
	if (!window.T || !mapInstance.value) return;

	// 创建图例容器
	const legend = document.createElement('div');
	legend.style.position = 'absolute';
	legend.style.top = '0px';
	legend.style.right = '0px';
	legend.style.color = '#fff';
	legend.style.zIndex = '1000';
	legend.style.fontSize = '12px';
	legend.style.padding = '10px';
	legend.style.borderRadius = '0 0 0 5px';
	legend.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
	// 添加图例项
	const legendItems = [
		{ icon: redSymbol, label: '公共场所CO2' },
		{ icon: orangeSymbol, label: '空气巡航' },
		{ icon: buleSymbol, label: '末梢水检测点' },
	];

	legendItems.forEach((item) => {
		const itemDiv = document.createElement('div');
		itemDiv.style.display = 'flex';
		itemDiv.style.alignItems = 'center';
		itemDiv.style.margin = '5px 0';

		const iconImg = document.createElement('img');
		iconImg.src = item.icon;
		iconImg.style.width = '16px';
		iconImg.style.height = '20px';
		iconImg.style.marginRight = '8px';

		const labelSpan = document.createElement('span');
		labelSpan.textContent = item.label;

		itemDiv.appendChild(iconImg);
		itemDiv.appendChild(labelSpan);
		legend.appendChild(itemDiv);
	});

	// 将图例添加到地图容器
	document.getElementById('map')?.appendChild(legend);
};
// 创建信息窗口模板
const createInfoWindowContent = (data: any) => {
	if (data.type === '末梢水' && data.list) {
		// 末梢水类型：循环显示 list 中的 name 和 value
		const listItems = data.list.map((item: any) => `<p style="margin:0 0 5px 0;color:#666;">${item.name}：${item.value}</p>`).join('');
		return `
	<div style="width:300px;">
		<h3 style="margin:0 0 10px 0;color:#333;">${data.name}</h3>
		<p style="margin:0 0 5px 0;color:#666;">类型：${data.type}</p>
		${listItems}
	</div>
	`;
	} else if (data.type === '空气质量巡检') {
		return `
	<div style="width:300px;">
		<h3 style="margin:0 0 10px 0;color:#333;">${data.name}</h3>
		<p style="margin:0 0 5px 0;color:#666;">类型：${data.type}</p>
		<table style="width:100%;border-collapse:collapse;margin-top:10px;">
		<thead>
			<tr style="background-color:#f0f0f0;">
			<th style="padding:8px;border:1px solid #ddd;text-align:left;width:40%;">设备名</th>
			<th style="padding:8px;border:1px solid #ddd;text-align:left;width:40%;">接收时间</th>
			<th style="padding:8px;border:1px solid #ddd;text-align:left;width:20%;">结果</th>
			</tr>
		</thead>
		<tbody>
			<tr>
			<td style="padding:8px;border:1px solid #ddd;width:40%;">${tableData.value.deviceName || '无数据'}</td>
			<td style="padding:8px;border:1px solid #ddd;width:40%;">${tableData.value.time || '无数据'}</td>
			<td style="padding:8px;border:1px solid #ddd;width:20%;">${tableData.value.result || '无数据'}</td>
			</tr>
		</tbody>
		</table>
	</div>
	`;
	} else if (data.type === 'CO2') {
		return `
	<div style="width:300px;">
		<h3 style="margin:0 0 10px 0;color:#333;">${data.name}</h3>
		<p style="margin:0 0 5px 0;color:#666;">类型：${data.type}</p>
		<table style="width:100%;border-collapse:collapse;margin-top:10px;">
		<thead>
			<tr style="background-color:#f0f0f0;">
			<th style="padding:8px;border:1px solid #ddd;text-align:left;">设备名</th>
			<th style="padding:8px;border:1px solid #ddd;text-align:left;">接收时间</th>
			<th style="padding:8px;border:1px solid #ddd;text-align:left;">最新数据</th>
			</tr>
		</thead>
		<tbody>
			<tr>
			<td style="padding:8px;border:1px solid #ddd;">${CO2Data.value.deviceNum || '无数据'}</td>
			<td style="padding:8px;border:1px solid #ddd;">${CO2Data.value.time || '无数据'}</td>
			<td style="padding:8px;border:1px solid #ddd;">${CO2Data.value.data || '无数据'}</td>
			</tr>
		</tbody>
		</table>
	</div>
	`;
	}
};
onMounted(async () => {
	try {
		await loadTiandituScript();
	} catch (error) {
		// console.error('初始化天地图失败:', error);
	}
});
</script>

<style scoped lang="scss">
@font-face {
	font-family: Source Han Sans CN-Medium;
	src: url('../../../../src/assets/font/Source Han Sans CN Medium.otf');
}
.el-button.is-loading:before{
	background-color: transparent !important;
}
.mapbox {
	display: flex;
	justify-content: center;
	align-items: flex-end;
}
.centerMap {
	width: 100%;
	height: 100%;
	color: white;
	position: relative;

	.centerMap-border {
		width: 100%;
		position: absolute;
		top: -2.5vh;
		display: flex;
		justify-content: space-between;
	}
}
.Img {
	right: 1vw;
	top: 3vh;
}

.expot {
	:deep(.el-button) {
		@media screen and (max-width: 3840px) {
			font-size: 20px;
		}

		@media screen and (max-width: 3440px) {
			font-size: 16px;
		}

		@media screen and (max-width: 2560px) {
			font-size: 12px;
		}

		@media screen and (max-width: 1960px) {
			font-size: 10px;
		}

		@media screen and (max-width: 1440px) {
			font-size: 8px;
		}

		@media screen and (max-width: 1024px) {
			font-size: 6px;
		}
	}
	@media screen and (max-width: 3840px) {
		font-size: 20px;
	}

	@media screen and (max-width: 3440px) {
		font-size: 16px;
	}

	@media screen and (max-width: 2560px) {
		font-size: 12px;
	}

	@media screen and (max-width: 1960px) {
		font-size: 10px;
	}

	@media screen and (max-width: 1440px) {
		font-size: 8px;
	}

	@media screen and (max-width: 1024px) {
		font-size: 6px;
	}
	position: absolute;
	box-shadow: none;
	background-color: transparent;
	padding: 0 0.5vw;
	width: 5vw;
	height: 1.35vw;
	display: flex;
	align-items: center;
	justify-content: center;
	background-image: url(../../../../src/assets/dashBoard/yearSelect.svg);
	background-repeat: no-repeat;
	background-size: auto 100%;
	cursor: pointer;
	color: #18caca;
}
.yearSelect-btn {
	:deep(.el-input) {
		@media screen and (max-width: 3840px) {
			font-size: 20px;
		}

		@media screen and (max-width: 3440px) {
			font-size: 16px;
		}

		@media screen and (max-width: 2560px) {
			font-size: 12px;
		}

		@media screen and (max-width: 1960px) {
			font-size: 10px;
		}

		@media screen and (max-width: 1440px) {
			font-size: 8px;
		}

		@media screen and (max-width: 1024px) {
			font-size: 6px;
		}

		top: -1px;
	}

	:deep(.el-date-editor) {
		--el-date-editor-width: 4.44vw;
		--el-input-height: 1.2vw;
	}

	:deep(.el-input__wrapper) {
		box-shadow: none;
		background-color: transparent;
		padding: 0 0.5vw;

		background-image: url(../../../../src/assets/dashBoard/yearSelect.svg);
		background-repeat: no-repeat;
		background-size: auto 100%;

		cursor: pointer;
	}

	:deep(.el-input__inner) {
		width: calc(3.2vw);
		height: 1.2vw;
		line-height: 1.2vw;
		-webkit-appearance: none;
		background-color: transparent;

		border: none;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		font-size: 1em !important;
		font-family: Source Han Sans CN, Source Han Sans CN-Medium;
		font-weight: Medium;
		text-align: left;
		color: #18caca;
		display: inline-block;
		font-size: inherit;
		outline: 0;
		padding: 0;
		-webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
		transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

		cursor: pointer;
	}

	:deep(.el-select .el-input.is-focus .el-input__wrapper) {
		box-shadow: none;
	}

	:deep(.el-input__suffix-inner > :first-child) {
		margin-left: 0px;
	}
}
.custom-map-loading.el-loading-mask {
  z-index: 999999999 !important;
  color: #FFF;
}
</style>
