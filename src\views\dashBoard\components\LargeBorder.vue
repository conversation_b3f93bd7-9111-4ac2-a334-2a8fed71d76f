<template>
    <!-- <div></div> -->
    <div class="large-border">
        <div ref="lottieBorderRef" style="width: 100%; height: 100%; pointer-events: none;position: absolute;"></div>
        <div class="sub-box">
            <div class="sub-box-title">
                {{ props.title }}
            </div>
            <div class="sub-box-line"></div>
            <div class="sub-box-slot">
                <slot></slot>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts" name="DashBoardBorder">
import lottie from 'lottie-web';
import animationData from '../../../../src/assets/lottie/largeBorder.json'; // 路径需要根据实际情况修改
import { ref, onMounted } from "vue";

const props = defineProps<{ title: string }>()

const lottieBorderRef = ref()
const initLottie = () => {
    lottie.loadAnimation({
        container: lottieBorderRef.value, // 在模板中添加一个 ref="lottieContainer" 的容器
        animationData: animationData,
        renderer: 'svg', // 选择渲染器，可以是 'svg'、'canvas'、'html'
        loop: true, // 是否循环播放
        autoplay: true, // 是否自动播放
    });
}

onMounted(() => {
    initLottie()
})

</script>

<style scoped lang="scss">
/* miniBorder */
.large-border {
    width: 100%;
    height: 100%;
    box-sizing: content-box;
    position: relative;
}

/* subBoxBorder */
@font-face {
    font-family: Source Han Sans CN-Medium;
    src: url("../../../../src/assets/font/Source Han Sans CN Medium.otf");
}

.sub-box {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 20px 15px 5px 20px;

    .sub-box-title {
        font-size: 12px;
        font-family: Source Han Sans CN, Source Han Sans CN-Medium;
        font-weight: Medium;
        text-align: left;
        color: #18caca;
        line-height: 25px;
        margin-left: 7.34px;
        text-shadow: 0px 0px 9px 0px rgba(86, 254, 254, 0.53);
    }

    .sub-box-line {
        width: 100%;
        height: 4px;
        background-image: url("../../../../src/assets/images/subBox/sub-box-line.png");
        background-size: 100% auto;
    }

    .sub-box-slot {
        width: 100%;
        height: calc(100% - 34px);
        padding-bottom: 17px;
    }

}
</style>