import request from '/@/utils/request';

/**
 *  获取 大屏最右侧 末梢水某个站点近30天的检测数据，时间间隔是5min
 */
export function getWaterData(query: { zbh: string; xmid: string }) {
    return request({
        url: '/detected-data/water-data',
        method: 'get',
        params: query,
    });
}

/**
 *  获取 大屏地图 末梢水统计数据
 */
export function getWaterStatistics() {
    return request({
        url: '/detected-data/water-statistics',
        method: 'get',
    });
}

/**
 *  获取 大屏地图 公共场CO2年度各场所检测数据统计
 */
export function getCO2ByYear(query: { year?: string }) {
    return request({
        url: '/statistical-data/getByYear',
        method: 'get',
        params: query,
    });
}

/**
 *  获取报警次数排行
 */
export function getWarningCount(query:{year:string}) {
    return request({
        url: '/warning/getWarningCount',
        method: 'get',
        params: query,
    });
}

/**
 *  获取总体统计
 */
export function getAllTotal(query:{year:string}) {
    return request({
        url: '/warning/getWarningCount',
        method: 'get',
        params: query,
    });
}