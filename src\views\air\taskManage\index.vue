<template>
    <div class="system-place-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-place-search mb15">
                    <el-input v-model="state.taskNum" :placeholder="$t('message.views.placeholderTaskNumSearch')"
                        style="max-width: 240px;width: 150px;" clearable >
                    </el-input>
                    <el-select v-model="state.deviceName"
                        :placeholder="$t('message.views.placeholderchangeDeviceModel')"  clearable filterable style="max-width: 240px;width: 150px; margin: 0 10px;">
                        <el-option v-for="item in DeviceList" :key="item" :label="item.deviceNum"
                            :value="item.deviceName"></el-option>
                    </el-select>
                    <el-select v-model="state.robotPubilcPlace"
                        :placeholder="$t('message.views.placeholderchangePlaceName')"  clearable filterable style="max-width: 240px;width: 150px;">
                        <el-option v-for="item in PlaceList" :key="item" :label="item.placeName"
                            :value="item._id"></el-option>
                    </el-select>
                    <el-select v-model="state.taskStatus"
                        :placeholder="$t('message.views.placeholderTaskStatus')"  clearable filterable style="max-width: 240px;width: 150px; margin: 0 0 0 10px;">
                        <el-option v-for="item in taskStatus" :key="item" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                    <el-button size="default" type="primary" class="ml10" @click="getTableData()">
                        <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }} </el-button>
                    <el-button size="default" type="success" class="ml10" @click="onOpenAddDevice('add')">
                        <el-icon><ele-FolderAdd /></el-icon>{{ $t('message.views.taskAdd') }} </el-button>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%;height: 57vh;">
                <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
                <el-table-column prop="taskNum" width="130" :label="$t('message.views.taskNum')"
                    show-overflow-tooltip ></el-table-column>
                <el-table-column prop="deviceInfo.deviceNum" show-overflow-tooltip width="130" :label="$t('message.views.deviceNum')"></el-table-column>
                <el-table-column prop="deviceInfo.deviceNickName" :label="$t('message.views.deviceName')"
                    show-overflow-tooltip width="130"></el-table-column>
                <el-table-column prop="deviceInfo.deviceModel" :label="$t('message.views.deviceModel')"
                    show-overflow-tooltip width="100"></el-table-column>
                <el-table-column prop="placeInfo.placeName" :label="$t('message.views.placeName')"
                    show-overflow-tooltip width="100"></el-table-column>
                <el-table-column prop="placeInfo.placeAddress" :label="$t('message.views.placeAddress')"
                    show-overflow-tooltip width="130"></el-table-column>
                <el-table-column prop="startTime" :label="$t('message.views.startTime')"
                show-overflow-tooltip width="100"></el-table-column>
                <el-table-column :label="$t('message.views.intervalTime')"
                show-overflow-tooltip width="100">
                    <template #default="{row}">
                        <div v-if="row.intervalTime">{{ row.intervalTime+'小时' }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="samplePointList" :label="$t('message.views.samplePointList')"
                show-overflow-tooltip width="130"></el-table-column>
                <el-table-column prop="taskStatus" :label="$t('message.views.taskStatus')"  width="80" fixed="right">
                    <template #default="{row}">
                        <el-tag v-if="row.taskStatus === 'PENDING'" type="info">待开始</el-tag>
                        <el-tag v-else-if="row.taskStatus === 'IN_PROGRESS'" type="warning">进行中</el-tag>
                        <el-tag v-else-if="row.taskStatus === 'COMPLETED'" type="success">已完成</el-tag>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('message.common.operation')" width="120" fixed="right">
                    <template #default="scope">
                        <el-button size="small" text type="primary" @click="onUpdateTask( $t('message.common.PENDING'),scope.row,'IN_PROGRESS')" v-if="scope.row.taskStatus === 'PENDING'">{{
                            $t('message.common.startTask') }}</el-button>
                        <el-button size="small" text type="primary" @click="onUpdateTask($t('message.common.IN_PROGRESS'),scope.row,'COMPLETED')" v-if="scope.row.taskStatus === 'IN_PROGRESS'">{{
                    $t('message.common.overTask') }}</el-button>
                        <el-button size="small" text type="danger" @click="onRowDel(scope.row)" v-if="scope.row.taskStatus === 'PENDING'">{{
                            $t('message.common.delete') }}</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <PlaceDialog ref="PlaceDialogRef" :DeviceList="DeviceList" :PlaceList="PlaceList" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="taskManage">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getTaskList, deleteTask,updateTaskInfo } from "/@/api/task/index";
import { getRobotDeviceList} from "/@/api/robotDevice/index";
import { getRobotPlaceList } from '/@/api/publicPlace/index'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 引入组件
const PlaceDialog = defineAsyncComponent(() => import('./dialog.vue'));
// 组件ref
const PlaceDialogRef = ref();
// 定义变量内容
const state = reactive<taskState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 查询输入
    taskNum: '',
    taskStatus: '',
    robotPubilcPlace: '',
    deviceName: '',
});
// 设备列表
const DeviceList=reactive<rowRobotDeviceType[]>([])

// 场所列表
const PlaceList=reactive<RobotplaceDataFome[]>([])

// 任务状态
const taskStatus=reactive([
    {
        label:'待开始',
        value:'PENDING'
    },
    {
        label:'进行中',
        value:'IN_PROGRESS'
    },
    {
        label:'已完成',
        value:'COMPLETED'
    }
])
// 获取表格数据
const getTableData = async (taskNum = state.taskNum,taskStatus = state.taskStatus,robotPubilcPlace = state.robotPubilcPlace,deviceName = state.deviceName) => {
    state.tableData.loading = true;
    const res = await getTaskList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        taskNum,
        taskStatus,
        robotPubilcPlace,
        deviceName,
    })
    // console.log(res,'res');
    
    state.tableData.data = res.data.list;
    state.tableData.total = res.data.total;
    state.tableData.loading = false;
};
// 打开新增场所弹窗
const onOpenAddDevice = (type: string) => {
    PlaceDialogRef.value.openDialog(type);
};
// 开始任务
const onUpdateTask = (title:string, row: RowTaskType,status:string) => {
    ElMessageBox.confirm(
    `${title}`,
     t('message.common.prompt'),
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
        await updateTaskInfo({
            _id: row._id,
            deviceName:row.deviceName,
            taskStatus:status,
        });
        ElMessage.success( t('message.common.taskStatusChange'));
        getTableData();
    })
}
// 删除场所
const onRowDel = (row: RowTaskType) => {
    ElMessageBox.confirm(t('message.views.deleteTaskConfirm', { taskNum: row.taskNum }), t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deleteTask(row._id)
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};
// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
const RobotDeviceList=async ()=>{
    const res=await getRobotDeviceList({
        page: 1,
        pageSize: 99999,
    })
    Object.assign(DeviceList,res.data.list)
}
const RobotPlaceList=async ()=>{
    const res=await getRobotPlaceList({
        page: 1,
        pageSize: 99999,
    })
    Object.assign(PlaceList,res.data.list)
}
// DOM挂载完
onMounted(() => {
    getTableData();
    RobotDeviceList()
    RobotPlaceList()
});
</script>

<style scoped lang="scss">
.system-place-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-place-search {
    display: flex;
    align-content: space-around;
}
</style>
