// 定义内容
export default {
	common: {
		saveTask:'保存任务',
		more: '更多',
		empty: '暂无',
		noMessage: '暂无消息',
		all: '全部',
		startTask:'开始',
		overTask:'完成',
		PENDING:'确定修改此任务状态为进行中吗？',
		IN_PROGRESS:'确定修改此任务状态为已完成吗？',
		taskStatusChange:'任务更新成功！',
		index: '序号',
		name: '姓名',
		unit: '单位',
		dataUnit: '数据单位',
		threshold: '阈值',
		phoneNum: '手机号 | 联系电话',
		password: '密码',
		user: '用户 | 普通用户',
		admin: '管理员',
		createdAt: '创建时间',
		updatedAt: '修改时间',
		username: '用户名 | 登录账号',
		WarningType:'预警类别',
		isAble: '是否启用',
		able: '启',
		unable: '禁',

		status: '状态',
		enable: '启用 | 启',
		disable: '禁用 | 禁',
		operation:'操作',
		status1: '待机',
		status2: '运动中',
		status3: '错误',
		status4: '断开连接',
		status5: '关机',
		add: '新增 | 添加',
		create: '创建',
		delete: '删除',
		update: '修改 | 更新',
		modify: '修改',
		search: '查询',
		cancel: '取消',
		confirm: '确认',
		prompt: '提示',
		lift: '解除',
		export: '导出',
		edit: '编辑',

		placeholderName: '请输入姓名',
		placeholderPhoneNum: '请输入手机号',
		placeholderPassword: '请输入密码',
		placeholderDate: '请选择日期|请选择查询日期',
		placeholderSearchKeyWord: '请输入查询关键字',

		addSuccess: '添加成功',
		modifySuccess: '修改成功',
	},
	views: {
		selectSamplePoint:'选择采样点',
		samplePointName:'采样点名称',
		serialNumber:'序号',
		addSamplePoint:'添加采样点',
		startTime:'启动时间',
		intervalTime:'间隔时间',
		samplePointList:'采样点序列',
		sample_point:'采样点',
		MetricsRange:'指标范围',
		MeasurementData:'采样数据',
		Result:'采样结果',
		Metrics:'指标',
		MetricsInfo:'指标结果详情',
		deleteTaskConfirm:'此操作将永久删除任务({taskNum})，是否继续？',
		taskAdd:'新增巡检任务',
		taskNum:'任务编号',
		taskStatus:'任务状态',
		placeholderTaskStatus:'请选择任务状态',
		// 设备管理 device 
		// index
		deviceAdd: '新增设备',
		deviceName: '设备名称',
		deviceNum: '设备编号',
		deviceType: '设备类型',
		// deviceUnit: '单位',
		// deviceThreshold: '阈值',
		devicePlace: '安装场所',
		devicePosition: '安装点位',
		deviceStatus: '设备状态',
		sensorData:'指标列表',
		deviceModel:'设备型号',
		deviceAdminName:'设备管理员名字',
		deviceAdminPhone:'设备管理员电话',
		placeholderDeviceKeyword: '请输入设备编码/名称/类型',
		placeholderRobotDeviceKeyword: '请输入设备编码/名称',
		placeholderDevicePlcae: '请选择安装场所',
		placeholderRobotDevice: '请选择设备',
		deleteDeviceConfirm: '此操作将永久删除设备({name})及其检测数据，是否继续？',
		deleteRobotDeviceConfirm: '此操作将永久删除设备({deviceNickName})及其检测数据，是否继续？',
		// dialog
		deviceDialogUpdateTitle: '修改设备',
		deviceDialogAddTitle: '新增设备',
		placeholderDeviceName: '请输入设备名称',
		placeholderStartTime: '请选择启动时间',
		placeholderiIntervalTime: '请选择间隔时间',
		placeholderiSamplePointList: '请添加采样点序列',
		placeholderSamplePointName: '请选择采样点名称',
		placeholderDeviceNum: '请选择设备编号',
		placeholderdeviceNickName: '请选择deviceName',
		placeholderDeviceType: '选择设备编码后自动填充类型',
		placeholderDeviceThreshold: '请输入设备阈值',
		placeholderDevicePlace: '请选择安装场所',
		placeholderDevicePosition: '请选择安装点位',
		placeholderSensorData: '请选择指标列表',
		placeholderDeviceModel: '请输入设备型号',
		placeholderchangeDeviceModel: '请选择设备型号',
		placeholderchangeDeviceNum: '请选择设备编号',
		placeholderchangeDeviceStatus: '请选择设备状态',
		placeholderDeviceAdminName: '请输入设备管理员名字',
		placeholderDeviceAdminPhone: '请输入设备管理员电话',
		placeholderResult:'请选择采样结果',
		placeholderTaskNumSearch:'请输入任务编号',
		placeholderWarningType:'请选择预警类别',
		// 用户管理 sys-user
		// index
		userAdd: '新增用户',
		userName: '用户名',
		userRole: '用户角色',
		userStatus: '用户状态',
		placeholderUserKeyword: '请输入用户名/姓名/手机号',
		deleteUserConfirm: '此操作将永久删除用户（{userName}），是否继续？',
		// dialog
		userDialogUpdateTitle: '修改用户',
		userDialogAddTitle: '新增用户',
		placeholderUserName: '请输入用户名',
		placeholderUserRole: '请选择用户角色',
		rulesUserPasswordLength: '密码的长度应在6到16之间',
		rulesUserPhoneNumReg: '手机号码格式不正确',

		// 空气监测air
		// latest
		CO2ConcentrationDetection: '二氧化碳浓度检测',
		timeSpan: '时间跨度',
		month: '月',
		day: '日',
		hour: '时',
		minute: '分 | {n}分',
		startingAndEndingTime: '起止时间',
		startingTime: '起始时间',
		endingTime: '截止时间',
		lastMonth: '最近一月',
		lastWeek: '最近一周',
		lastDay: '最近一日',
		lastHour: '最近一小时',
		// history
		receiveTime: '接收时间',
		historyData: '数据(ppm)|百分比(%)',
		sendmsg:'发送短信',
		// place - index 
		
		placeAdd: '新增场所',
		placeModify: '修改场所',
		placeholderPlaceNameSearch: '请输入场所名称进行查询',
		placeName: '场所名称',
		placeType: '场所类别',
		placeArea: '场所区域',
		placeAddress: '场所地址',
		placeHead: '主要负责人',
		placeEngineeringHead: '工程部负责人',
		deletePlcaeConfirm: '此操作将永久删除场所（{placeName}），是否继续？',
		InchargeName:'场所负责人名字',
		InchargePhone:'场所负责人电话',
		SupervisionName:'场所监督员名字',
		SupervisionPhone:'场所监督员电话',

		// place - dialog
		placeholderPlaceName: '请输入场所名称',
		placeholderchangePlaceName: '请选择场所',
		placeholderPlaceType: '请选择场所类型',
		placeholderPlaceAddress: '请输入场所地址',
		placeholderInchargeName: '请输入场所负责人名字',
		placeholderInchargePhone: '请输入场所负责人电话',
		placeholderSupervisionName: '请输入场所监督员名字',
		placeholderSupervisionPhone: '请输入场所监督员电话',
		rulesPlaceArea: '请选择场所所在地区',
		rulesPlaceAddress: '请选择场所详细地址',
		rulesPlaceHeadName: '请填写主要负责人姓名',
		rulesPlaceHeadPhoneNum: '请填写主要负责人联系电话',
		rulesPlaceEngineeringHeadName: '请填写工程部负责人姓名',
		rulesPlaceEngineeringHeadPhoneNum: '请填写工程部负责人联系电话',
		rulesPlacePhoneNum: '电话号码格式不正确',

		// elaryWarning
		messageContent: '消息内容',
		elaryWarningStatus: '处置状态',
		pending: '待处理',
		lifted: '已解除',
		placeholderWarningDeviceNum: '请选择设备编号',
		placeholderWarningStatus: '请选择处置状态',
		deleteWarningConfirm: '此操作将永久删除预警（{createdAt}），是否继续？',
		liftWarningConfirm: '确定要解除该设备当前（{createdAt}）及之前的所有预警吗？',
		robotliftWarningConfirm: '确定要解除该设备当前（{createdAt}）的预警吗？',
		// water
		waterSname: '站点名称',
		turbidity: '浊度(NTU)|浊度',
		conductivity: '电导率(μS/cm)|电导率',
		waterTemperature: '水温(℃)|水温',
		totalChlorine: '总氯(mg/L)|总氯',
		placeholderWaterSite: '请选择站点',

		// xdj
		store: '门店',
		headStore: '总店',
		storeName: '门店名称',
		storeType: '门店类型',
		creator: '创建人',

		// personal
		personalInfo: '个人信息',
		updateInfo: '更新信息',
		basiclInfo: '基本信息',

		// privateMessage 站内消息 
		privateMessageTitle: '消息标题',
		privateMessageContent: '消息内容',
		privateMessageStatus: '消息状态',
		privateMessageMarkRead: '标记已读',
		unread: '未读',
		read: '已读',
		placeholderMessageStatus: '请选择消息状态',
		markReadConfirm: '确定要将该消息（{createdAt}）标记为已读吗？',

		// warningTextMessage 预警短信
		warningMessageContent: '短信内容',
		warningMessageSendTime: '短信发送时间',
	},

	router: {
		placeHead: '场所负责人',
		latest_CO2:'CO2',
		latest_Robot:'空气质量巡检',
		home: '首页',
		system: '系统设置',
		systemMenu: '菜单管理',
		systemRole: '角色管理',
		systemDept: '部门管理',
		systemDic: '字典管理',
		limits: '权限管理',
		limitsFrontEnd: '前端控制',
		limitsFrontEndPage: '页面权限',
		limitsFrontEndBtn: '按钮权限',
		limitsBackEnd: '后端控制',
		limitsBackEndEndPage: '页面权限',
		taskManage:'设备巡检任务管理',
		makeTableDemo: '表格封装演示',
		chartIndex: '大数据图表',
		// 
		history: '历史数据',
		latest: '最新数据',

		systemUser: '用户管理',
		deviceMana: '设备管理',

		air: '公共场所空气监测',
		placeManage: '场所管理',
		elaryWarning: '预警处置',

		water: '末梢水水质监测',

		cleaningRoomMonitor: '住宿场所清洗消毒间监控',
		monitorList:'监控列表',

		disinfectionProductAudit: '消毒产品销售监管审核',
		xdjUser: '用户管理',
		xdjStore: '门店管理',
		xdjDetection: '消毒剂检测',

		personal: '个人中心',

		elaryWarningMsg: '预警信息',

		privateMessage: '站内消息',
		warningTextMessage: '预警短信',

		developing: '开发中',

		dashBoard: '驾驶舱'
	},
	staticRoutes: {
		signIn: '登录',
		notFound: '找不到此页面',
		noPower: '没有权限',
	},
	user: {
		title0: '组件大小',
		title1: '语言切换',
		title2: '菜单搜索',
		title3: '布局配置',
		title4: '消息',
		title5: '开全屏',
		title6: '关全屏',
		dropdownLarge: '大型',
		dropdownDefault: '默认',
		dropdownSmall: '小型',
		dropdown1: '首页',
		dropdown2: '个人中心',
		dropdown3: '404',
		dropdown4: '401',
		dropdown5: '退出登录',
		dropdown6: '代码仓库',
		searchPlaceholder: '菜单搜索：支持中文、路由路径',
		newTitle: '通知',
		newBtn: '全部已读',
		newGo: '前往通知中心',
		newDesc: '暂无通知',
		logOutTitle: '提示',
		logOutMessage: '此操作将退出登录, 是否继续?',
		logOutConfirm: '确定',
		logOutCancel: '取消',
		logOutExit: '退出中',

		dashBoard: '数据大屏',
	},
	tagsView: {
		refresh: '刷新',
		close: '关闭',
		closeOther: '关闭其它',
		closeAll: '全部关闭',
		fullscreen: '当前页全屏',
		closeFullscreen: '关闭全屏',
	},
	notFound: {
		foundTitle: '地址输入错误，请重新输入地址~',
		foundMsg: '您可以先检查网址，然后重新输入或给我们反馈问题。',
		foundBtn: '返回首页',
	},
	noAccess: {
		accessTitle: '您未被授权，没有操作权限~',
		accessMsg: '请联系管理员',
		accessBtn: '重新授权',
	},
	layout: {
		configTitle: '布局配置',
		oneTitle: '全局主题',
		twoTopTitle: '顶栏设置',
		twoMenuTitle: '菜单设置',
		twoColumnsTitle: '分栏设置',
		twoTopBar: '顶栏背景',
		twoTopBarColor: '顶栏默认字体颜色',
		twoIsTopBarColorGradual: '顶栏背景渐变',
		twoMenuBar: '菜单背景',
		twoMenuBarColor: '菜单默认字体颜色',
		twoMenuBarActiveColor: '菜单高亮背景色',
		twoIsMenuBarColorGradual: '菜单背景渐变',
		twoColumnsMenuBar: '分栏菜单背景',
		twoColumnsMenuBarColor: '分栏菜单默认字体颜色',
		twoIsColumnsMenuBarColorGradual: '分栏菜单背景渐变',
		twoIsColumnsMenuHoverPreload: '分栏菜单鼠标悬停预加载',
		threeTitle: '界面设置',
		threeIsCollapse: '菜单水平折叠',
		threeIsUniqueOpened: '菜单手风琴',
		threeIsFixedHeader: '固定 Header',
		threeIsClassicSplitMenu: '经典布局分割菜单',
		threeIsLockScreen: '开启锁屏',
		threeLockScreenTime: '自动锁屏(s/秒)',
		fourTitle: '界面显示',
		fourIsShowLogo: '侧边栏 Logo',
		fourIsBreadcrumb: '开启 Breadcrumb',
		fourIsBreadcrumbIcon: '开启 Breadcrumb 图标',
		fourIsTagsview: '开启 Tagsview',
		fourIsTagsviewIcon: '开启 Tagsview 图标',
		fourIsCacheTagsView: '开启 TagsView 缓存',
		fourIsSortableTagsView: '开启 TagsView 拖拽',
		fourIsShareTagsView: '开启 TagsView 共用',
		fourIsFooter: '开启 Footer',
		fourIsGrayscale: '灰色模式',
		fourIsInvert: '色弱模式',
		fourIsDark: '深色模式',
		fourIsWartermark: '开启水印',
		fourWartermarkText: '水印文案',
		fiveTitle: '其它设置',
		fiveTagsStyle: 'Tagsview 风格',
		fiveAnimation: '主页面切换动画',
		fiveColumnsAsideStyle: '分栏高亮风格',
		fiveColumnsAsideLayout: '分栏布局风格',
		sixTitle: '布局切换',
		sixDefaults: '默认',
		sixClassic: '经典',
		sixTransverse: '横向',
		sixColumns: '分栏',
		tipText: '点击下方按钮，复制布局配置去 `src/stores/themeConfig.ts` 中修改。',
		copyText: '一键复制配置',
		resetText: '一键恢复默认',
		copyTextSuccess: '复制成功！',
		copyTextError: '复制失败！',
	}
};
