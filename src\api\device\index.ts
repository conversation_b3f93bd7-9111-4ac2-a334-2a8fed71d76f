import request from '/@/utils/request';

// 获取设备列表
export function getDeviceList(query: API.DeviceListParams) {
    return request({
        url: '/device',
        method: 'get',
        params: query,
    });
}

// 根据id获取某个设备信息
export function getDeviceInfo(query: { _id: string }) {
    return request({
        url: '/device/' + query._id.toString(),
        method: 'get',
    });
}

// 添加设备 
export function createDevice(data: API.CreateDeviceParams) {
    return request({
        url: '/device',
        method: 'post',
        data,
    });
}

// 更新设备信息
export function updateDevice(data: API.UpdateDeviceParams) {
    return request({
        url: '/device',
        method: 'put',
        data,
    });
}

// 删除设备
export function deleteDevice(id: string) {
    return request({
        url: '/device/' + id.toString(),
        method: 'delete',
    });
}

// 获取所有的设备类型
export function getDeviceType() {
    return request({
        url: '/device/types',
        method: 'get',
    });
}
/**
 * @description 设备信息 - 获取所有设备
 * @returns 
 */
export function getDeviceListAll() {
    return request({
        url: '/device/all',
        method: 'get',
    });
}