<template>
    <div class="system-user-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <h1 class="msg">{{ msg }}</h1>
        </el-card>
    </div>
</template>

<script setup lang="ts" name="developing">
import { ref } from 'vue'
const msg = ref('开发中，敬请期待')

</script>

<style lang="scss" scoped>
.system-user-container {
    :deep(.el-card__body) {
        height: 100%;
        display: flex;
        flex: 1;
        overflow: auto;
        align-items: center;
        justify-content: center;
    }
}

.msg {
    font-size: 30px;
}
</style>