<template>
	<div class="system-place-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="800px">
			<el-form ref="placeDialogFormRef" :rules="rules" :model="state.dataFome" v-loading="state.loading"
				destroy-on-close label-width="auto">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="placeName" :label="$t('message.views.placeName')">
							<el-input v-model="state.dataFome.placeName"
								:placeholder="$t('message.views.placeholderPlaceName')" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="placeAddress" :label="$t('message.views.placeAddress')">
							<el-input v-model="state.dataFome.placeAddress"
								:placeholder="$t('message.views.placeholderPlaceAddress')" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="InchargeName" :label="$t('message.views.InchargeName')">
							<el-input v-model="state.dataFome.InchargeName"
								:placeholder="$t('message.views.placeholderInchargeName')" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="InchargePhone" :label="$t('message.views.InchargePhone')">
							<el-input v-model="state.dataFome.InchargePhone"
								:placeholder="$t('message.views.placeholderInchargePhone')" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="SupervisionName" :label="$t('message.views.SupervisionName')">
							<el-input v-model="state.dataFome.SupervisionName"
								:placeholder="$t('message.views.placeholderSupervisionName')" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="SupervisionPhone" :label="$t('message.views.SupervisionPhone')">
							<el-input v-model="state.dataFome.SupervisionPhone"
								:placeholder="$t('message.views.placeholderSupervisionPhone')" clearable></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item  prop="sensorData">
							<el-table :data="sensorData" style="width: 100%" @selection-change="handleSelectionChange">
								<el-table-column type="selection"  width="55">
									<template #header>
										<el-checkbox v-model="allchoose" />
									</template>
									<template #default="{$index}">
										<el-checkbox v-model="state.dataFome.sensorData[$index].choose"   @change="handleCheckboxChange($index)"/>
									</template>
								</el-table-column>	
								<el-table-column prop="sensorName" label="传感器指标" width="100" >

								</el-table-column>
								<!-- <el-table-column prop="unit" label="单位" width="70" /> -->
								<el-table-column prop="sensorPlaceAndRange.place" label="场所类别" >
									<template #default="{row,$index}">
										<el-select :disabled="!state.dataFome.sensorData[$index].choose" v-model="state.dataFome.sensorData[$index].sensorPlaceAndRange[0].place" placeholder="请选择" @change="changePlace(row.sensorPlaceAndRange,$index,state.dataFome.sensorData[$index].sensorPlaceAndRange[0].place)" style="width: 100%;">
											<el-option :label="item.place" :value="item.place" :key="item.place" v-for="item in row.sensorPlaceAndRange"></el-option>
										</el-select>
									</template>
								</el-table-column>
								<el-table-column prop="sensorPlaceAndRange" label="限值" width="150">
									<template #default="{row,$index}">
										<div>
											{{state.dataFome.sensorData[$index].choose? state.dataFome.sensorData[$index].sensorPlaceAndRange[0].place!=''?
												formatRange(
													state.dataFome.sensorData[$index].sensorPlaceAndRange[0].Range
												,row.unit) :'请选择场所' :'请先勾选指标'
											}}
										</div>
										<!-- <div style="display: flex;">
											<el-select disabled v-model="state.dataFome.sensorData[$index].sensorPlaceAndRange[0].Range.maxValueType" 
											placeholder="选择区间" style="width: 70px">
												<el-option
													v-for="item in MaxrangeOptions"
													:key="item.value"
													:label="item.label"
													:value="item.value"
												/>
											</el-select>
											<el-input disabled v-model="state.dataFome.sensorData[$index].sensorPlaceAndRange[0].Range.minValue" style="width: 100px;margin: 0 10px; " placeholder="输入最小值" />
											
											<el-select disabled v-model="state.dataFome.sensorData[$index].sensorPlaceAndRange[0].Range.minValueType" placeholder="选择区间" style="width: 70px">
												<el-option
													v-for="item in MinrangeOptions"
													:key="item.value"
													:label="item.label"
													:value="item.value"
												/>
											</el-select>
											<el-input disabled v-model="state.dataFome.sensorData[$index].sensorPlaceAndRange[0].Range.maxValue"  placeholder="输入最大值" style="width: 100px;  margin-left: 10px;"/>
										</div> -->
									</template>
								</el-table-column>
							</el-table>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(placeDialogFormRef)">
						{{ state.dialog.submitTxt }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="placeDialog">
import { reactive, ref, nextTick, onMounted, computed } from 'vue';
import { addRobotPlace, updateRobotPlaceInfo, getPlaceTypeList, getRobotPlaceDetail,getSensorData } from '/@/api/publicPlace/index'
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 组件ref
// const useAreaSelectorRef = ref();
const placeDialogFormRef = ref();

// 校验规则
const rules = reactive({
	placeName: [{ required: true, message: computed(() => t('message.views.placeholderPlaceName')), trigger: 'blur' }],
	placeAddress: [{ required: true, message: computed(() => t('message.views.placeholderPlaceAddress')), trigger: 'blur' }],
	InchargeName: [{ required: true, message: computed(() => t('message.views.placeholderInchargeName')), trigger: 'blur' }],
	InchargePhone: [{ required: true, message: computed(() => t('message.views.placeholderInchargePhone')), trigger: 'blur' }, { pattern: /^1[3-9]\d{9}$/, message: computed(() => t('message.views.rulesPlacePhoneNum')), trigger: 'blur' }],
	SupervisionName: [{ required: true, message: computed(() => t('message.views.placeholderSupervisionName')), trigger: 'blur' }],
	SupervisionPhone: [{ required: true, message: computed(() => t('message.views.placeholderSupervisionPhone')), trigger: 'blur' }, { pattern: /^1[3-9]\d{9}$/, message: computed(() => t('message.views.rulesPlacePhoneNum')), trigger: 'blur' }],
})
// 定义变量内容
const useState = () => reactive<RobotplaceDialogState>({
	dataFome: {
		_id: '',
		placeName: '',
		placeAddress: '',
		InchargeName: '',
		InchargePhone: '',
		SupervisionName: '',
		SupervisionPhone: '',
		sensorData: [
    {
        sensorName: '温度',
        unit: '℃',
		EnglishName: 'temperature',
        choose: false,
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: '相对湿度',
        unit: '%',
		EnglishName: 'humidity',
        choose: false,
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: '风速',
        unit: 'm/s',
		EnglishName: 'windspeed',
        choose: false,
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: '噪声',
        unit: 'dB(A)',
        choose: false,
		EnglishName: 'noise',
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: '二氧化碳',
        unit: '%',
		EnglishName: 'co2',
        choose: false,
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: '甲醛',
        unit: 'mg/m3',
		EnglishName: 'ch2o',
        choose: false,
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: 'PM10',
        unit: 'mg/m3',
        choose: false,
		EnglishName: 'PM10',
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: 'PM2.5',
        unit: 'μg/m3',
		EnglishName: 'PM2_5',
        choose: false,
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: '一氧化碳',
        unit: 'mg/m3',
        choose: false,
		EnglishName: 'co',
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: 'TVOC',
        unit: 'mg/m3',
		EnglishName: 'TVOC',
        choose: false,
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    },
    {
        sensorName: '大气压',
        unit: 'kPa',
		EnglishName: 'pressure',
        choose: false,
        sensorPlaceAndRange: [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ]
    }
]
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
	loading: false,
	// 负责人关联
	headRelated: true,
});

const formatRange = (range: {
  minValue: string | number;
  minValueType: string | number;
  maxValue: string | number;
  maxValueType: string | number;
},unit='') => {
  const { minValue, minValueType, maxValue, maxValueType } = range;

  const isMinEmpty = minValue === '' || minValue === '无限值';
  const isMaxEmpty = maxValue === '' || maxValue === '无限值';

  // 最小值文本
  const minText = isMinEmpty
    ? ''
    : maxValueType === 2
      ? `≥${minValue}${unit}`
      : `>${minValue}${unit}`;

  // 最大值文本
  const maxText = isMaxEmpty
    ? ''
    : minValueType === 2
      ? `≤${maxValue}${unit}`
      : `<${maxValue}${unit}`;

  // 组合逻辑
  if (isMinEmpty && isMaxEmpty) {
    return '无限值';
  } else if (isMinEmpty) {
    return maxText;
  } else if (isMaxEmpty) {
    return minText;
  } else {
    // 若两端都是“闭区间”才用 ~ 连接
    if (minValueType === 2 && maxValueType === 2) {
      return `${minValue}${unit}~${maxValue}${unit}`;
    } else {
      return `${minText}${unit} ~ ${maxText}${unit}`;
    }
  }
};



const handleCheckboxChange = (index: number) => {
	if(!state.dataFome.sensorData[index].choose){
		state.dataFome.sensorData[index].sensorPlaceAndRange = [
            { place: '', Range: { minValue: '', minValueType: '', maxValue: '', maxValueType: '' } }
        ];
	}
};
const handleSelectionChange = () => {
	allchoose.value=!allchoose.value
	state.dataFome.sensorData.forEach((item:any) => {
		item.choose=allchoose.value
	})
}
// 选择场所类别自动填充限制范围
const changePlace=(row:any,index:number,selectrow:any)=>{
	// console.log(row,index);
	// console.log(selectrow);
	row.forEach((item:any)=>{
		if(item.place === selectrow){
			state.dataFome.sensorData[index].sensorPlaceAndRange[0].Range = item.Range
		}
	})
}
const state = useState()
// 场所类型列表
const placeTypeList = reactive([])

const allchoose = ref(false);

const sensorData=ref([])
// 获取场所类型列表
const getPlaceTypeListData = async () => {
	const res = await getPlaceTypeList()
	Object.assign(placeTypeList, res.data)
}
// 页面DOM挂载完后
onMounted(async () => {
	getPlaceTypeListData()
	getSensorDatas()
})

const getSensorDatas = async () => { 
	const res= await getSensorData()
	// console.log(res);
	if(res.code===0){
		sensorData.value=res.data
	}
}

// 打开弹窗
const openDialog = async (type: string, row: rowPlaceType) => {
	state.dialog.isShowDialog = true;
	state.loading = true
	// 弹窗数据初始化
	if (type === 'update') {

		state.dialog.type = type
		state.dialog.title = t('message.views.placeModify');
		state.dialog.submitTxt = t('message.common.modify');
		// 获取详情
		const res = await getRobotPlaceDetail({ _id: row._id })

		// 填入数据
		const { _id, placeName, placeAddress, InchargeName, InchargePhone,SupervisionName, SupervisionPhone,sensorData} = res.data
		const data = {
			_id, placeName, placeAddress, InchargeName, InchargePhone,SupervisionName,SupervisionPhone,sensorData
		}

		state.dataFome = data

	} else {
		state.dialog.type = 'add';
		state.dialog.title = t('message.views.placeAdd');
		state.dialog.submitTxt = t('message.common.add');

		Object.assign(state.dataFome, useState().dataFome)

	}
	state.loading = false
};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		placeDialogFormRef.value.resetFields();
	});
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
	formEl.validate(async (valid) => {
		if (!valid) {
			return
		}
		const hasChosen = state.dataFome.sensorData.some((sensor:any) => sensor.choose);
		if (!hasChosen) {
			ElMessage.warning('请至少勾选一个指标');
			return; // 阻止提交
		}
		// 检查已勾选指标的 place 是否为空
		const hasEmptyPlace = state.dataFome.sensorData.some((sensor: any) => {
			return sensor.choose && sensor.sensorPlaceAndRange.some((item: any) => item.place === '');
		});

		if (hasEmptyPlace) {
			ElMessage.warning('请为指标选择场所');
			return; // 阻止提交
		}

		try {
			if (state.dialog.type === 'add') {
				await addRobotPlace(state.dataFome)
				ElMessage.success(t('message.common.addSuccess'))
			}
			else {
				// 如果是修改，参数需要包含_id
				await updateRobotPlaceInfo(Object.assign(state.dataFome, { _id: state.dataFome._id }))
				ElMessage.success(t('message.common.modifySuccess'))
			}
		} catch (error) {
			// ElMessage.error('操作失败！请检查输入')
			return
		}
		closeDialog();
		emit('refresh');
	})
};

// 点击负责人输入关联icon
// const onClickHeadIcon = () => {
// 	state.headRelated = !state.headRelated
// }

// 负责人关联输入
// watch(
// 	() => [
// 		state.dataFome.head.name, state.dataFome.head.phoneNum,
// 		state.dataFome.engineeringHead.name, state.dataFome.engineeringHead.phoneNum,
// 	],
// 	(newVal, oldVal) => {
// 		if (state.headRelated) {
// 			if (newVal[0] != oldVal[0]) {
// 				state.dataFome.engineeringHead.name = state.dataFome.head.name
// 			} else if (newVal[1] != oldVal[1]) {
// 				state.dataFome.engineeringHead.phoneNum = state.dataFome.head.phoneNum
// 			} else if (newVal[2] != oldVal[2]) {
// 				state.dataFome.head.name = state.dataFome.engineeringHead.name
// 			} else {
// 				state.dataFome.head.phoneNum = state.dataFome.engineeringHead.phoneNum
// 			}
// 		}
// 	}
// )

// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style lang="scss">
.inputIcon {
	color: #aaa;

	&:hover {
		color: var(--el-color-primary);
		cursor: pointer;
	}
}
</style>
