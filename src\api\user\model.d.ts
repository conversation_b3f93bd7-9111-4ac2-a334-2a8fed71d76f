declare namespace API {

    type UserListParams = {
        page?: number;
        pageSize?: number;
        keyWord?: string;
        active?: number;
        roles?: string[];
        enterpriseId?: string;
    }


    /**
     *  role:
     *  superAdmin          超级管理员
     *  admin               卫健委监督员
     *  headStoreManager    总店管理员
     *  storeManager        门店管理员
     *  placeHead           场所负责人
     *  engineeringHead     工程负责人
     */
    type CreateUserParams = {
        userName: string;
        password?: string;
        name: string;
        phoneNum?: string;
        role?: userRole;
        avatar?: string;
        active?: 0 | 1
    }

    type UpdateUserParams = {
        _id: string;
        password?: string;
        name?: string;
        phoneNum?: string;
        enum?: string;
        role?: userRole;
        active?: number;
        userName?: string;
    }
}