declare namespace API {
    type receivedTaskListItem = {
        _id: string;
        deviceName: string;
        deviceInfo: any;
        placeInfo: any;
        robotPubilcPlace: string;
        taskNum?: string;
        taskStatus: string;
        createdAt: string;
        updatedAt: string;
        [k: string]: any;
    }

    interface getTaskListResult {
        total: number;
        list: receivedTaskListItem[]
    }

    declare type TaskListParams = {
        page?: number;
        pageSize?: number;
        taskNum?: string;
        deviceName?:string;
        robotPubilcPlace?:string;
        taskStatus?:string;
    }


    interface AddTaskParams {
        deviceName: string;
        robotPubilcPlace: string;
        taskStatus?: string;
        taskNum?: string;
        taskStatus?: string;
        startTime:string;
        intervalTime:string;
        samplePointList:string;
        // head: personShortInfo;
        // engineeringHead: personShortInfo;
        [property: string]: any;
    }

    interface updateTaskParams {
        _id: string;
        taskStatus:string;
        deviceName:string;
    }
}