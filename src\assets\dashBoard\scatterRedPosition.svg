<?xml version="1.0" encoding="UTF-8"?>
<svg width="39.5786206px" height="40.6060413px" viewBox="0 0 39.5786206 40.6060413" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>首页_地图_定位_红备份 3</title>
    <defs>
        <linearGradient x1="52.9411804%" y1="45.5743939%" x2="50.000004%" y2="54.8470917%" id="linearGradient-1">
            <stop stop-color="#F73D48" offset="0%"></stop>
            <stop stop-color="#D51A1A" stop-opacity="0.0700000003" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="52.2256649%" y1="4.34782588%" x2="50.0000031%" y2="99.9999936%" id="linearGradient-2">
            <stop stop-color="#FF7777" offset="0%"></stop>
            <stop stop-color="#FF153F" offset="100%"></stop>
        </linearGradient>
        <path d="M10.290099,1.13960119e-11 C15.7411494,1.13960119e-11 20.1600985,4.04665135 20.1600985,9.03844914 C20.1600985,12.3663143 16.8700987,16.9175901 10.290099,22.6922766 C3.71009928,16.9175901 0.420099432,12.3663143 0.420099432,9.03844914 C0.420099432,4.04665135 4.83904856,1.13960119e-11 10.290099,1.13960119e-11 Z M10.5002488,4.615387 C8.18065274,4.615387 6.30024858,6.33736648 6.30024858,8.46153582 C6.30024858,10.5857052 8.18065274,12.3076846 10.5002488,12.3076846 C12.8198449,12.3076846 14.7002491,10.5857052 14.7002491,8.46153582 C14.7002491,6.33736648 12.8198449,4.615387 10.5002488,4.615387 Z" id="path-3"></path>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="徐汇区巡航监测“谛听”系统（公共场所）备份-6" transform="translate(-1719.7107, -471.197)">
            <g id="编组-39备份" transform="translate(1713, 437.5) rotate(-66) translate(-1713, -437.5)translate(1340.2081, 143.8735)">
                <g id="首页_地图_定位_红备份-3" transform="translate(334.239, 339.7992) rotate(66) translate(-334.239, -339.7992)translate(323.739, 327.2992)">
                    <path d="M6.21782496e-12,2.10309684e-12 L21,2.10309684e-12 L21,24.9999656 L6.21782496e-12,24.9999656 Z" id="Frame-100-(Background)"></path>
                    <path d="M10.5,25 C16.2989899,25 21,23.5363177 21,21.730774 C21,19.9252303 16.2989899,18.461548 10.5,18.461548 C4.70101013,18.461548 -1.43920034e-12,19.9252303 -1.43920034e-12,21.730774 C-1.43920034e-12,23.5363177 4.70101013,25 10.5,25 Z" id="Ellipse-10" fill="url(#linearGradient-1)"></path>
                    <g id="Subtract">
                        <use fill="#C4C4C4" xlink:href="#path-3"></use>
                        <use fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>