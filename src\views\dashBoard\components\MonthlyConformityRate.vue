<template>
    <miniBorder title="公共场所监测月度合格率">
        <div ref="lineChartRef" class="lineChart"></div>
    </miniBorder>
</template>
<script setup lang="ts" name="MonthlyConformityRate">
import miniBorder from './miniBorder.vue';
import * as echarts from 'echarts';
import { ref, onMounted } from "vue";

const lineChartRef = ref()

const hexToRgba = (hex: string, opacity: number) => {
    let rgbaColor = '';
    let reg = /^#[\da-f]{6}$/i;
    if (reg.test(hex)) {
        rgbaColor = `rgba(${parseInt('0x' + hex.slice(1, 3))},${parseInt(
            '0x' + hex.slice(3, 5)
        )},${parseInt('0x' + hex.slice(5, 7))},${opacity})`;
    }
    return rgbaColor;
};

const initLine = () => {

    let color = '#01C4D3';
    const lineChart = echarts.init(lineChartRef.value, undefined, { renderer: 'svg' })
    const option = {
        color,
        tooltip: {
            trigger: 'axis',
        },
        legend: {
            orient: 'horizontal',
            right: 30,
            itemHeight: 10,
            itemWidth: 10,
            data: [{
                icon: 'circle',
                name: '月度检测合格率',
                textStyle: { color: '#FFFFFF', fontSize: 12 },
            }],
        },
        grid: {
            left: 0,
            top: 32,
            right: 32,
            bottom: '3%',
            containLabel: true,
        },
        xAxis: [{
            type: 'category',
            boundaryGap: false,
            data: [
                '一月',
                '二月',
                '三月',
                '四月',
                '五月',
                '六月',
                '七月',
                '八月',
                '九月',
                '十月',
                '十一月',
                '十二月',
            ],
            axisLine: {
                lineStyle: {
                    color: '#D9D9D9',
                },
            },
        }],
        yAxis: [{
            type: 'value',
            axisLabel: {
                textStyle: {
                    color: '#D9D9D9',
                },
            },
            splitLine: {
                lineStyle: {
                    // type: 'dashed',
                    color: '#444', // #E9E9E9
                },
            }
        }],
        series: [{
            name: '月度检测合格率',
            type: 'line',
            smooth: true,
            lineStyle: {
                color,
                shadowBlur: 3,
                shadowColor: hexToRgba(color, 0.5),
                shadowOffsetY: 0,
            },
            // showSymbol: false,
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
                    [
                        {
                            offset: 0,
                            color: hexToRgba(color, 1),
                        },
                        {
                            offset: 1,
                            color: hexToRgba(color, 0.15),
                        },
                    ],
                    false
                ),
                shadowColor: hexToRgba(color, 0.1),
                shadowBlur: 10,
            },
            data: [41, 64,99, 79,  55, 52, 86, 26, 32, 87, 68, 13],
            // 图表标注
            markPoint: {
                // 标记的图形，可以为图片
                // symbol:'pin',
                // 标记的大小
                symbolSize: 36,
                data: [
                    // 最大值标注
                    {
                        type: 'max',
                        name: 'Max',
                        itemStyle: {
                            opacity: 0.8
                        },
                        label: {
                        }
                    },
                ]
            },
        },
        ],
    };

    lineChart.setOption(option);
}

onMounted(() => { initLine() })
</script>

<style scoped lang="scss">
@font-face {
    font-family: Source Han Sans CN-Medium;
    src: url('../../../../src/assets/font/Source Han Sans CN Medium.otf');
}

.lineChart {
    width: 105%;
    height: 100%;
}
</style>
