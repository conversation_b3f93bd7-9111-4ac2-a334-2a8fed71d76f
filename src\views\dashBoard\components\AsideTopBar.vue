<template>
    <div class="top-bar">
        <img class="bgimg w100" src="../../../../src/assets/dashBoard/asideHeader.svg" />
        <!-- 标题 -->
        <div class="title">{{ props.title }}</div>

    </div>
</template>
<script setup lang="ts" name="TopBar">
const props = defineProps<{ title: string }>()

</script>
  
<style scoped lang="scss">
@font-face {
    font-family: Source Han Sans CN-Medium;
    src: url('../../../../src/assets/font/Source Han Sans CN Medium.otf');
}

.top-bar {
    height: 10vh;
    display: relative;
    overflow: hidden;

    @media screen and (max-width: 3840px) {
        font-size: 20px;
    }

    @media screen and (max-width: 3440px) {
        font-size: 16px;
    }

    @media screen and (max-width: 2560px) {
        font-size: 12px;
    }

    @media screen and (max-width: 1960px) {
        font-size: 10px;
    }

    @media screen and (max-width: 1440px) {
        font-size: 8px;
    }

    @media screen and (max-width: 1024px) {
        font-size: 6px;
    }

    .bgimg {
        position: relative;
        top: calc(-1%);
        // top: -1px;
        // transform: translateY(-2%);
    }

    .title {
        position: absolute;
        top: 0.6em;
        width: 24%;
        background-image: linear-gradient(180deg, #56f4fe 33%, #a3ffcd 85%);
        cursor: pointer;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 1.6em;
        font-family: Source Han Sans CN, Source Han Sans CN-Medium;
        font-weight: Medium;
        text-align: center;
        color: #56f4fe;
        line-height: 1.8em;
        letter-spacing: 0.08px;
        pointer-events: none;
    }
}
</style>