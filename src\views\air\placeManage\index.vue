<template>
    <div class="system-place-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-tabs type="card" v-model="activeName"  >
                <el-tab-pane label="CO2" name="CO2">
                    <div class="system-place-search mb15">
                        <el-input v-model="state.keyWord" :placeholder="$t('message.views.placeholderPlaceNameSearch')"
                            style="max-width: 240px" clearable>
                        </el-input>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData()">
                            <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }} </el-button>
                        <el-button size="default" type="success" class="ml10" @click="onOpenAddDevice('add')">
                            <el-icon><ele-FolderAdd /></el-icon>{{ $t('message.views.placeAdd') }} </el-button>
                    </div>
                    <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%;height: 57vh;">
                        <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
                        <el-table-column prop="placeName" :label="$t('message.views.placeName')"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="placeType" :label="$t('message.views.placeType')"></el-table-column>
                        <el-table-column prop="region" :label="$t('message.views.placeArea')"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="address" :label="$t('message.views.placeAddress')"
                            show-overflow-tooltip></el-table-column>
                        <!-- <el-table-column prop="head" :label="$t('message.views.placeHead')" show-overflow-tooltip>
                            <template #default="scope">
                                <el-text>{{ scope.row.head.name }}：</el-text>
                                <el-text>{{ scope.row.head.phoneNum }}</el-text>
                            </template>
                        </el-table-column>
                        <el-table-column prop="engineeringHead" :label="$t('message.views.placeEngineeringHead')"
                            show-overflow-tooltip>
                            <template #default="scope">
                                <el-text>{{ scope.row.engineeringHead.name }}：</el-text>
                                <el-text>{{ scope.row.engineeringHead.phoneNum }}</el-text>
                            </template>
                        </el-table-column> -->
                        <el-table-column :label="$t('message.common.operation')" width="120">
                            <template #default="scope">
                                <el-button size="small" text type="primary" @click="onOpenEditDevice('update', scope.row)">{{
                                    $t('message.common.modify') }}</el-button>
                                <el-button size="small" text type="primary" @click="onRowDel(scope.row)">{{
                                    $t('message.common.delete') }}</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                        :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
                        v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                        :total="state.tableData.total">
                    </el-pagination>
                </el-tab-pane>
                <el-tab-pane label="空气质量巡检" name="Robot">
                    <robotPlaceManage></robotPlaceManage>
                </el-tab-pane>
            </el-tabs>
            
        </el-card>
        <PlaceDialog ref="PlaceDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="airPlaceManage">
// import type { TabsPaneContext } from 'element-plus'
import robotPlaceManage from '../robotPlaceManage/index.vue'
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getPlaceList, deletePlace } from "/@/api/publicPlace/index";
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// 引入组件
const PlaceDialog = defineAsyncComponent(() => import('./dialog.vue'));
// 组件ref
const PlaceDialogRef = ref();
// 定义变量内容
const state = reactive<placeState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 查询输入
    keyWord: '',
});

const activeName = ref('Robot')
// 获取表格数据
const getTableData = async (keyWord: string = state.keyWord) => {
    state.tableData.loading = true;
    const res = await getPlaceList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        keyWord,
        _type: 'public_place',
    })
    const dataList = res.data.list
    // dataList.forEach((ele) => {
    //     if (ele.region.length) {
    //         ele.area = ele.region.reduce((pre: string, cur: regionItemType) => (pre + cur.name), ele.region[0].name)
    //         const area_code: string[] = []
    //         ele.region.forEach((item: regionItemType) => {
    //             area_code.push(item.area_code)
    //         })
    //         ele.area_code = area_code
    //     }
    //     // ！如果不赋初值，使用 scope.row 时会出错！（获取一条新的包含空数据的row，空的那个字段会使用上一个 row 的数据）
    //     else {
    //         ele.area = ''
    //         ele.area_code = []
    //     }
    // });
    const data = dataList
    state.tableData.data = data;
    state.tableData.total = res.data.count;
    state.tableData.loading = false;
};
// 打开新增场所弹窗
const onOpenAddDevice = (type: string) => {
    PlaceDialogRef.value.openDialog(type);
};
// 打开修改场所弹窗
const onOpenEditDevice = (type: string, row: rowPlaceType) => {
    PlaceDialogRef.value.openDialog(type, row);
};
// 删除场所
const onRowDel = (row: rowPlaceType) => {
    ElMessageBox.confirm(t('message.views.deletePlcaeConfirm', { placeName: row.placeName }), t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deletePlace(row._id)
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};
// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};

// DOM挂载完
onMounted(() => {
    getTableData();
});
</script>

<style scoped lang="scss">
.system-place-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-place-search {
    display: flex;
    align-content: space-around;
}
</style>
