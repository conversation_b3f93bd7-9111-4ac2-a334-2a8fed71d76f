// 定义内容
export default {
	common: {
		more: 'More',
		empty: 'No data',
		noMessage: 'No message',
		all: 'All',
		startTask: 'Start',
		overTask: 'Complete',
		PENDING: 'Confirm to change this task status to "In Progress"?',
		IN_PROGRESS: 'Confirm to change this task status to "Completed"?',
		taskStatusChange: 'Task updated successfully!',
		index: 'Index',
		name: 'Name',
		unit: 'Unit',
		dataUnit: 'Data unit',
		threshold: 'Threshold',
		phoneNum: 'Phone number | Contact number',
		password: 'Password',
		user: 'User | Regular user',
		admin: 'Administrator',
		createdAt: 'Created time',
		updatedAt: 'Updated time',
		username: 'Username | Login account',
		WarningType: 'Warning type',
		isAble: 'Is enabled',
		able: 'Yes',
		unable: 'No',
		status: 'Status',
		enable: 'Enable | Active',
		disable: 'Disable | Inactive',
		operation: 'Operation',
		status1: 'Standby',
		status2: 'In motion',
		status3: 'Error',
		status4: 'Disconnected',
		status5: 'Shutdown',
		add: 'Add',
		create: 'Create',
		delete: 'Delete',
		update: 'Modify | Update',
		modify: 'Modify',
		search: 'Search',
		cancel: 'Cancel',
		confirm: 'Confirm',
		prompt: 'Prompt',
		lift: 'Lift',
		export: 'Export',
		edit: 'Edit',
		placeholderName: 'Please enter the name',
		placeholderPhoneNum: 'Please enter the phone number',
		placeholderPassword: 'Please enter the password',
		placeholderDate: 'Please select the date | Please select the query date',
		placeholderSearchKeyWord: 'Please enter the search keyword',
		addSuccess: 'Added successfully',
		modifySuccess: 'Modified successfully',
	},
	views: {
		selectSamplePoint: 'Select Sampling Point',
		samplePointName: 'Sampling Point Name',
		serialNumber: 'Serial Number',
		addSamplePoint: 'Add Sampling Point',
		startTime: 'Start Time',
		intervalTime: 'Interval Time',
		samplePointList: 'Sampling Point Sequence',
		sample_point: 'Sampling point',
		MetricsRange: 'Metrics range',
		MeasurementData: 'Sampling data',
		Result: 'Sampling result',
		Metrics: 'Metrics',
		MetricsInfo: 'Metrics details',
		deleteTaskConfirm: 'This operation will permanently delete the task ({taskNum}), continue?',
		taskAdd: 'Add inspection task',
		taskNum: 'Task number',
		taskStatus: 'Task status',
		placeholderTaskStatus: 'Please select the task status',
		// Device management
		deviceAdd: 'Add device',
		deviceName: 'Device name',
		deviceNum: 'Device number',
		deviceType: 'Device type',
		devicePlace: 'Installation place',
		devicePosition: 'Installation position',
		deviceStatus: 'Device status',
		sensorData: 'Metrics list',
		deviceModel: 'Device model',
		deviceAdminName: 'Device administrator name',
		deviceAdminPhone: 'Device administrator phone',
		placeholderDeviceKeyword: 'Please enter the device number/name/type',
		placeholderRobotDeviceKeyword: 'Please enter the device number/name',
		placeholderDevicePlcae: 'Please select the installation place',
		placeholderRobotDevice: 'Please select the device',
		deleteDeviceConfirm: 'This operation will permanently delete the device ({name}) and its detection data, continue?',
		deleteRobotDeviceConfirm: 'This operation will permanently delete the device ({deviceNickName}) and its detection data, continue?',
		// Dialog
		deviceDialogUpdateTitle: 'Modify device',
		deviceDialogAddTitle: 'Add device',
		placeholderStartTime: 'Please select start time',
		placeholderiIntervalTime: 'Please select interval time',
		placeholderiSamplePointList: 'Please add sampling point sequence',
		placeholderSamplePointName: 'Please select sampling point name',
		placeholderDeviceName: 'Please enter the device name',
		placeholderDeviceNum: 'Please enter the device number',
		placeholderdeviceNickName: 'Please select deviceName',
		placeholderDeviceType: 'Device type will be auto-filled after selecting the device number',
		placeholderDeviceThreshold: 'Please enter the device threshold',
		placeholderDevicePlace: 'Please select the installation place',
		placeholderDevicePosition: 'Please select the installation position',
		placeholderSensorData: 'Please select the metrics list',
		placeholderDeviceModel: 'Please enter the device model',
		placeholderchangeDeviceModel: 'Please select the device model',
		placeholderchangeDeviceNum: 'Please select the device number',
		placeholderchangeDeviceStatus: 'Please select the device status',
		placeholderDeviceAdminName: 'Please enter the device administrator name',
		placeholderDeviceAdminPhone: 'Please enter the device administrator phone',
		placeholderResult: 'Please select the sampling result',
		placeholderTaskNumSearch: 'Please enter the task number',
		placeholderWarningType: 'Please select the warning type',
		// User management
		userAdd: 'Add user',
		userName: 'Username',
		userRole: 'User role',
		userStatus: 'User status',
		placeholderUserKeyword: 'Please enter the username/name/phone number',
		deleteUserConfirm: 'This operation will permanently delete the user ({userName}), continue?',
		// Dialog
		userDialogUpdateTitle: 'Modify user',
		userDialogAddTitle: 'Add user',
		placeholderUserName: 'Please enter the username',
		placeholderUserRole: 'Please select the user role',
		rulesUserPasswordLength: 'Password length should be between 6 and 16',
		rulesUserPhoneNumReg: 'Invalid phone number format',
		// Air monitoring
		CO2ConcentrationDetection: 'CO2 concentration detection',
		timeSpan: 'Time span',
		month: 'Month',
		day: 'Day',
		hour: 'Hour',
		minute: 'Minute | {n} minutes',
		startingAndEndingTime: 'Start and end time',
		startingTime: 'Start time',
		endingTime: 'End time',
		lastMonth: 'Last month',
		lastWeek: 'Last week',
		lastDay: 'Last day',
		lastHour: 'Last hour',
		// History
		receiveTime: 'Receive time',
		historyData: 'Data(ppm)|Percentage(%)',
		sendmsg: 'Send SMS',
		// Place management
		placeAdd: 'Add place',
		placeModify: 'Modify place',
		placeholderPlaceNameSearch: 'Please enter the place name to search',
		placeName: 'Place name',
		placeType: 'Place type',
		placeArea: 'Place area',
		placeAddress: 'Place address',
		placeHead: 'Main person in charge',
		placeEngineeringHead: 'Engineering department head',
		deletePlcaeConfirm: 'This operation will permanently delete the place ({placeName}), continue?',
		InchargeName: 'Place person in charge name',
		InchargePhone: 'Place person in charge phone',
		SupervisionName: 'Place supervisor name',
		SupervisionPhone: 'Place supervisor phone',
		// Dialog
		placeholderPlaceName: 'Please enter the place name',
		placeholderchangePlaceName: 'Please select the place',
		placeholderPlaceType: 'Please select the place type',
		placeholderPlaceAddress: 'Please enter the place address',
		placeholderInchargeName: 'Please enter the place person in charge name',
		placeholderInchargePhone: 'Please enter the place person in charge phone',
		placeholderSupervisionName: 'Please enter the place supervisor name',
		placeholderSupervisionPhone: 'Please enter the place supervisor phone',
		rulesPlaceArea: 'Please select the place area',
		rulesPlaceAddress: 'Please select the place address',
		rulesPlaceHeadName: 'Please enter the main person in charge name',
		rulesPlaceHeadPhoneNum: 'Please enter the main person in charge phone number',
		rulesPlaceEngineeringHeadName: 'Please enter the engineering department head name',
		rulesPlaceEngineeringHeadPhoneNum: 'Please enter the engineering department head phone number',
		rulesPlacePhoneNum: 'Invalid phone number format',
		// Early warning
		messageContent: 'Message content',
		elaryWarningStatus: 'Disposal status',
		pending: 'Pending',
		lifted: 'Lifted',
		placeholderWarningDeviceNum: 'Please select the device number',
		placeholderWarningStatus: 'Please select the disposal status',
		deleteWarningConfirm: 'This operation will permanently delete the warning ({createdAt}), continue?',
		liftWarningConfirm: 'Confirm to lift all current ({createdAt}) and previous warnings for this device?',
		robotliftWarningConfirm: 'Confirm to lift the current ({createdAt}) warning for this device?',
		// Water
		waterSname: 'Site name',
		turbidity: 'Turbidity(NTU)|Turbidity',
		conductivity: 'Conductivity(μS/cm)|Conductivity',
		waterTemperature: 'Water temperature(℃)|Water temperature',
		totalChlorine: 'Total chlorine(mg/L)|Total chlorine',
		placeholderWaterSite: 'Please select the site',

		// xdj
		store: 'Store',
		headStore: 'Head store',
		storeName: 'Store name',
		storeType: 'Store type',
		creator: 'Creator',

		// personal
		personalInfo: 'Personal information',
		updateInfo: 'Update information',
		basiclInfo: 'Basic information',

		// privateMessage
		privateMessageTitle: 'Message title',
		privateMessageContent: 'Message content',
		privateMessageStatus: 'Message status',
		privateMessageMarkRead: 'Mark as read',
		unread: 'Unread',
		read: 'Read',
		placeholderMessageStatus: 'Please select the message status',
		markReadConfirm: 'Confirm to mark this message ({createdAt}) as read?',

		// warningTextMessage
		warningMessageContent: 'SMS content',
		warningMessageSendTime: 'SMS send time',
	},

	router: {
		placeHead: 'Place person in charge',
		latest_CO2: 'CO2',
		latest_Robot: 'Air quality inspection',
		home: 'Home',
		system: 'System settings',
		systemMenu: 'Menu management',
		systemRole: 'Role management',
		systemDept: 'Department management',
		systemDic: 'Dictionary management',
		limits: 'Permission management',
		limitsFrontEnd: 'Frontend control',
		limitsFrontEndPage: 'Page permission',
		limitsFrontEndBtn: 'Button permission',
		limitsBackEnd: 'Backend control',
		limitsBackEndEndPage: 'Page permission',
		taskManage: 'Device inspection task management',
		makeTableDemo: 'Table demo',
		chartIndex: 'Big data chart',
		// 
		history: 'History data',
		latest: 'Latest data',

		systemUser: 'User management',
		deviceMana: 'Device management',

		air: 'Public places air monitoring',
		placeManage: 'Place management',
		elaryWarning: 'Early warning disposal',

		water: 'Water quality monitoring',

		cleaningRoomMonitor: 'Cleaning room monitoring',
		monitorList: 'Monitor list',

		disinfectionProductAudit: 'Disinfection product audit',
		xdjUser: 'User management',
		xdjStore: 'Store management',
		xdjDetection: 'Disinfectant inspection',

		personal: 'Personal center',

		elaryWarningMsg: 'Early warning message',

		privateMessage: 'Private message',
		warningTextMessage: 'Warning SMS',

		developing: 'Developing',

		dashBoard: 'Dashboard'
	},
	staticRoutes: {
		signIn: 'Sign in',
		notFound: 'Not found',
		noPower: 'No permission',
	},
	user: {
		title0: 'Component size',
		title1: 'Language switching',
		title2: 'Menu search',
		title3: 'Layout configuration',
		title4: 'News',
		title5: 'Full screen on',
		title6: 'Full screen off',
		dropdownLarge: 'Large',
		dropdownDefault: 'Default',
		dropdownSmall: 'Small',
		dropdown1: 'Home page',
		dropdown2: 'Personal center',
		dropdown3: '404',
		dropdown4: '401',
		dropdown5: 'Log out',
		dropdown6: 'Code repository',
		searchPlaceholder: 'Menu search: support Chinese, routing path',
		newTitle: 'Notice',
		newBtn: 'All read',
		newGo: 'Go to the notification center',
		newDesc: 'No notice',
		logOutTitle: 'Tips',
		logOutMessage: 'This operation will log out. Do you want to continue?',
		logOutConfirm: 'Confirm',
		logOutCancel: 'Cancel',
		logOutExit: 'Exiting',

		dashBoard: 'Data dashboard',
	},
	tagsView: {
		refresh: 'Refresh',
		close: 'Close',
		closeOther: 'Close other',
		closeAll: 'Close all',
		fullscreen: 'Fullscreen',
		closeFullscreen: 'Close fullscreen',
	},
	notFound: {
		foundTitle: 'Wrong address input, please re-enter the address~',
		foundMsg: 'You can check the web address first, and then re-enter or give us feedback.',
		foundBtn: 'Back to home page',
	},
	noAccess: {
		accessTitle: 'You are not authorized to operate~',
		accessMsg: 'Contact information: add QQ group discussion *********',
		accessBtn: 'Reauthorization',
	},
	layout: {
		configTitle: 'Layout configuration',
		oneTitle: 'Global themes',
		twoTopTitle: 'Top bar settings',
		twoMenuTitle: 'Menu settings',
		twoColumnsTitle: 'Columns settings',
		twoTopBar: 'Top bar background',
		twoTopBarColor: 'Top bar default font color',
		twoIsTopBarColorGradual: 'Top bar gradient',
		twoMenuBar: 'Menu background',
		twoMenuBarColor: 'Menu default font color',
		twoMenuBarActiveColor: 'Menu highlight color',
		twoIsMenuBarColorGradual: 'Menu gradient',
		twoColumnsMenuBar: 'Column menu background',
		twoColumnsMenuBarColor: 'Column menu default font color',
		twoIsColumnsMenuBarColorGradual: 'Column gradient',
		twoIsColumnsMenuHoverPreload: 'Column menu hover preload',
		threeTitle: 'Interface settings',
		threeIsCollapse: 'Menu horizontal collapse',
		threeIsUniqueOpened: 'Menu accordion',
		threeIsFixedHeader: 'Fixed header',
		threeIsClassicSplitMenu: 'Classic layout split menu',
		threeIsLockScreen: 'Open the lock screen',
		threeLockScreenTime: 'Screen locking(s/s)',
		fourTitle: 'Interface display',
		fourIsShowLogo: 'Sidebar logo',
		fourIsBreadcrumb: 'Open breadcrumb',
		fourIsBreadcrumbIcon: 'Open breadcrumb icon',
		fourIsTagsview: 'Open tagsview',
		fourIsTagsviewIcon: 'Open tagsview icon',
		fourIsCacheTagsView: 'Enable tagsview cache',
		fourIsSortableTagsView: 'Enable tagsview drag',
		fourIsShareTagsView: 'Enable tagsview sharing',
		fourIsFooter: 'Open footer',
		fourIsGrayscale: 'Grey model',
		fourIsInvert: 'Color weak mode',
		fourIsDark: 'Dark mode',
		fourIsWartermark: 'Turn on watermark',
		fourWartermarkText: 'Watermark copy',
		fiveTitle: 'Other settings',
		fiveTagsStyle: 'Tagsview style',
		fiveAnimation: 'Page animation',
		fiveColumnsAsideStyle: 'Column style',
		fiveColumnsAsideLayout: 'Column layout',
		sixTitle: 'Layout switch',
		sixDefaults: 'Default',
		sixClassic: 'Classic',
		sixTransverse: 'Transverse',
		sixColumns: 'Columns',
		tipText: 'Click the button below to copy the layout configuration to `/src/stores/themeConfig.ts` It has been modified in.',
		copyText: 'Copy configuration',
		resetText: 'Restore default',
		copyTextSuccess: 'Copy succeeded!',
		copyTextError: 'Copy failed!',
	}
};