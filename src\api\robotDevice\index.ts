import request from '/@/utils/request';

// 获取设备编号
export function getDeviceNums() {
    return request({
        url: '/robotDevice/getDeviceNums',
        method: 'get',
    });
}

//获取设备列表
export function getRobotDeviceList(query: { page?: number; pageSize?: number; keyWord?: string; status?: string;deviceModel?: string;deviceName?: string }) {
    return request({
        url: '/robotDevice/all',
        method: 'get',
        params: query,
    });
}

// 根据id获取某个设备信息
export function getRobotDeviceInfo(query: { _id: string }) {
    return request({
        url: '/robotDevice/' + query._id.toString(),
        method: 'get',
    });
}

// 添加设备 
export function createRobotDevice(data: API.CreateRobotDeviceParams) {
    return request({
        url: '/robotDevice',
        method: 'post',
        data,
    });
}

// 更新设备信息
export function updateRobotDevice(data: API.UpdateRobotDeviceParams) {
    return request({
        url: '/robotDevice',
        method: 'put',
        data,
    });
}

// 删除设备
export function deleteRobotDevice(id: string) {
    return request({
        url: '/robotDevice/' + id.toString(),
        method: 'delete',
    });
}
