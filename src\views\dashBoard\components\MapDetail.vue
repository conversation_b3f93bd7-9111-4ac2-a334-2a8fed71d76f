<template>
    <div class="mapDetail-container" >
        <div class="title">
            <span style="font-size: 0.55vw;color: #18CACA;">{{ props.mapDetailData.name }}</span>
        </div>
        <div class="content" v-if="props.mapDetailData.type==='末梢水'">
            <div class="item" v-for="item in props.mapDetailData.list" :key="item.name">
                <div class="item-left">
                    <span>
                        {{ item.name }}:
                    </span>
                </div>
                <div class="item-right">
                    <div class="bar" :style="{ width: item.value / item.total * 80 + '%' }"></div>
                    <span class="value">
                        {{ item.value }}
                    </span>
                </div>
            </div>
        </div>
        <div class="content" v-if="props.mapDetailData.type==='空气质量巡检'">
            <div class="lis">
                <div class="il">设备名：</div>
                <div class="ir">{{ tableData.deviceName }}</div>
            </div>
            <div class="lis">
                <div class="il">时间：</div>
                <div class="ir">{{ tableData.time }}</div>
            </div>
            <div class="lis">
                <div class="il">结果：</div>
                <div :class="tableData.result==='合格' ? 'ir' :'ir nook'" >
                    {{tableData.result}}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="MapDetail">
import { getLatestRobotDataByPlace } from '/@/api/latest/index'
const props = withDefaults(defineProps<{ mapDetailData?: any}>(), {
    mapDetailData: {} as any,
    
});
import { ref, watch } from "vue";
const tableData = ref<any>({});
watch(() => props.mapDetailData, () => {
    if(props.mapDetailData.type==='空气质量巡检'){
       getLatestRobotDataByPlaces()
    }
});
const getLatestRobotDataByPlaces = async () => {
    const res = await getLatestRobotDataByPlace({
        robotPubilcPlace: props.mapDetailData._id
    });
    // 2. 提取基础字段
    const deviceInfo = res.data.deviceInfo;
    const deviceNickName = deviceInfo && deviceInfo.deviceNickName 
        ? deviceInfo.deviceNickName 
        : '未知设备';
    
    const robotData = res.data.robotData;
    const timestamp = robotData.time;

    // 3. 时间戳转换 (格式: YYYY-MM-DD HH:mm:ss)
    const formatTime = (ts: number) => {
        if (!ts) return '无时间数据';
        const date = new Date(ts);
        const pad = (num: number) => num < 10 ? '0' + num : num.toString();
        return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +
               `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
    };

    // 4. 数据校验逻辑
    let finalResult = '无有效数据';
    if (robotData.sensorData && props.mapDetailData.sensorData) {
        let allValid = true;
        let hasData = false;

        props.mapDetailData.sensorData.forEach((sensor: any) => {
            if (sensor && sensor.choose) {
                const sensorData = robotData.sensorData;
                const value = sensorData && sensorData[sensor.EnglishName];
                if (value !== undefined && value !== null) {
                    hasData = true;
                    const rangeArray = sensor.sensorPlaceAndRange;
                    const range = rangeArray && rangeArray[0] && rangeArray[0].Range;
                    if (range) {
                        const minValue = range.minValue ? parseFloat(range.minValue) : NaN;
                        const maxValue = range.maxValue ? parseFloat(range.maxValue) : NaN;
                        const minType = range.minValueType;
                        const maxType = range.maxValueType;

                        let isValid = true;
                        if (!isNaN(minValue)) {
                            isValid = minType === 2 
                                ? value >= minValue 
                                : value > minValue;
                        }
                        if (!isNaN(maxValue) && isValid) {
                            isValid = maxType === 2 
                                ? value <= maxValue 
                                : value < maxValue;
                        }

                        if (!isValid) allValid = false;
                    }
                }
            }
        });

        finalResult = hasData ? (allValid ? '合格' : '不合格') : '无有效数据';
    }
    tableData.value={
        deviceName: deviceNickName,
        time: formatTime(timestamp),
        result: finalResult
    }

};

</script>

<style scoped lang="scss">
.mapDetail-container {

    width: 8.2vw;
    height: 5.5vw;
    padding: 0.5vw;
    padding-right: 0.6vw;
    background: url(../../../../src/assets/dashBoard/mapDetail.svg);
    background-size: 100% 100%;

    .title {
        width: 100%;
        height: 20%;
        display: flex;
        justify-content: center;
    }

    .content {
        width: 100%;
        height: 80%;
        .lis{
            font-size: 0.5vw;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            .il{
                margin-right: 5px;
            }
            .ir{
                color: #18CACA;
            }
            .nook{
                color: #f56c6c;
            }
        }
        .item {
            display: flex;
            height: 20%;
            font-size: 0.5vw;
            justify-content: space-around;
        }

        .item-left {
            width: 32%;
            position: relative;
            // top: -25%;
        }

        .item-right {
            width: 68%;
            display: flex;

            .bar {
                height: 0.2vw;
                background-color: #18CACA;
                position: relative;
                top: 0.175vw;
                margin-left: 2px;
            }

            .value {
                vertical-align: center;
                font-size: 0.5vw;
                margin-left: 4px;
                color: #18CACA;
            }
        }
    }
}
</style>