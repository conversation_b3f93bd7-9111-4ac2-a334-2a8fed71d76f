import axios, { AxiosInstance } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Session } from '/@/utils/storage';
import qs from 'qs';

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
	baseURL: import.meta.env.VITE_API_URL,
	timeout: 50000,
	headers: { 'Content-Type': 'application/json' },
	paramsSerializer: {
		serialize(params) {
			return qs.stringify(params, { allowDots: true });
		},
	},
});

// 添加请求拦截器
service.interceptors.request.use(
	(config) => {
		// 在发送请求之前做些什么 token
		if (Session.get('token')) {
			config.headers!['Authorization'] = `${Session.get('token')}`;
		}
		return config;
	},
	(error) => {
		// 对请求错误做些什么
		return Promise.reject(error);
	}
);

// 添加响应拦截器
service.interceptors.response.use(
	(response) => {
		// 对响应数据做点什么
		const res = response.data;
		const statusCode = res.code || res.statusCode;
		if (statusCode && statusCode !== 0) {
			// `token` 过期或者账号已在别处登录
			if (statusCode === 401 || statusCode === 4001) {
				Session.clear(); // 清除浏览器全部临时缓存
				ElMessageBox.alert('账号验证失败，请重新登录', '提示', {})
					.finally(() => {
						window.location.href = '/'; // 去登录页
					})
			}
			if (statusCode === -1 || statusCode === 400) {
				ElMessage.error(res.message)
			}
			// 服务器出错
			if (statusCode === 500) {
				ElMessage.error('服务端出错！')
			}
			return Promise.reject(response);
		} else {
			return res;
		}
	},
	(error) => {
		// 重新登录
		if (error.response.data && error.response.data.message && new RegExp(/登录/).test(error.response.data.message)) {
			Session.clear(); // 清除session
			ElMessageBox.alert(error.response.data.message, '提示', {})
				.finally(() => {
					window.location.href = '/'; // 去登录页
				})
		}
		// 其他错误响应
		else if (error.message.indexOf('timeout') != -1) {
			ElMessage.error('网络超时');
		} else if (error.message == 'Network Error') {
			ElMessage.error('网络连接错误');
		} else if (error.message == "Request failed with status code 500") {
			ElMessage.error('服务器错误');
		} else if (error.response.data) {
			ElMessage.error((error.response.data.message?.toString() || error.response.statusText));
		}
		else {
			ElMessage.error(error.message)
		}


		return Promise.reject(error);
	}
);

// 导出 axios 实例
export default service;
