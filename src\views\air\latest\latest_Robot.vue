<template>
	<div class="home-container layout-pd">
		<!-- socket实时数据 -->
		<el-row :gutter="15" class="home-card-one mb15">
			<el-col class="home-media-sm">
				<div class="home-card-item flex">
					<div class="flex-margin flex w100">
						<div class="flex-auto">
							<span class="font30">{{ route.query.deviceNickName }}</span>
							
							<div class="mt10">{{ route.query.deviceNum }}</div>
						</div>
						
					</div>
				</div>
			</el-col>
		</el-row>
		<!-- 折线图 -->
		<el-row :gutter="15" class="home-card-two mb15">
			<el-col>
				<div class="home-card-item">
					<div class="flex-warp">
						<div class="flex-warp-item  mr10">
							<div class="flex-warp-item-box">
								{{ $t('message.views.placeName') }}：
								<el-select v-model="selectData.robotPubilcPlace"  placeholder="请选择"
									style="width: 100px;"  @change="changeData('robotPubilcPlace')">
									<el-option  label="未关联场所的数据" value="norobotPubilcPlace" />
									<el-option v-for="item in PlaceList" :key="item._id" :label="item.placeName" :value="item._id" />
								</el-select>
							</div>
						</div>
						<div class="flex-warp-item  mr10">
							<div class="flex-warp-item-box">
								{{ $t('message.views.sensorData') }}：
								<el-select v-model="selectData.sensorType" placeholder="请选择"
									style="width: 100px;"  @change="changeData">
									<el-option :label="item.sensorName" v-for="item in sensorData" :key="item.EnglishName" :value="item.EnglishName" />
									
								</el-select>
							</div>
						</div>
						<div class="flex-warp-item  mr10">
							<div class="flex-warp-item-box">
								{{ $t('message.views.timeSpan') }}：
								<el-select v-model="selectData.timeType" @change="changeData" placeholder="请选择"
									style="width: 80px;">
									<el-option :label="$t('message.views.month')" value="month_data" />
									<el-option :label="$t('message.views.day')" value="day_data" />
									<el-option :label="$t('message.views.hour')" value="hour_data" />
									<el-option :label="$t('message.views.minute', { n: 5 })" value="minute5_data" />
									<el-option :label="$t('message.views.minute')" value="minute_data" />
								</el-select>
							</div>
						</div>
						<div class="flex-warp-item">
							<div class="flex-warp-item-box">
								{{ $t('message.views.startingAndEndingTime') }}：
								<el-date-picker v-model="timeSelect" type="datetimerange" :shortcuts="state.shortcuts"
									range-separator="-" :start-placeholder="$t('message.views.startingTime')"
									:end-placeholder="$t('message.views.endingTime')" :default-time="defaultTime" :disabledDate="disabledDate" @change="changeData"/>
							</div>
						</div>
						
					</div>
					<div style="height: 100%" ref="homeLineRef"></div>
				</div>
			</el-col>
		</el-row>
		<el-row v-show="false">
			<el-col>
				
			</el-col>
		</el-row>

	</div>
</template>

<script setup lang="ts" name="disinfectant">
import { getSensorData } from '/@/api/publicPlace/index'
import { getAllRobotData,getMeasuredPlacesByDeviceName } from "/@/api/latest/index";

import { reactive, onMounted, ref, watch, nextTick, onActivated, markRaw, computed } from 'vue';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus'
import { useThemeConfig } from '/@/stores/themeConfig';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';

import { debounce } from 'lodash';
// import { timestampToTime } from '/@/utils/toolsValidate';

import { useRoute } from 'vue-router';
// const router = useRouter();
const route = useRoute();

import { useI18n } from 'vue-i18n';
const { t } = useI18n()

// 指标库
const sensorData=ref<any>([])

const sensorDataCopy=ref<any>([])
// 场所列表
const PlaceList=ref<any>([])
// 选择数据
const selectedSensor=ref<any>({})
const selectData=ref<any>({
	// 设备编号
	deviceName:route.query.deviceName,
	// 选择的场所
	robotPubilcPlace:route.query.robotPublicPlace,
	// 选择的指标
	sensorType:'temperature',
	// 时间跨度
	timeType:'hour_data',
	// 解析后用来发送请求的时间参数
	startTime: '',
	endTime: ''
})

// 组件ref
const homeLineRef = ref();
// store
const storesTagsViewRoutes = useTagsViewRoutes();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);

// 时间选择器
const timeSelect = ref('')
// 默认起始和结束时间 '00:00:00', '23:59:59'
const defaultTime: [Date, Date] = [
	new Date(2000, 1, 1, 0, 0, 0),
	new Date(2000, 2, 1, 23, 59, 59),
]
// 不能选择今天之后的日期
const disabledDate = (time: Date) => {
	return time.getTime() > Date.now()
}

// 配置数据
const state = reactive({
	// socket实时数据（最新数据）
	detectedData: {} as detectionState,
	global: {
		homeChartOne: null,
		dispose: [null, '', undefined],
	} as any,

	detectedStyle: {
		title: '实时二氧化碳检测',
		icon: 'fa fa-meetup',
		color1: '#6690F9',
		color2: '--next-color-primary-lighter',
		color3: '--el-color-primary',
	},
	myCharts: [] as EmptyArrayType,
	charts: {
		theme: '',
		bgColor: '',
		color: '#303133',
	},
	shortcuts: [
		{
			text: computed(() => t('message.views.lastMonth')),
			value: () => {
				const end = new Date()
				const start = new Date()
				start.setTime(start.getTime() - 1000 * 3600 * 24 * 30)
				return [start, end]
			},
		},
		{
			text: computed(() => t('message.views.lastWeek')),
			value: () => {
				const end = new Date()
				const start = new Date()
				start.setTime(start.getTime() - 1000 * 3600 * 24 * 7)
				return [start, end]
			},
		},

		{
			text: computed(() => t('message.views.lastDay')),
			value: () => {
				const end = new Date()
				const start = new Date()
				start.setTime(start.getTime() - 1000 * 3600 * 24)
				return [start, end]
			},
		},
		{
			text: computed(() => t('message.views.lastHour')),
			value: () => {
				const end = new Date()
				const start = new Date()
				start.setTime(start.getTime() - 1000 * 3600)
				return [start, end]
			},
		},
	]
});
// 折线图配置项
const lineChartOption = reactive({
	backgroundColor: state.charts.bgColor,
	color: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC'],
	title: {
		text: "温度",
		x: 'left',
		textStyle: { fontSize: '15', color: state.charts.color },
	},
	grid: { top: 120, right: 42, bottom: 40, left: 42 },
	tooltip: { trigger: 'axis' },
	toolbox: {
		show: true,
		left: 0,
		top: 20,
		// feature: {
		// 	saveAsImage: {}  // 导出图片
		// },
		feature: {
			saveAsImage: {},  // 导出图片
			dataZoom: {
				yAxisIndex: 'none'
			},
			dataView: {
				readOnly: false,
				optionToContent: function (opt: { xAxis: { data: any; }[]; series: { data: any; }[]; }) {
					let axisData = opt.xAxis[0].data;
					let seriesData = opt.series[0].data;
					let table =
						`<table style="margin:10px 20px;border-collapse:collapse;font-size:13px;text-align:center;width:calc(100% - 40px);line-height: 32px;border:1px solid #E5EFFB"><tbody><tr style="border-bottom:1px solid #E5EFFB; font-weight:bolder; font-size:14px;">
						<td>时间</td>
						<td>${selectedSensor.value.sensorName}</td>
						</tr>`;
					for (let i = 0, l = axisData.length; i < l; i++) {
						table +=
							`<tr style="border-bottom:1px solid #E5EFFB">
							<td> ${axisData[i]} </td>
							<td> ${seriesData[i]}${selectedSensor.value.unit} </td>
							</tr>`;
					}
					table += '</tbody></table>';
					return table;
				},
				textColor: '#0063E0',
				buttonColor: '#0063E0'
			},
			// magicType: { type: ['line', 'bar'] },
			// restore: {},
		},
	},
	legend: {
        data: [] as string[], // 初始化 legend 数据为空数组
        top: 10, // 将 legend 放在图表底部
		
    },
	visualMap:{} as any,
	xAxis: { data: [] as any[] },
	yAxis: [
		{
			type: 'value',
			name: '单位：ppm',
			splitLine: { show: true, lineStyle: { type: 'dashed', color: '#f5f5f5' } },
		},
	],

	series: [] as any[],
	dataZoom: [
		{
			type: 'inside',
			start: 0,
			end: 100,
			minValueSpan: 9
		},
		{
			show: false,
			type: 'slider',
			bottom: 30,
			start: 0,
			end: 100,
			minValueSpan: 9
		}
	]
})



// 根据筛选条件获取数据
const changeData=debounce((robotPubilcPlace: string) => {
	if(robotPubilcPlace==='robotPubilcPlace'){
		const selectedPlace = PlaceList.value.find((place: any) => place._id === selectData.value.robotPubilcPlace);
		if (selectedPlace) {
			sensorData.value = selectedPlace.sensorData.filter((sensor: any) => sensor.choose);
		}
		selectData.value.sensorType=sensorData.value[0].EnglishName
	}
	getAllRobotDatas()
}, 500)
// 折线图初始化
const initLineChart = () => {
	if (!state.global.dispose.some((b: any) => b === state.global.homeChartOne)) state.global.homeChartOne.dispose();
	state.global.homeChartOne = markRaw(echarts.init(homeLineRef.value, state.charts.theme));
	renderChart()
	// state.global.homeChartOne.setOption(lineChartOption);
	// state.myCharts.push(state.global.homeChartOne);
};

// 批量设置 echarts resize
const initEchartsResizeFun = () => {
	nextTick(() => {
		for (let i = 0; i < state.myCharts.length; i++) {
			setTimeout(() => {
				state.myCharts[i].resize();
			}, i * 1000);
		}
	});
};
// 批量设置 echarts resize
const initEchartsResize = () => {
	window.addEventListener('resize', initEchartsResizeFun);
};
const getSensorDatas = async () => { 
	const res= await getSensorData()
	// console.log(res);
	if(res.code===0){
		sensorData.value=res.data
		sensorDataCopy.value=res.data
		selectData.value.sensorType=sensorData.value[0].EnglishName
	}
}
const chartDatas=ref<any[]>([])
	const renderChart = () => {
    let chartData = chartDatas.value;

    // 获取所有时间点
    const allTimePoints = Array.from(new Set(chartData.flatMap(item => item.data.map((d:any) => d.timeInterval)))).sort();

    // 预处理数据，确保每个series在相同的时间点上都有数据
    const processedData = chartData.map(item => {
        const dataMap = new Map(item.data.map((d:any) => [d.timeInterval, d.sensorData[selectData.value.sensorType]]));
        return {
            samplePoint: item.samplePoint,
            data: allTimePoints.map(time => dataMap.has(time) ? dataMap.get(time) : undefined) // 明确区分0和缺失数据
        };
    });
	// console.log(processedData,'processedData');
	
    // 轻量级配置更新
    const option = {
        ...lineChartOption,
        title: { ...lineChartOption.title },
        yAxis: [...lineChartOption.yAxis],
        legend: { ...lineChartOption.legend },
        xAxis: { ...lineChartOption.xAxis, data: allTimePoints }, // 使用所有时间点
        series: [] as any[]
    };

    // 快速获取传感器和范围数据
    const selectedSensor = sensorData.value.find((item: any) => item.EnglishName === selectData.value.sensorType);
    if (selectedSensor) {
        option.title.text = `${selectedSensor.sensorName}检测`;
        option.yAxis[0].name = `单位：${selectedSensor.unit}`;
    }

    const currentRange = getCurrentSensorRange();
    const isInfinite = currentRange?.minValue === '无限值' && currentRange?.maxValue === '无限值';

    // 动态生成series
    option.series = processedData.map((item, index) => ({
        name: item.samplePoint,
        type: 'line',
        symbolSize: 6,
        symbol: 'circle',
        smooth: true,
        data: item.data,
        connectNulls: true, // 启用连接缺失数据点
        lineStyle: { color: lineChartOption.color[index % lineChartOption.color.length] },
        itemStyle: { color: lineChartOption.color[index % lineChartOption.color.length] },
        ...(!isInfinite && currentRange ? {
            markLine: {
                silent: true,
                symbol: 'none',
                lineStyle: { color: "#EE6666" },
                data: [
                    { yAxis: currentRange.minValue, name: '最小值' },
                    { yAxis: currentRange.maxValue, name: '最大值' }
                ]
            }
        } : {})
    }));

    // 按需设置visualMap
    option.visualMap = !isInfinite && currentRange ? {
        show: false,
        dimension: 1,
        pieces: [
            { lt: currentRange.minValue, color: '#EE6666' },
            { gte: currentRange.minValue, lte: currentRange.maxValue, color: '#21a675' },
            { gt: currentRange.maxValue, color: '#EE6666' }
        ],
        outOfRange: { color: '#EE6666' }
    } : undefined;

    // 更新数据
    option.legend.data = processedData.map(item => item.samplePoint);

    // 高效渲染
    if (!state.global.homeChartOne) {
        state.global.homeChartOne = markRaw(echarts.init(homeLineRef.value, state.charts.theme));
    }

    state.global.homeChartOne.setOption(option, { notMerge: true, lazyUpdate: true });
}
// 根据筛选条件获取全部数据
const getAllRobotDatas = async () => { 
    const res = await getAllRobotData(selectData.value);
    if (res.code === 0 && res.data.length > 0) {
        chartDatas.value = res.data; // 提取 data 数组
        renderChart(); // 调用 renderChart 函数渲染图表
    } else {
        ElMessage.warning('暂无数据！');
    }
};
const getCurrentSensorRange = () => {
    if (!selectData.value.robotPubilcPlace || !selectData.value.sensorType) return null;

    const selectedPlace = PlaceList.value.find((place: any) => place._id === selectData.value.robotPubilcPlace);
    if (!selectedPlace) return null;

    const selectedSensor = selectedPlace.sensorData.find((sensor: any) => 
        sensor.EnglishName === selectData.value.sensorType
    );
	
    return selectedSensor?.sensorPlaceAndRange[0].Range || null;
};
const getMeasuredPlacesByDeviceNames=async () => { 
	const res = await getMeasuredPlacesByDeviceName({
		deviceName: route.query.deviceName as string
	})
	PlaceList.value=res.data
}

// 页面加载时
onMounted(() => {
	initEchartsResize();
	getSensorDatas()
	getMeasuredPlacesByDeviceNames()
	getAllRobotDatas()

});

// 实时数据的时间戳转换为date格式字符串
// const socketParsedTime = computed(() => {
// 	const time = state.detectedData.msg.time
// 	if (time) {
// 		// console.log(timestampToTime(time));
// 		return timestampToTime(time)
// 	}
// 	else {
// 		return ''
// 	}
// })

// 由于页面缓存原因，keep-alive
onActivated(() => {
	// initEchartsResizeFun();
});

// 监听 pinia 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
watch(
	() => isTagsViewCurrenFull.value,
	() => {
		// initEchartsResizeFun();
	}
);
// 监听 pinia 中是否开启深色主题
watch(
	() => themeConfig.value.isIsDark,
	(isIsDark) => {
		nextTick(() => {
			state.charts.theme = isIsDark ? 'dark' : '';
			state.charts.bgColor = isIsDark ? 'transparent' : '';
			state.charts.color = isIsDark ? '#dadada' : '#303133';
			setTimeout(() => {
				initLineChart();
			}, 500);
		});
	},
	{
		deep: true,
		immediate: true,
	}
);
// 监听时间选择器是否更改
watch(timeSelect, (newValue) => {
	if (newValue) {
		// 字符串形式
		selectData.value.startTime = newValue[0].toString()
		selectData.value.endTime = newValue[1].toString()
		// 转为时间戳
		// parsedTime.startTime = Date.parse(newValue[0].toString())
		// parsedTime.endTime = Date.parse(newValue[1].toString())
	}
	else {
		selectData.value.startTime = ''
		selectData.value.endTime = ''
	}
	getAllRobotDatas()
})
</script>

<style scoped lang="scss">
$homeNavLengh: 8;

.home-container {
	overflow: hidden;

	.home-card-one,
	.home-card-two {
		.home-card-item {
			width: 100%;
			height: 150px;
			border-radius: 4px;
			transition: all ease 0.3s;
			padding: 20px;
			padding-bottom: 32px;
			overflow: hidden;
			background: var(--el-color-white);
			color: var(--el-text-color-primary);
			border: 1px solid var(--next-border-color-light);

			&:hover {
				box-shadow: 0 2px 12px var(--next-color-dark-hover);
				transition: all ease 0.3s;
			}

			&-icon {
				width: 70px;
				height: 70px;
				border-radius: 100%;
				flex-shrink: 1;

				i {
					color: var(--el-text-color-placeholder);
				}
			}

			&-title {
				font-size: 15px;
				font-weight: bold;
				height: 30px;
			}
		}
	}

	.home-card-two {
		.home-card-item {
			height: 580px;
			width: 100%;
			overflow: hidden;
			padding-bottom: 60px;

			.home-monitor {
				height: 100%;

				.flex-warp-item {
					width: 25%;
					height: 111px;
					display: flex;

					.flex-warp-item-box {
						margin: auto;
						text-align: center;
						color: var(--el-text-color-primary);
						display: flex;
						border-radius: 5px;
						background: var(--next-bg-color);
						cursor: pointer;
						transition: all 0.3s ease;

						&:hover {
							background: var(--el-color-primary-light-9);
							transition: all 0.3s ease;
						}
					}

					@for $i from 0 through $homeNavLengh {
						.home-animation#{$i} {
							opacity: 0;
							animation-name: error-num;
							animation-duration: 0.5s;
							animation-fill-mode: forwards;
							animation-delay: calc($i/10) + s;
						}
					}
				}
			}
		}
	}
}


.flex-warp {
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;
	margin: 0 -5px 6px 0;

	.flex-warp-item {
		padding: 5px;

		.flex-warp-item-box {
			width: 100%;
			height: 100%;
		}
	}
}
</style>