<?xml version="1.0" encoding="UTF-8"?>
<svg width="17.590074px" height="17.2003832px" viewBox="0 0 17.590074 17.2003832" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 36</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#7CFABC" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#7FFABD" offset="33.3998033%"></stop>
            <stop stop-color="#82FABD" offset="60.9566215%"></stop>
            <stop stop-color="#4AFFAF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#4EFAB1" offset="0%"></stop>
            <stop stop-color="#DFFBD3" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="徐汇区巡航监测“谛听”系统（公共场所）备份-6" transform="translate(-1626.6716, -438.2903)">
            <g id="编组-39备份" transform="translate(1713, 437.5) rotate(-66) translate(-1713, -437.5)translate(1340.2081, 143.8735)">
                <g id="编组-37备份-4" transform="translate(340.3995, 223.015) rotate(65) translate(-340.3995, -223.015)translate(323.8754, 209.9351)">
                    <g id="编组-36" transform="translate(11.1695, 17.0404)">
                        <rect id="矩形" fill="url(#linearGradient-1)" x="4.9739504" y="-2.66710014e-12" width="1" height="7.59695088"></rect>
                        <polygon id="路径-27" fill="url(#linearGradient-2)" points="8.13938664e-13 -8.91395437e-13 5.27437146 9.11947279 10.7091516 -8.91395437e-13 6.16504072 1.80934791 5.27437146 8.95086909 4.30652583 1.80934791"></polygon>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>