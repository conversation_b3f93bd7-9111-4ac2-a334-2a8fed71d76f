<template>
	<div class="system-place-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="placeDialogFormRef" :rules="rules" :model="state.dataFome" v-loading="state.loading"
				destroy-on-close label-width="auto">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="placeName" :label="$t('message.views.placeName')">
							<el-input v-model="state.dataFome.placeName"
								:placeholder="$t('message.views.placeholderPlaceName')" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="placeType" :label="$t('message.views.placeType')">
							<!-- <el-input v-model="state.dataFome.placeType" placeholder="请输入场所类型" clearable></el-input> -->
							<el-select v-model="state.dataFome.placeType"
								:placeholder="$t('message.views.placeholderPlaceType')" class="w100" clearable filterable>
								<el-option v-for="item in placeTypeList" :key="item" :label="item"
									:value="item"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="area_code" :label="$t('message.views.placeArea')" style="{ width: 100 %}">
							<Area ref="useAreaSelectorRef" :value="state.dataFome.area_code"
								@clear="() => { state.dataFome.area_code = [] }" style="{ width: 100 %}"></Area>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item prop="address" :label="$t('message.views.placeAddress')">
							<el-input v-model="state.dataFome.address"
								:placeholder="$t('message.views.placeholderPlaceAddress')" clearable></el-input>
						</el-form-item>
					</el-col>

				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel">{{ $t('message.common.cancel') }}</el-button>
					<el-button type="primary" @click="onSubmit(placeDialogFormRef)">
						{{ state.dialog.submitTxt }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="placeDialog">
import { reactive, ref, nextTick, onMounted, computed } from 'vue';
import { addPlace, updatePlaceInfo, getPlaceTypeList, getPlaceDetail } from '/@/api/publicPlace/index'
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus'
import Area from "/@/components/areaSelector/index.vue";
import { useI18n } from 'vue-i18n';
const { t } = useI18n()

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 组件ref
const useAreaSelectorRef = ref();
const placeDialogFormRef = ref();
// 校验规则
const rules = reactive({
	placeName: [{ required: true, message: computed(() => t('message.views.placeholderPlaceName')), trigger: 'change' }],
	area_code: [{ required: true, type: 'array', message: computed(() => t('message.views.rulesPlaceArea')), trigger: 'blur' }],
	address: [{ required: true, message: computed(() => t('message.views.rulesPlaceAddress')), trigger: 'change' }],
})
// 定义变量内容
const useState = () => reactive<placeDialogState>({
	dataFome: {
		_id: '',
		placeName: '',
		placeType: '',
		address: '',
		region: [],
		area_code: [],
		_type: 'public_place'
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
	loading: false,
	// 负责人关联
	headRelated: true,
});
const state = useState()
// 场所类型列表
const placeTypeList = reactive([])

// 获取场所类型列表
const getPlaceTypeListData = async () => {
	const res = await getPlaceTypeList()
	Object.assign(placeTypeList, res.data)
}
// 页面DOM挂载完后
onMounted(async () => {
	getPlaceTypeListData()
})

// 打开弹窗
const openDialog = async (type: string, row: rowPlaceType) => {
	state.dialog.isShowDialog = true;
	state.loading = true
	// 弹窗数据初始化
	if (type === 'update') {

		state.dialog.type = type
		state.dialog.title = t('message.views.placeModify');
		state.dialog.submitTxt = t('message.common.modify');
		// 获取详情
		const res = await getPlaceDetail({ _id: row._id })
		const area_code: string[] = []
		// 获取area_code
		res.data.region.length && res.data.region.forEach((item: regionItemType) => {
			area_code.push(item.area_code)
		})
		// 填入数据
		const { _id, placeName, placeType, address, region, } = res.data
		const data = {
			_id, placeName, placeType, address, region,
			area_code
		}

		state.dataFome = data

	} else {
		state.dialog.type = 'add';
		state.dialog.title = t('message.views.placeAdd');
		state.dialog.submitTxt = t('message.common.add');

		Object.assign(state.dataFome, useState().dataFome)
		// state.dataFome._id = '';
		// state.dataFome.placeName = '';
		// state.dataFome.placeType = '';
		// state.dataFome.address = '';
		// state.dataFome.region = [];
		// state.dataFome.area_code = [];
		// state.dataFome.head = {};
		// state.dataFome.engineeringHead = {};
	}
	state.loading = false
};
// 关闭弹窗
const closeDialog = () => {
	// 清空表单
	nextTick(() => {
		placeDialogFormRef.value.resetFields();
	});
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (formEl: FormInstance) => {
	formEl.validate(async (valid) => {
		if (!valid) {
			return
		}
		let { placeName, placeType, address, area_code, _type } = state.dataFome
		const data = {
			placeName, placeType, address, _type,
			region: area_code
		}
		try {
			if (state.dialog.type === 'add') {
				await addPlace(data)
				ElMessage.success(t('message.common.addSuccess'))
			}
			else {
				// 如果是修改，参数需要包含_id
				await updatePlaceInfo(Object.assign(data, { _id: state.dataFome._id }))
				ElMessage.success(t('message.common.modifySuccess'))
			}
		} catch (error) {
			ElMessage.error('操作失败！请检查输入')
			return
		}
		closeDialog();
		emit('refresh');
	})
};

// 点击负责人输入关联icon
// const onClickHeadIcon = () => {
// 	state.headRelated = !state.headRelated
// }

// 负责人关联输入
// watch(
// 	() => [
// 		state.dataFome.head.name, state.dataFome.head.phoneNum,
// 		state.dataFome.engineeringHead.name, state.dataFome.engineeringHead.phoneNum,
// 	],
// 	(newVal, oldVal) => {
// 		if (state.headRelated) {
// 			if (newVal[0] != oldVal[0]) {
// 				state.dataFome.engineeringHead.name = state.dataFome.head.name
// 			} else if (newVal[1] != oldVal[1]) {
// 				state.dataFome.engineeringHead.phoneNum = state.dataFome.head.phoneNum
// 			} else if (newVal[2] != oldVal[2]) {
// 				state.dataFome.head.name = state.dataFome.engineeringHead.name
// 			} else {
// 				state.dataFome.head.phoneNum = state.dataFome.engineeringHead.phoneNum
// 			}
// 		}
// 	}
// )

// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style lang="scss">
.inputIcon {
	color: #aaa;

	&:hover {
		color: var(--el-color-primary);
		cursor: pointer;
	}
}
</style>
