<template>
	<div class="login-container ">
		<div class="topBorder ">
			<img style="width:98%;" src="../../../src/assets/login/topBorder.png" />
		</div>

		<div class="title">
			<div class="title-firstLine">
				<div>
					<img class="firstLine-logo" src="../../../src/assets/dashBoard/logo.svg">
				</div>
				<div class="firstLine-text">徐汇区巡航监测“谛听”系统</div>
			</div>
			<div class="title-secondLine">(徐汇区巡航监测“SNN”系统)
			</div>
		</div>

		<div class="bottomBorder ">
			<img style="width:98%;height: auto;" src="../../../src/assets/login/bottomBorder.png" />
		</div>

		<div class="flash"> </div>
		<div class="map"> </div>


		<div class="login">
			<login></login>
		</div>

	</div>
</template>

<script setup lang="ts" name="loginIndex">
import { defineAsyncComponent, onMounted } from 'vue';
import { NextLoading } from '/@/utils/loading';

const login = defineAsyncComponent(() => import('/@/views/login/component/login.vue'));

// 页面加载时
onMounted(() => {
	NextLoading.done();
});
</script>

<style>
.login-container {

	@media screen and (max-width: 3840px) {
		font-size: 32px;
	}

	@media screen and (max-width: 3440px) {
		font-size: 24px;
	}

	@media screen and (max-width: 2560px) {
		font-size: 18px;
	}

	@media screen and (max-width: 1960px) {
		font-size: 14px;
	}

	@media screen and (max-width: 1440px) {
		font-size: 9px;
	}

	@media screen and (max-width: 1024px) {
		font-size: 7px;
	}
}
</style>

<style scoped lang="scss">
.login-container {
	width: 100%;
	height: 100%;
	background: url(../../../src/assets/login/compressedBigBackground.png);

	background-repeat: no-repeat;
	background-size: 100% 100%;

	position: relative;

	display: flex;
	flex-direction: column;
	justify-content: space-between;

	padding: 2vh 1vw;

	.topBorder {
		width: 100%;
		height: auto;
		display: flex;
		justify-content: space-around;
	}

	.bottomBorder {
		width: 100%;
		height: auto;
		display: flex;
		justify-content: space-around;
	}

	.title {
		width: 98%;
		height: auto;
		position: absolute;
		display: flex;
		flex-direction: column;
		top: 3vh;

		.title-firstLine {
			display: flex;
			justify-content: center;

			.firstLine-logo {
				width: 2.58em;
				height: 3.06em;
				margin-right: 0.5vw;
			}

			.firstLine-text {
				color: #FFFFFF;
				font-size: 1.98em;
				top: 4vh;
			}
		}

		.title-secondLine {
			display: flex;
			justify-content: center;
			color: #FFFFFF;
			font-size: 1.44em;
			top: 8vh;
		}
	}

	.map {
		height: 100vh;
		width: 63vw;
		background: url(../../../src/assets/login/map.png);
		background-repeat: no-repeat;
		background-size: auto 100%;
		background-position: center;
		position: absolute;
	}

	.flash {
		height: 100vh;
		width: 85vw;
		background: url(../../../src/assets/login/compressedFlash.png);
		background-repeat: no-repeat;
		background-size: 100% 100%;
		position: absolute;
		z-index: 9;
	}

	.login {
		width: 31vw;
		height: 54vh;
		background: url(../../../src/assets/login/loginBorder.png);
		background-size: 100% 100%;
		position: absolute;
		left: 54%;
		top: 25%;
	}

}
</style>
