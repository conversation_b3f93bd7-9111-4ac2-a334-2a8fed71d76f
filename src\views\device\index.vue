<template>
    <div class="system-device-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-tabs type="card" v-model="activeName"  >
                <el-tab-pane label="CO2" name="CO2">
                    <div class="system-device-search mb15">
                        <!-- 搜索关键字 -->
                        <el-input v-model="state.keyWord" :placeholder="$t('message.views.placeholderDeviceKeyword')"
                            style="max-width: 200px" clearable>
                        </el-input>
                        <!-- 安装场所选择 -->
                        <el-select v-model="state.publicPlaceId" :placeholder="$t('message.views.placeholderDevicePlcae')"
                            style="max-width: 200px" clearable filterable class="ml10">
                            <el-option v-for="item in placeList" :key="item._id" :label="item.placeName"
                                :value="item._id"></el-option>
                        </el-select>
                        <!-- 查询按钮 -->
                        <el-button type="primary" class="ml10" @click="getTableData()">
                            <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }}</el-button>
                        <!-- 新增设备 -->
                        <el-button type="success" class="ml10" @click="onOpenAddDevice('add')">
                            <el-icon><ele-FolderAdd /></el-icon>{{ $t('message.views.deviceAdd') }}</el-button>
                    </div>
                    <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%;height: 57vh;">
                        <!-- 序号 -->
                        <el-table-column type="index" :index="indexMethod" :label="$t('message.common.index')" width="66" />
                        <!-- 设备名 -->
                        <el-table-column prop="name" :label="$t('message.views.deviceName')"
                            show-overflow-tooltip></el-table-column>
                        <!-- 设备编码 -->
                        <el-table-column prop="deviceNum" :label="$t('message.views.deviceNum')"
                            show-overflow-tooltip></el-table-column>
                        <!-- 设备类型 -->
                        <el-table-column prop="type" :label="$t('message.views.deviceType')"></el-table-column>
                        <!-- 单位 -->
                        <el-table-column prop="unit" :label="$t('message.common.unit')"></el-table-column>
                        <!-- 阈值 -->
                        <el-table-column prop="threshold" :label="$t('message.common.threshold')"></el-table-column>
                        <!-- 安装场所 -->
                        <el-table-column prop="publicPlace.placeName" :label="$t('message.views.devicePlace')"
                            show-overflow-tooltip>
                            <template #default="scope">
                                <span>{{ scope.row.publicPlace && scope.row.publicPlace.placeName }}</span>
                            </template>
                        </el-table-column>
                        <!-- 安装点位 -->
                        <el-table-column prop="position" :label="$t('message.views.devicePosition')"
                            show-overflow-tooltip></el-table-column>
                        <!-- 设备状态 -->
                        <el-table-column prop="status" :label="$t('message.views.deviceStatus')" min-width="86">
                            <template #default="scope">
                                <el-tag type="success" v-if="scope.row.status == 1">{{ $t('message.common.enable') }}</el-tag>
                                <el-tag type="info" v-else>{{ $t('message.common.disable') }}</el-tag>
                            </template>
                        </el-table-column>
                        <!-- 操作 -->
                        <el-table-column :label="$t('message.common.operation')" width="120">
                            <template #default="scope">
                                <el-button size="small" text type="primary" @click="onOpenEditDevice('update', scope.row)">{{
                                    $t('message.common.modify') }}</el-button>
                                <el-button size="small" text type="primary" @click="onRowDel(scope.row)">{{
                                    $t('message.common.delete') }}</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 分页 -->
                    <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                        :pager-count="5" :page-sizes="[10, 20, 30]" v-model:current-page="state.tableData.param.pageNum" background
                        v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                        :total="state.tableData.total">
                    </el-pagination>
                </el-tab-pane>
                <el-tab-pane label="空气质量巡检" name="Robot">
                    <robordevice></robordevice>
                </el-tab-pane>
            </el-tabs>
            
        </el-card>
        <DeviceDialog ref="DeviceDialogRef" :placeList="placeList" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="deviceMana">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getDeviceList, deleteDevice } from "/@/api/device/index";
import { getPlaceAll } from "/@/api/publicPlace/index";
import robordevice from '../robotdevice/index.vue'
// 
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
const activeName = ref('Robot')
// 引入组件
const DeviceDialog = defineAsyncComponent(() => import('/@/views/device/dialog.vue'));
// 组件ref
const DeviceDialogRef = ref();
// 定义变量内容
const state = reactive<deviceState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 查询参数
    keyWord: '',
    publicPlaceId: '',
});
// 场所列表
const placeList = reactive<{ _id: string; placeName: string }[]>([])

// 获取表格数据
const getTableData = async (keyWord: string = state.keyWord, publicPlaceId: string = state.publicPlaceId) => {
    state.tableData.loading = true;
    const res = await getDeviceList({
        page: state.tableData.param.pageNum,
        pageSize: state.tableData.param.pageSize,
        keyWord,
        publicPlaceId
    })
    const data = [...res.data.list]
    state.tableData.data = data;
    state.tableData.total = res.data.count;
    state.tableData.loading = false;
};
// 加载场所列表
const getPlaceList = async () => {
    const res = await getPlaceAll({ _type: 'public_place' })
    Object.assign(placeList, res.data)
}

// 打开新增设备弹窗
const onOpenAddDevice = (type: string) => {
    DeviceDialogRef.value.openDialog(type);
};
// 打开修改设备弹窗
const onOpenEditDevice = (type: string, row: rowDeviceType) => {
    DeviceDialogRef.value.openDialog(type, row);
};

// 删除设备
const onRowDel = (row: rowDeviceType) => {
    ElMessageBox.confirm(t('message.views.deleteDeviceConfirm', { name: row.name }), t('message.common.prompt'), {
        confirmButtonText: t('message.common.confirm'),
        cancelButtonText: t('message.common.cancel'),
        type: 'warning',
    }).then(async () => {
        await deleteDevice(row._id)
    }).then(() => {
        ElMessage.success('删除成功！');
        getTableData();
    }).catch(() => {
    });
};

// 表格列行号
const indexMethod = (index: number) => {
    return (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + index + 1
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};

// 页面加载时
onMounted(() => {
    getTableData();
    getPlaceList()
});
</script>

<style scoped lang="scss">
.system-device-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-device-search {
    display: flex;
    align-content: space-around;
}
</style>
