<template>
    <div class="center-container">
        <TopBar></TopBar>
        <div class="center-main">
            <div class="center-main-aside ">
                <!-- <Flipper :size="20"/> -->
                <totalCount :totalCount="dashBoardStore.totalCount" />
                <RegionalRank title="消毒间报警次数排行" :yAxisData="dashBoardStore.xdjWarning.yAxisData"
                    :seriesData="dashBoardStore.xdjWarning.seriesData" />
                <RegionalRank title="消毒间监控类型统计" :yAxisData="dashBoardStore.xdjMonitor.yAxisData"
                    :seriesData="dashBoardStore.xdjMonitor.seriesData" />
            </div>
            <div class="center-main-center ">
                <div style="width: 100%;height: 60vh;">
                    <center-map :key="2" />
                </div>
                <div class="warningList">
                    <WarningList />
                </div>
            </div>
            <div class="center-main-aside">
                <RegionalRank title="空气巡航报警次数排行" :yAxisData="dashBoardStore.airWarning.yAxisData"
                    :seriesData="dashBoardStore.airWarning.seriesData" />
                <RegionalRank title="末稍水报警次数排行" :yAxisData="waterWarning.yAxisData" :seriesData="waterWarning.seriesData" />
                <RegionalRank title="CO₂监测点报警次数排行" :yAxisData="CO2Warning.yAxisData" :seriesData="CO2Warning.seriesData" />
            </div>

        </div>
    </div>
</template>

<script setup lang="ts" name="center">
import totalCount from './totalCount.vue';
import TopBar from './TopBar.vue';
import CenterMap from './CenterMap.vue';
import RegionalRank from "./RegionalRank.vue";
import WarningList from "./WarningList.vue"


import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '/@/stores/dashBoard';
const dashBoardStore = useDashBoardStore();
const { waterWarning, CO2Warning } = storeToRefs(dashBoardStore);
import { onMounted} from "vue";
// 末梢水数据不随年份选择改变，只获取一次
dashBoardStore.getWaterStatisticsData()

onMounted(() => {

})
</script>

<style scoped lang="scss">
.center-container {
    width: 100%;
    height: 100%;
}

.center-main {
    width: 100%;
    height: 90%;
    display: flex;
    justify-content: space-between;
    /* margin-top: -27px; */
    padding: 0 0.5vw;

    .center-main-aside {
        display: flex;
        height: 100%;
        flex-direction: column;

        position: relative;
        top: -1vh;
    }

    .center-main-center {
        width: 60%;
        height: 100%;
        display: flex;
        flex-direction: column;

        margin: 0 1vw;
    }

    .warningList {
        position: relative;
        top: -1vh;
    }
}
</style>