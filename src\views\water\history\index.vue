<template>
    <div class="system-water-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-form ref="useSearchFormRef" :model="state" :rules="rules" class="system-water-search">
                <el-row>
                    <el-form-item prop="zbhSelected" class="mr10 pb18" style="margin-bottom: 0;">
                        <el-select v-model="state.zbhSelected" :placeholder="$t('message.views.placeholderWaterSite')"
                            style="width: 300px;" clearable>
                            <el-option v-for="item in zbhList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="timeSelected" class="pb18">
                        <el-date-picker v-model="state.timeSelected" type="date"
                            :placeholder="$t('message.common.placeholderDate')" @change="timeChange"
                            :disabledDate="disabledDate" class="mr10" style="width: 210px;" />
                        <el-button type="primary" @click="search(useSearchFormRef)">
                            <el-icon><ele-Search /></el-icon>{{ $t('message.common.search') }} </el-button>
                    </el-form-item>
                </el-row>
            </el-form>

            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%">
                <el-table-column prop="zbh" :label="$t('message.views.waterSname')" min-width="270"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="time" :label="$t('message.views.receiveTime')" min-width="120"
                    show-overflow-tooltip></el-table-column>
                <el-table-column prop="pH" label="pH" min-width="120"></el-table-column>
                <el-table-column prop="浊度" :label="$t('message.views.turbidity')" min-width="120"></el-table-column>
                <el-table-column prop="电导率" :label="$t('message.views.conductivity')" min-width="120"></el-table-column>
                <el-table-column prop="水温" :label="$t('message.views.waterTemperature')"
                    min-width="120"></el-table-column>
                <el-table-column prop="总氯" :label="$t('message.views.totalChlorine')" min-width="120"></el-table-column>
            </el-table>

        </el-card>
    </div>
</template>

<script setup lang="ts" name="waterHistory">
import { reactive, ref, onMounted, computed } from 'vue'
import { getWaterHistoryList } from "/@/api/water/index"
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { tofixN, getToday } from "/@/utils/toolsValidate";
import { useI18n } from 'vue-i18n';
const { t } = useI18n()
// ref
const useSearchFormRef = ref()
// 搜索校验
const rules = ({
    zbhSelected: [{ required: true, message: computed(() => t('message.views.placeholderWaterSite')), trigger: 'change' }],
    timeSelected: [{ required: true, type: 'date', message: computed(() => t('message.common.placeholderDate')), trigger: 'change' }],
})
// 数据变量
const state = reactive<WaterHistoryState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
    // 所选站点编号
    zbhSelected: 'SHXHYYS10',
    // 所选日期
    timeSelected: undefined,
    datatime: '',
})
// 站点selectOptions
const zbhList = [
    {
        value: 'SHXHYYS10',
        label: '天平社区卫生服务中心(太原路110号)',
    },
    {
        value: 'SHPD08030YYS12',
        label: '斜土社区卫生服务中心(零陵北路2号)',
    },
    {
        value: 'SHXU0803YYS11',
        label: '田林社区卫生服务中心(柳州路509号)',
    },
    {
        value: 'SHXH0803YYS004',
        label: '华泾社区卫生服务中心(建华路180号)',
    },
    {
        value: 'SHXH0803YYS005',
        label: '虹梅社区卫生服务中心(虹梅路1501号)',
    },
]
// 站点编号对应的站点名称
const zname: { [key: string]: string } = {
    'SHXHYYS10': '天平社区卫生服务中心(太原路110号)',
    'SHPD08030YYS12': '斜土社区卫生服务中心(零陵北路2号)',
    'SHXU0803YYS11': '田林社区卫生服务中心(柳州路509号)',
    'SHXH0803YYS004': '华泾社区卫生服务中心(建华路180号)',
    'SHXH0803YYS005': '虹梅社区卫生服务中心(虹梅路1501号)'
}

// 获取表格数据
const getTableData = async () => {
    state.tableData.loading = true
    const res = await getWaterHistoryList({
        zbh: state.zbhSelected,
        datatime: <string>state.datatime
    }).catch(() => {
        ElMessage.error('查询失败！')
        state.tableData.loading = false
    })
    if (!res) { return }
    // 数据类型
    const dataType = res.data.dataType
    // 请求数据列表过滤
    const tableDate = res.data.list.filter((ele: { xmid: string; }) => ele.xmid)
    // 获取时间列表
    const lenList: number[] = []
    for (let i = 0; i < tableDate.length; i++) {
        lenList.push(Object.keys(tableDate[i].data).length)
    }
    let max = 0
    for (let i = 0; i < lenList.length; i++) {
        if (lenList[i] > lenList[max]) {
            max = i
        }
    }
    // 最长时间列表
    let timeList = Object.keys(tableDate[max].data)
    // 根据数据类型得到 按照 datatype数据类型 排序的数据对象
    const dataList = []
    for (let i = 0; i < dataType.length; i++) {
        for (let j = 0; j < tableDate.length; j++) {
            if (dataType[i] == tableDate[j].xmid) {
                dataList.push(tableDate[j].data)
            }
        }
    }
    const tableArray = []
    for (let i = 0; i < timeList.length; i++) {
        tableArray.push({
            zbh: zname[res.data.zbh as string],
            time: timeList[i],
            pH: tofixN(dataList[0][timeList[i]], 2),
            浊度: tofixN(dataList[1][timeList[i]], 3),
            电导率: tofixN(dataList[2][timeList[i]], 2),
            水温: tofixN(dataList[3][timeList[i]], 2),
            总氯: tofixN(dataList[4][timeList[i]], 2),
        })
    }
    state.tableData.data = tableArray
    state.tableData.total = tableArray.length
    state.tableData.loading = false
}

// 点击查询按钮，进行校验
const search = (formEl: FormInstance) => {
    formEl.validate((valid) => {
        if (!valid) {
            return ElMessage.warning('请选择正确的查询参数！')
        }
        getTableData()
    })
}

// 时间选择器修改日期选择
const timeChange = (): void => {
    if (state.timeSelected == null) {
        return
    }
    state.datatime = getToday(state.timeSelected)
}

// 不能选择今天之后的日期
const disabledDate = (time: Date) => {
    return time.getTime() > Date.now()
}

onMounted(() => {
    // 初始化
    state.timeSelected = new Date()
    state.datatime = getToday(state.timeSelected)
    getTableData()
})

</script>

<style scoped>
.system-water-container {
    :deep(.el-card__body) {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        .el-table {
            flex: 1;
        }
    }
}

.system-water-search {
    /* width: 100%; */
    display: flex;
    align-content: space-around;
}

.el-form-item--default {
    margin: auto 0;
}
</style>
